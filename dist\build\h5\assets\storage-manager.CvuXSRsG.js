import{G as t,R as s,J as e}from"./index-D2g-qjTN.js";var r=Object.defineProperty,o=(t,s,e)=>(((t,s,e)=>{s in t?r(t,s,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[s]=e})(t,"symbol"!=typeof s?s+"":s,e),e);class n{static getPosts(){try{const s=t(this.STORAGE_KEY);return s?JSON.parse(s):[]}catch(s){return console.error("读取帖子缓存失败:",s),[]}}static setPosts(t){try{s(this.STORAGE_KEY,JSON.stringify(t))}catch(e){console.error("保存帖子缓存失败:",e)}}static getPostById(t){return this.getPosts().find(s=>s.id===t)||null}static updatePost(t,s){const e=this.getPosts(),r=e.findIndex(s=>s.id===t);return-1!==r&&(e[r]={...e[r],...s},this.setPosts(e),!0)}static addPost(t){const s=this.getPosts();s.unshift(t),this.setPosts(s)}static removePost(t){const s=this.getPosts(),e=s.filter(s=>s.id!==t);return e.length!==s.length&&(this.setPosts(e),!0)}static clearPosts(){try{e(this.STORAGE_KEY)}catch(t){console.error("清空帖子缓存失败:",t)}}static getPostCount(){return this.getPosts().length}static hasPost(t){return null!==this.getPostById(t)}static hasNewContent(){try{const s=t(this.NEW_CONTENT_FLAG_KEY);return!0===s||"true"===s}catch(s){return console.error("检查新内容标志失败:",s),!1}}static setNewContentFlag(t=!0){try{s(this.NEW_CONTENT_FLAG_KEY,t)}catch(e){console.error("设置新内容标志失败:",e)}}static clearNewContentFlag(){try{e(this.NEW_CONTENT_FLAG_KEY)}catch(t){console.error("清除新内容标志失败:",t)}}}o(n,"STORAGE_KEY","posts"),o(n,"NEW_CONTENT_FLAG_KEY","posts_new_content_flag");class c{static setUser(t){try{s(this.USER_KEY,JSON.stringify(t))}catch(e){console.error("保存用户信息失败:",e)}}static getUser(){try{const s=t(this.USER_KEY);return s?JSON.parse(s):null}catch(s){return console.error("读取用户信息失败:",s),null}}static setToken(t){try{s(this.TOKEN_KEY,t)}catch(e){console.error("保存token失败:",e)}}static getToken(){try{return t(this.TOKEN_KEY)}catch(s){return console.error("读取token失败:",s),null}}static clearUserData(){try{e(this.USER_KEY),e(this.TOKEN_KEY)}catch(t){console.error("清除用户数据失败:",t)}}static isLoggedIn(){return!!this.getToken()&&!!this.getUser()}}o(c,"USER_KEY","current_user"),o(c,"TOKEN_KEY","auth_token");export{n as P,c as U};
