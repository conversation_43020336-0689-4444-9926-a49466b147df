import{c as s,w as a,i as e,o as t,a as c,e as l,f as o}from"./index-D2g-qjTN.js";import{_ as n}from"./_plugin-vue_export-helper.BCo6x5W8.js";const d=n({name:"DiscoverPage"},[["render",function(n,d,i,r,f,u){const _=l,m=e;return t(),s(m,{class:"discover-container"},{default:a(()=>[c(m,{class:"header"},{default:a(()=>[c(_,{class:"title"},{default:a(()=>[o("发现")]),_:1})]),_:1}),c(m,{class:"content"},{default:a(()=>[c(m,{class:"coming-soon"},{default:a(()=>[c(_,{class:"coming-text"},{default:a(()=>[o("🔍")]),_:1}),c(_,{class:"coming-title"},{default:a(()=>[o("发现页面")]),_:1}),c(_,{class:"coming-desc"},{default:a(()=>[o("敬请期待更多精彩内容")]),_:1})]),_:1})]),_:1})]),_:1})}],["__scopeId","data-v-7f7040b2"]]);export{d as default};
