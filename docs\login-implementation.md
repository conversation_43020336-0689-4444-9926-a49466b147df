# 登录功能实现文档

## 概述

Biu社交应用的登录功能基于自定义认证系统实现，不依赖Supabase Auth。整个系统使用Supabase作为后端数据库，通过自定义认证服务处理用户登录流程。

## 技术架构

```
登录页面 (pages/login/login.vue)
  ↓
认证存储 (stores/auth.ts)
  ↓
认证服务 (services/auth.ts)
  ↓
Supabase数据库
```

## 实现细节

### 1. 密码安全

- 使用SHA-256算法对用户密码进行加密存储
- 数据库中不存储明文密码，只存储加密后的哈希值

```typescript
const hashPassword = (password: string): string => {
  // 使用SHA-256加密密码
  return crypto.SHA256(password).toString()
}
```

### 2. 登录流程

1. 用户在登录页面输入手机号和密码
2. 页面调用 `authStore.login()` 方法
3. Store调用 `authService.login()` 服务方法
4. 服务层执行以下操作：
   - 验证输入参数
   - 根据手机号查询用户信息
   - 对比密码哈希值
   - 生成会话令牌
   - 创建用户会话记录
   - 返回用户信息和令牌

### 3. 会话管理

- 使用JWT风格的自定义令牌机制
- 令牌有效期为7天
- 令牌存储在本地存储中，应用启动时自动验证

```typescript
// 生成会话令牌
const generateSessionToken = (): string => {
  return crypto.lib.WordArray.random(32).toString()
}

// 会话有效期设置
const expiresAt = new Date()
expiresAt.setDate(expiresAt.getDate() + 7) // 7天后过期
```

### 4. Supabase数据库表结构

登录功能涉及以下数据库表：

#### users表
存储用户基本信息：
- id: 用户唯一标识
- phone: 手机号（唯一）
- password: 加密后的密码
- nickname: 用户昵称
- avatar_url: 头像URL
- gender: 性别
- bio: 个人简介
- created_at: 创建时间

#### user_sessions表
存储用户会话信息：
- user_id: 关联的用户ID
- token: 会话令牌
- expires_at: 过期时间

## 错误处理

系统对以下错误情况进行处理：
- 用户不存在
- 密码错误
- 数据库查询异常
- 网络连接问题

所有错误都会向用户显示友好的提示信息，同时在控制台记录详细日志便于调试。

## 安全特性

1. 密码加密存储
2. 会话令牌机制
3. 令牌过期处理
4. 输入验证和参数检查
5. SQL注入防护（通过Supabase SDK）
6. 错误信息不会暴露敏感信息

## 代码示例

### 登录服务方法
```typescript
export const login = async (credentials: LoginCredentials): Promise<ApiResponse<AuthData>> => {
  try {
    const supabase = getSupabaseInstance();
    const { phone, password } = credentials

    // 验证输入
    if (!phone || !password) {
      return { success: false, error: '手机号和密码不能为空' }
    }

    // 加密密码
    const hashedPassword = hashPassword(password)

    // 查找用户
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, phone, nickname, avatar_url, gender, bio, password')
      .eq('phone', phone)
      .single()

    if (userError) {
      if (userError.code === 'PGRST116') {
        return { success: false, error: '用户不存在' }
      }
      console.error('查询用户失败:', userError)
      return { success: false, error: '登录失败' }
    }

    // 验证密码
    if (user.password !== hashedPassword) {
      return { success: false, error: '密码错误' }
    }

    // 删除密码字段，不返回给前端
    delete user.password

    // 生成会话令牌
    const token = generateSessionToken()
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 7) // 7天后过期

    // 创建会话记录
    const { error: sessionError } = await supabase
      .from('user_sessions')
      .insert({
        user_id: user.id,
        token,
        expires_at: expiresAt.toISOString()
      })

    if (sessionError) {
      console.error('创建会话失败:', sessionError)
      return { success: false, error: '登录失败' }
    }

    return {
      success: true,
      data: {
        user: {
          id: user.id,
          phone: user.phone,
          nickname: user.nickname,
          gender: user.gender,
          avatar_url: user.avatar_url,
          bio: user.bio
        },
        token
      }
    }
  } catch (error: any) {
    console.error('登录异常:', error)
    return {
      success: false,
      error: error.message || '登录异常'
    }
  }
}
```