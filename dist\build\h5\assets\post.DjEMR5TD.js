import{K as e,a1 as r}from"./index-D2g-qjTN.js";import{g as s}from"./avatar.DQyLU1U7.js";const t=async(t=1,n=10,o)=>{try{const o=e(),a=r(o.token),c=(t-1)*n;let i=a.from("posts").select("\n        *,\n        users (\n          id,\n          nickname,\n          avatar_url\n        )\n      ").order("created_at",{ascending:!1}).range(c,c+n-1);const{data:u,error:l}=await i;if(l)throw console.error("获取帖子列表失败:",l),new Error("获取帖子列表失败: "+(l.message||"未知错误"));const d=u.map(e=>{var r,t,n;return{id:e.id,content:e.content,images:e.media||[],location:e.location,created_at:e.created_at,user_id:e.author_id,username:(null==(r=e.users)?void 0:r.nickname)||"未知用户",avatar_url:s(null==(t=e.users)?void 0:t.avatar_url,null==(n=e.users)?void 0:n.nickname),like_count:e.likes_count||0,comment_count:e.comments_count||0,share_count:e.shares_count||0,liked:!1}});return{posts:d,hasMore:u.length===n}}catch(a){throw console.error("获取帖子数据异常:",a),a}},n=async t=>{var n,o,a;try{const c=e(),i=r(c.token),u={content:t.content,media:t.images||[],location:t.location,created_at:(new Date).toISOString(),author_id:t.user_id},{data:l,error:d}=await i.from("posts").insert(u).select("\n        *,\n        users (\n          id,\n          nickname,\n          avatar_url\n        )\n      ").single();if(d)return console.error("发布帖子失败:",d),{success:!1,error:"发布帖子失败: "+d.message};return{success:!0,data:{id:l.id,content:l.content,images:l.media||[],location:l.location,created_at:l.created_at,user_id:l.author_id,username:(null==(n=l.users)?void 0:n.nickname)||"未知用户",avatar_url:s(null==(o=l.users)?void 0:o.avatar_url,null==(a=l.users)?void 0:a.nickname),like_count:l.likes_count||0,comment_count:l.comments_count||0,share_count:l.shares_count||0,liked:!1}}}catch(c){return console.error("发布帖子异常:",c),{success:!1,error:"发布帖子异常: "+(c.message||"未知错误")}}},o=async(s,t)=>{try{const n=e(),o=r(n.token),{data:a,error:c}=await o.from("post_likes").select("id").eq("post_id",s).eq("user_id",t).maybeSingle();if(c)return console.error("检查点赞状态失败:",c),{success:!1,error:"检查点赞状态失败"};if(a){const{error:e}=await o.from("post_likes").delete().eq("id",a.id);if(e)return console.error("取消点赞失败:",e),{success:!1,error:"取消点赞失败"}}else{const{error:e}=await o.from("post_likes").insert({post_id:s,user_id:t});if(e)return console.error("点赞失败:",e),{success:!1,error:"点赞失败"}}return{success:!0}}catch(n){return console.error("点赞操作异常:",n),{success:!1,error:"点赞操作异常"}}},a=async(e,r)=>await o(e,r);export{o as a,n as c,t as g,a as l};
