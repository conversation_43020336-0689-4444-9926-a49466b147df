import{c as e,w as t,C as a,Q as s,K as l,i as n,o as r,a as i,e as o,f as c,b as d,F as u,r as p,n as g,t as f,p as _,L as m,z as h}from"./index-D2g-qjTN.js";import{_ as b}from"./_plugin-vue_export-helper.BCo6x5W8.js";const k=b({name:"TestRegistrationPage",data:()=>({currentStep:0,steps:["手机号","密码","昵称","性别"],registrationData:{phone:"",password:"",nickname:"",gender:null}}),computed:{authStore:()=>l(),passwordLengthMet(){return this.registrationData.password.length>=6}},methods:{nextStep(){this.currentStep<this.steps.length-1&&this.currentStep++},prevStep(){this.currentStep>0&&this.currentStep--},async submitRegistration(){try{const e=await this.authStore.registerWithPhone(this.registrationData);e&&e.success?(a({title:"注册成功",icon:"success",duration:1500}),setTimeout(()=>{s({url:"/pages/index/index"})},1500)):a({title:(null==e?void 0:e.error)||"注册失败",icon:"none"})}catch(e){a({title:"注册失败，请重试",icon:"none"})}}}},[["render",function(a,s,l,b,k,S){const C=o,D=n,v=m,x=h;return r(),e(D,{class:"test-registration-container"},{default:t(()=>[i(D,{class:"header"},{default:t(()=>[i(C,{class:"title"},{default:t(()=>[c("测试注册流程")]),_:1})]),_:1}),i(D,{class:"content"},{default:t(()=>[i(D,{class:"step-indicator"},{default:t(()=>[(r(!0),d(u,null,p(k.steps,(a,s)=>(r(),e(D,{key:s,class:g(["step",{active:k.currentStep===s}])},{default:t(()=>[i(C,{class:"step-number"},{default:t(()=>[c(f(s+1),1)]),_:2},1024),i(C,{class:"step-name"},{default:t(()=>[c(f(a),1)]),_:2},1024)]),_:2},1032,["class"]))),128))]),_:1}),i(D,{class:"form-section"},{default:t(()=>[0===k.currentStep?(r(),e(D,{key:0,class:"step-content"},{default:t(()=>[i(D,{class:"input-group"},{default:t(()=>[i(C,{class:"label"},{default:t(()=>[c("手机号")]),_:1}),i(v,{modelValue:k.registrationData.phone,"onUpdate:modelValue":s[0]||(s[0]=e=>k.registrationData.phone=e),type:"text",placeholder:"请输入手机号",class:"form-input"},null,8,["modelValue"])]),_:1}),i(x,{class:"next-btn",disabled:!k.registrationData.phone.trim(),onClick:S.nextStep},{default:t(()=>[c(" 下一步 ")]),_:1},8,["disabled","onClick"])]),_:1})):_("",!0),1===k.currentStep?(r(),e(D,{key:1,class:"step-content"},{default:t(()=>[i(D,{class:"input-group"},{default:t(()=>[i(C,{class:"label"},{default:t(()=>[c("密码")]),_:1}),i(v,{modelValue:k.registrationData.password,"onUpdate:modelValue":s[1]||(s[1]=e=>k.registrationData.password=e),type:"password",placeholder:"请输入密码（至少6位）",class:"form-input"},null,8,["modelValue"])]),_:1}),i(D,{class:"password-requirements"},{default:t(()=>[i(C,{class:g(["requirement",{met:S.passwordLengthMet}])},{default:t(()=>[c(" • 至少6位字符 ")]),_:1},8,["class"])]),_:1}),i(D,{class:"button-group"},{default:t(()=>[i(x,{class:"prev-btn",onClick:S.prevStep},{default:t(()=>[c("上一步")]),_:1},8,["onClick"]),i(x,{class:"next-btn",disabled:!S.passwordLengthMet,onClick:S.nextStep},{default:t(()=>[c(" 下一步 ")]),_:1},8,["disabled","onClick"])]),_:1})]),_:1})):_("",!0),2===k.currentStep?(r(),e(D,{key:2,class:"step-content"},{default:t(()=>[i(D,{class:"input-group"},{default:t(()=>[i(C,{class:"label"},{default:t(()=>[c("昵称")]),_:1}),i(v,{modelValue:k.registrationData.nickname,"onUpdate:modelValue":s[2]||(s[2]=e=>k.registrationData.nickname=e),type:"text",placeholder:"请输入昵称",class:"form-input"},null,8,["modelValue"])]),_:1}),i(D,{class:"button-group"},{default:t(()=>[i(x,{class:"prev-btn",onClick:S.prevStep},{default:t(()=>[c("上一步")]),_:1},8,["onClick"]),i(x,{class:"next-btn",disabled:!k.registrationData.nickname.trim(),onClick:S.nextStep},{default:t(()=>[c(" 下一步 ")]),_:1},8,["disabled","onClick"])]),_:1})]),_:1})):_("",!0),3===k.currentStep?(r(),e(D,{key:3,class:"step-content"},{default:t(()=>[i(D,{class:"input-group"},{default:t(()=>[i(C,{class:"label"},{default:t(()=>[c("性别")]),_:1}),i(D,{class:"gender-options"},{default:t(()=>[i(D,{class:g(["gender-option",{active:1===k.registrationData.gender}]),onClick:s[3]||(s[3]=e=>k.registrationData.gender=1)},{default:t(()=>[i(C,null,{default:t(()=>[c("男")]),_:1})]),_:1},8,["class"]),i(D,{class:g(["gender-option",{active:2===k.registrationData.gender}]),onClick:s[4]||(s[4]=e=>k.registrationData.gender=2)},{default:t(()=>[i(C,null,{default:t(()=>[c("女")]),_:1})]),_:1},8,["class"]),i(D,{class:g(["gender-option",{active:0===k.registrationData.gender}]),onClick:s[5]||(s[5]=e=>k.registrationData.gender=0)},{default:t(()=>[i(C,null,{default:t(()=>[c("保密")]),_:1})]),_:1},8,["class"])]),_:1})]),_:1}),i(D,{class:"button-group"},{default:t(()=>[i(x,{class:"prev-btn",onClick:S.prevStep},{default:t(()=>[c("上一步")]),_:1},8,["onClick"]),i(x,{class:"submit-btn",disabled:null===k.registrationData.gender,onClick:S.submitRegistration},{default:t(()=>[c(" 完成注册 ")]),_:1},8,["disabled","onClick"])]),_:1})]),_:1})):_("",!0)]),_:1})]),_:1})]),_:1})}],["__scopeId","data-v-a8845362"]]);export{k as default};
