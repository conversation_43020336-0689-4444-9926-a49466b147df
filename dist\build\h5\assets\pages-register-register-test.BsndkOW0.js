import{c as a,w as e,N as l,C as t,Q as s,K as o,i as r,o as n,a as i,e as d,f as u,p as c,L as f,n as m,t as p,z as g}from"./index-D2g-qjTN.js";import{_}from"./_plugin-vue_export-helper.BCo6x5W8.js";const h=_({name:"RegisterTestPage",data:()=>({formData:{phone:"",password:"",nickname:"",gender:0},loading:!1,error:""}),computed:{authStore:()=>o(),canSubmit(){return this.formData.phone.trim()&&this.formData.password.trim()&&this.formData.nickname.trim()&&this.formData.password.length>=6}},methods:{async handleRegister(){if(this.canSubmit){this.loading=!0,this.error="";try{const a=await this.authStore.registerWithPhone(this.formData);a&&a.success?(t({title:"注册成功",icon:"success",duration:1500}),setTimeout(()=>{s({url:"/pages/index/index"})},1500)):this.error=(null==a?void 0:a.error)||"注册失败，请重试"}catch(a){this.error="注册失败，请重试"}finally{this.loading=!1}}else this.error="请填写完整的注册信息"},goToLogin(){l({url:"/pages/login/login"})}}},[["render",function(l,t,s,o,_,h){const D=d,k=r,b=f,y=g;return n(),a(k,{class:"register-test-container"},{default:e(()=>[i(k,{class:"header"},{default:e(()=>[i(D,{class:"title"},{default:e(()=>[u("注册流程测试")]),_:1})]),_:1}),i(k,{class:"form-container"},{default:e(()=>[i(k,{class:"input-group"},{default:e(()=>[i(D,{class:"label"},{default:e(()=>[u("手机号")]),_:1}),i(b,{modelValue:_.formData.phone,"onUpdate:modelValue":t[0]||(t[0]=a=>_.formData.phone=a),type:"text",placeholder:"请输入手机号",class:"form-input"},null,8,["modelValue"])]),_:1}),i(k,{class:"input-group"},{default:e(()=>[i(D,{class:"label"},{default:e(()=>[u("密码")]),_:1}),i(b,{modelValue:_.formData.password,"onUpdate:modelValue":t[1]||(t[1]=a=>_.formData.password=a),type:"password",placeholder:"请输入密码（至少6位）",class:"form-input"},null,8,["modelValue"])]),_:1}),i(k,{class:"input-group"},{default:e(()=>[i(D,{class:"label"},{default:e(()=>[u("昵称")]),_:1}),i(b,{modelValue:_.formData.nickname,"onUpdate:modelValue":t[2]||(t[2]=a=>_.formData.nickname=a),type:"text",placeholder:"请输入昵称",class:"form-input"},null,8,["modelValue"])]),_:1}),i(k,{class:"input-group"},{default:e(()=>[i(D,{class:"label"},{default:e(()=>[u("性别")]),_:1}),i(k,{class:"gender-options"},{default:e(()=>[i(k,{class:m(["gender-option",{active:1===_.formData.gender}]),onClick:t[3]||(t[3]=a=>_.formData.gender=1)},{default:e(()=>[i(D,null,{default:e(()=>[u("男")]),_:1})]),_:1},8,["class"]),i(k,{class:m(["gender-option",{active:2===_.formData.gender}]),onClick:t[4]||(t[4]=a=>_.formData.gender=2)},{default:e(()=>[i(D,null,{default:e(()=>[u("女")]),_:1})]),_:1},8,["class"]),i(k,{class:m(["gender-option",{active:0===_.formData.gender}]),onClick:t[5]||(t[5]=a=>_.formData.gender=0)},{default:e(()=>[i(D,null,{default:e(()=>[u("保密")]),_:1})]),_:1},8,["class"])]),_:1})]),_:1}),_.error?(n(),a(k,{key:0,class:"error-message"},{default:e(()=>[i(D,null,{default:e(()=>[u(p(_.error),1)]),_:1})]),_:1})):c("",!0),i(y,{class:"register-btn",disabled:_.loading||!h.canSubmit,onClick:h.handleRegister},{default:e(()=>[_.loading?(n(),a(D,{key:0},{default:e(()=>[u("注册中...")]),_:1})):(n(),a(D,{key:1},{default:e(()=>[u("注册")]),_:1}))]),_:1},8,["disabled","onClick"]),i(k,{class:"login-link"},{default:e(()=>[i(D,{class:"link-text"},{default:e(()=>[u("已有账号？")]),_:1}),i(D,{class:"link-btn",onClick:h.goToLogin},{default:e(()=>[u("立即登录")]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1})}],["__scopeId","data-v-6ee07bae"]]);export{h as default};
