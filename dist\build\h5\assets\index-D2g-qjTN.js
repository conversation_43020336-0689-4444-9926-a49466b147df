function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["assets/pages-index-index.CuqwsmnY.js","assets/post.DjEMR5TD.js","assets/avatar.DQyLU1U7.js","assets/storage-manager.CvuXSRsG.js","assets/auth-manager.Cl_11pDL.js","assets/time-utils.BUXFJoP1.js","assets/_plugin-vue_export-helper.BCo6x5W8.js","assets/index-5f8cf8f1.css","assets/pages-discover-discover.DQScCRVS.js","assets/discover-BAbcxIRv.css","assets/pages-profile-profile.jszcMxNU.js","assets/profile-B0zlu9LM.css","assets/pages-search-search.C0Cw1drz.js","assets/search-ZA-w_k8L.css","assets/pages-login-login.w9Ntw37g.js","assets/login-BGemQe0_.css","assets/pages-register-register.6BTAnS_k.js","assets/register-B12lXTLq.css","assets/pages-register-register-test.BsndkOW0.js","assets/register-test-D6-VjiOj.css","assets/pages-register-test-registration.DRcGnk2H.js","assets/test-registration-CkgWaQIB.css","assets/pages-create-post-create-post.B30wF2Qh.js","assets/create-post-Cs6qrsrj.css","assets/pages-message-message.BnmwPmuG.js","assets/message-CFb_x63J.css","assets/pages-post-detail-post-detail.O9Nffhl2.js","assets/post-detail-Ct9eErvy.css","assets/pages-avatar-test-avatar-test.D8ZBqktR.js","assets/avatar-test-CVKmORI0.css","assets/pages-debug-database-debug.kpuEfPJH.js","assets/database-debug-JBRub46O.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const e={},t=function(t,n,r){let s=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const t=document.querySelector("meta[property=csp-nonce]"),r=(null==t?void 0:t.nonce)||(null==t?void 0:t.getAttribute("nonce"));s=Promise.all(n.map(t=>{if((t=function(e){return"/"+e}(t))in e)return;e[t]=!0;const n=t.endsWith(".css"),s=n?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${t}"]${s}`))return;const o=document.createElement("link");return o.rel=n?"stylesheet":"modulepreload",n||(o.as="script",o.crossOrigin=""),o.href=t,r&&o.setAttribute("nonce",r),document.head.appendChild(o),n?new Promise((e,n)=>{o.addEventListener("load",e),o.addEventListener("error",()=>n(new Error(`Unable to preload CSS for ${t}`)))}):void 0}))}return s.then(()=>t()).catch(e=>{const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e})};
/**
* @vue/shared v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
function n(e,t){const n=new Set(e.split(","));return e=>n.has(e)}const r={},s=[],o=()=>{},i=()=>!1,a=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),l=e=>e.startsWith("onUpdate:"),c=Object.assign,u=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},h=Object.prototype.hasOwnProperty,d=(e,t)=>h.call(e,t),f=Array.isArray,p=e=>"[object Map]"===x(e),g=e=>"[object Set]"===x(e),v=e=>"function"==typeof e,m=e=>"string"==typeof e,y=e=>"symbol"==typeof e,_=e=>null!==e&&"object"==typeof e,b=e=>(_(e)||v(e))&&v(e.then)&&v(e.catch),w=Object.prototype.toString,x=e=>w.call(e),S=e=>"[object Object]"===x(e),k=e=>m(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,T=n(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),E=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},C=/-(\w)/g,O=E(e=>e.replace(C,(e,t)=>t?t.toUpperCase():"")),P=/\B([A-Z])/g,A=E(e=>e.replace(P,"-$1").toLowerCase()),$=E(e=>e.charAt(0).toUpperCase()+e.slice(1)),j=E(e=>e?`on${$(e)}`:""),B=(e,t)=>!Object.is(e,t),I=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},R=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},L=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let M;const D=()=>M||(M="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{});function N(e){if(f(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],s=m(r)?H(r):N(r);if(s)for(const e in s)t[e]=s[e]}return t}if(m(e)||_(e))return e}const F=/;(?![^(]*\))/g,U=/:([^]+)/,z=/\/\*[^]*?\*\//g;function H(e){const t={};return e.replace(z,"").split(F).forEach(e=>{if(e){const n=e.split(U);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function W(e){let t="";if(m(e))t=e;else if(f(e))for(let n=0;n<e.length;n++){const r=W(e[n]);r&&(t+=r+" ")}else if(_(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const q=n("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function V(e){return!!e||""===e}const X=e=>m(e)?e:null==e?"":f(e)||_(e)&&(e.toString===w||!v(e.toString))?JSON.stringify(e,K,2):String(e),K=(e,t)=>t&&t.__v_isRef?K(e,t.value):p(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((e,[t,n],r)=>(e[Y(t,r)+" =>"]=n,e),{})}:g(t)?{[`Set(${t.size})`]:[...t.values()].map(e=>Y(e))}:y(t)?Y(t):!_(t)||f(t)||S(t)?t:String(t),Y=(e,t="")=>{var n;return y(e)?`Symbol(${null!=(n=e.description)?n:t})`:e},J=["ad","ad-content-page","ad-draw","audio","button","camera","canvas","checkbox","checkbox-group","cover-image","cover-view","editor","form","functional-page-navigator","icon","image","input","label","live-player","live-pusher","map","movable-area","movable-view","navigator","official-account","open-data","picker","picker-view","picker-view-column","progress","radio","radio-group","rich-text","scroll-view","slider","swiper","swiper-item","switch","text","textarea","video","view","web-view","location-picker","location-view"].map(e=>"uni-"+e),G=["list-view","list-item","sticky-section","sticky-header","cloud-db-element"].map(e=>"uni-"+e),Z=["list-item"].map(e=>"uni-"+e);function Q(e){if(-1!==Z.indexOf(e))return!1;const t="uni-"+e.replace("v-uni-","");return-1!==J.indexOf(t)||-1!==G.indexOf(t)}const ee="\n",te=["%","%"],ne=/^([a-z-]+:)?\/\//i,re=/^data:.*,.*/,se="onShow",oe="onHide",ie="onLaunch",ae="onError",le="onThemeChange",ce="onPageNotFound",ue="onUnhandledRejection",he="onLoad",de="onUnload",fe="onInit",pe="onSaveExitState",ge="onResize",ve="onBackPress",me="onPageScroll",ye="onTabItemTap",_e="onReachBottom",be="onPullDownRefresh",we="onShareTimeline",xe="onAddToFavorites",Se="onShareAppMessage",ke="onNavigationBarButtonTap",Te="onNavigationBarSearchInputClicked",Ee="onNavigationBarSearchInputChanged",Ce="onNavigationBarSearchInputConfirmed",Oe="onNavigationBarSearchInputFocusChanged",Pe="onAppEnterForeground",Ae="onAppEnterBackground";function $e(e){if(!e)return;let t=e.type.name;for(;t&&Q(A(t));)t=(e=e.parent).type.name;return e.proxy}function je(e){return 1===e.nodeType}function Be(e){if(e instanceof Map){const t={};return e.forEach((e,n)=>{t[n]=e}),N(t)}if(m(e))return H(e);if(f(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],s=m(r)?H(r):Be(r);if(s)for(const e in s)t[e]=s[e]}return t}return N(e)}function Ie(e){let t="";if(e instanceof Map)e.forEach((e,n)=>{e&&(t+=n+" ")});else if(f(e))for(let n=0;n<e.length;n++){const r=Ie(e[n]);r&&(t+=r+" ")}else t=W(e);return t.trim()}function Re(e){return 0===e.indexOf("/")}function Le(e){return Re(e)?e:"/"+e}function Me(e,t=null){let n;return(...r)=>(e&&(n=e.apply(t,r),e=null),n)}function De(e){return O(e.substring(5))}const Ne=Me(()=>{const e=HTMLElement.prototype,t=e.setAttribute;e.setAttribute=function(e,n){if(e.startsWith("data-")&&this.tagName.startsWith("UNI-")){(this.__uniDataset||(this.__uniDataset={}))[De(e)]=n}t.call(this,e,n)};const n=e.removeAttribute;e.removeAttribute=function(e){this.__uniDataset&&e.startsWith("data-")&&this.tagName.startsWith("UNI-")&&delete this.__uniDataset[De(e)],n.call(this,e)}});function Fe(e){return c({},e.dataset,e.__uniDataset)}const Ue=new RegExp("\"[^\"]+\"|'[^']+'|url\\([^)]+\\)|(\\d*\\.?\\d+)[r|u]px","g");function ze(e){return{passive:e}}function He(e){const{id:t,offsetTop:n,offsetLeft:r}=e;return{id:t,dataset:Fe(e),offsetTop:n,offsetLeft:r}}function We(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function qe(e={}){const t={};return Object.keys(e).forEach(n=>{try{t[n]=We(e[n])}catch(r){t[n]=e[n]}}),t}const Ve=/\+/g;function Xe(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let r=0;r<n.length;++r){const e=n[r].replace(Ve," ");let s=e.indexOf("="),o=We(s<0?e:e.slice(0,s)),i=s<0?null:We(e.slice(s+1));if(o in t){let e=t[o];f(e)||(e=t[o]=[e]),e.push(i)}else t[o]=i}return t}function Ke(e,t,{clearTimeout:n,setTimeout:r}){let s;const o=function(){n(s);s=r(()=>e.apply(this,arguments),t)};return o.cancel=function(){n(s)},o}class Ye{constructor(e,t){this.id=e,this.listener={},this.emitCache=[],t&&Object.keys(t).forEach(e=>{this.on(e,t[e])})}emit(e,...t){const n=this.listener[e];if(!n)return this.emitCache.push({eventName:e,args:t});n.forEach(e=>{e.fn.apply(e.fn,t)}),this.listener[e]=n.filter(e=>"once"!==e.type)}on(e,t){this._addListener(e,"on",t),this._clearCache(e)}once(e,t){this._addListener(e,"once",t),this._clearCache(e)}off(e,t){const n=this.listener[e];if(n)if(t)for(let r=0;r<n.length;)n[r].fn===t&&(n.splice(r,1),r--),r++;else delete this.listener[e]}_clearCache(e){for(let t=0;t<this.emitCache.length;t++){const n=this.emitCache[t],r=e?n.eventName===e?e:null:n.eventName;if(!r)continue;"number"!=typeof this.emit.apply(this,[r,...n.args])?(this.emitCache.splice(t,1),t--):this.emitCache.pop()}}_addListener(e,t,n){(this.listener[e]||(this.listener[e]=[])).push({fn:n,type:t})}}const Je=[fe,he,se,oe,de,ve,me,ye,_e,be,we,Se,xe,pe,ke,Te,Ee,Ce,Oe];const Ge=[se,oe,ie,ae,le,ce,ue,"onExit",fe,he,"onReady",de,ge,ve,me,ye,_e,be,we,xe,Se,pe,ke,Te,Ee,Ce,Oe];const Ze=[];const Qe=Me((e,t)=>{if(v(e._component.onError))return t(e)}),et=function(){};et.prototype={on:function(e,t,n){var r=this.e||(this.e={});return(r[e]||(r[e]=[])).push({fn:t,ctx:n}),this},once:function(e,t,n){var r=this;function s(){r.off(e,s),t.apply(n,arguments)}return s._=t,this.on(e,s,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),r=0,s=n.length;r<s;r++)n[r].fn.apply(n[r].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),r=n[e],s=[];if(r&&t){for(var o=r.length-1;o>=0;o--)if(r[o].fn===t||r[o].fn._===t){r.splice(o,1);break}s=r}return s.length?n[e]=s:delete n[e],this}};var tt=et;const nt={black:"rgba(0,0,0,0.4)",white:"rgba(255,255,255,0.4)"};function rt(e,t,n){if(m(t)&&t.startsWith("@")){let s=e[t.replace("@","")]||t;switch(n){case"titleColor":s="black"===s?"#000000":"#ffffff";break;case"borderStyle":s=(r=s)&&r in nt?nt[r]:r}return s}var r;return t}function st(e,t={},n="light"){const r=t[n],s={};return void 0!==r&&e?(Object.keys(e).forEach(o=>{const i=e[o];s[o]=S(i)?st(i,t,n):f(i)?i.map(e=>"object"==typeof e?st(e,t,n):rt(r,e)):rt(r,i,o)}),s):e}
/**
* @dcloudio/uni-h5-vue v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ot,it;class at{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=ot,!e&&ot&&(this.index=(ot.scopes||(ot.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=ot;try{return ot=this,e()}finally{ot=t}}}on(){ot=this}off(){ot=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function lt(e){return new at(e)}function ct(){return ot}class ut{constructor(e,t,n,r){this.fn=e,this.trigger=t,this.scheduler=n,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,function(e,t=ot){t&&t.active&&t.effects.push(e)}(this,r)}get dirty(){if(2===this._dirtyLevel||3===this._dirtyLevel){this._dirtyLevel=1,yt();for(let e=0;e<this._depsLength;e++){const t=this.deps[e];if(t.computed&&(ht(t.computed),this._dirtyLevel>=4))break}1===this._dirtyLevel&&(this._dirtyLevel=0),_t()}return this._dirtyLevel>=4}set dirty(e){this._dirtyLevel=e?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let e=gt,t=it;try{return gt=!0,it=this,this._runnings++,dt(this),this.fn()}finally{ft(this),this._runnings--,it=t,gt=e}}stop(){var e;this.active&&(dt(this),ft(this),null==(e=this.onStop)||e.call(this),this.active=!1)}}function ht(e){return e.value}function dt(e){e._trackId++,e._depsLength=0}function ft(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)pt(e.deps[t],e);e.deps.length=e._depsLength}}function pt(e,t){const n=e.get(t);void 0!==n&&t._trackId!==n&&(e.delete(t),0===e.size&&e.cleanup())}let gt=!0,vt=0;const mt=[];function yt(){mt.push(gt),gt=!1}function _t(){const e=mt.pop();gt=void 0===e||e}function bt(){vt++}function wt(){for(vt--;!vt&&St.length;)St.shift()()}function xt(e,t,n){if(t.get(e)!==e._trackId){t.set(e,e._trackId);const n=e.deps[e._depsLength];n!==t?(n&&pt(n,e),e.deps[e._depsLength++]=t):e._depsLength++}}const St=[];function kt(e,t,n){bt();for(const r of e.keys()){let n;r._dirtyLevel<t&&(null!=n?n:n=e.get(r)===r._trackId)&&(r._shouldSchedule||(r._shouldSchedule=0===r._dirtyLevel),r._dirtyLevel=t),r._shouldSchedule&&(null!=n?n:n=e.get(r)===r._trackId)&&(r.trigger(),r._runnings&&!r.allowRecurse||2===r._dirtyLevel||(r._shouldSchedule=!1,r.scheduler&&St.push(r.scheduler)))}wt()}const Tt=(e,t)=>{const n=new Map;return n.cleanup=e,n.computed=t,n},Et=new WeakMap,Ct=Symbol(""),Ot=Symbol("");function Pt(e,t,n){if(gt&&it){let t=Et.get(e);t||Et.set(e,t=new Map);let r=t.get(n);r||t.set(n,r=Tt(()=>t.delete(n))),xt(it,r)}}function At(e,t,n,r,s,o){const i=Et.get(e);if(!i)return;let a=[];if("clear"===t)a=[...i.values()];else if("length"===n&&f(e)){const e=Number(r);i.forEach((t,n)=>{("length"===n||!y(n)&&n>=e)&&a.push(t)})}else switch(void 0!==n&&a.push(i.get(n)),t){case"add":f(e)?k(n)&&a.push(i.get("length")):(a.push(i.get(Ct)),p(e)&&a.push(i.get(Ot)));break;case"delete":f(e)||(a.push(i.get(Ct)),p(e)&&a.push(i.get(Ot)));break;case"set":p(e)&&a.push(i.get(Ct))}bt();for(const l of a)l&&kt(l,4);wt()}const $t=n("__proto__,__v_isRef,__isVue"),jt=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>"arguments"!==e&&"caller"!==e).map(e=>Symbol[e]).filter(y)),Bt=It();function It(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...e){const n=Sn(this);for(let t=0,s=this.length;t<s;t++)Pt(n,0,t+"");const r=n[t](...e);return-1===r||!1===r?n[t](...e.map(Sn)):r}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...e){yt(),bt();const n=Sn(this)[t].apply(this,e);return wt(),_t(),n}}),e}function Rt(e){const t=Sn(this);return Pt(t,0,e),t.hasOwnProperty(e)}class Lt{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){const r=this._isReadonly,s=this._isShallow;if("__v_isReactive"===t)return!r;if("__v_isReadonly"===t)return r;if("__v_isShallow"===t)return s;if("__v_raw"===t)return n===(r?s?fn:dn:s?hn:un).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const o=f(e);if(!r){if(o&&d(Bt,t))return Reflect.get(Bt,t,n);if("hasOwnProperty"===t)return Rt}const i=Reflect.get(e,t,n);return(y(t)?jt.has(t):$t(t))?i:(r||Pt(e,0,t),s?i:An(i)?o&&k(t)?i:i.value:_(i)?r?mn(i):gn(i):i)}}class Mt extends Lt{constructor(e=!1){super(!1,e)}set(e,t,n,r){let s=e[t];if(!this._isShallow){const t=bn(s);if(wn(n)||bn(n)||(s=Sn(s),n=Sn(n)),!f(e)&&An(s)&&!An(n))return!t&&(s.value=n,!0)}const o=f(e)&&k(t)?Number(t)<e.length:d(e,t),i=Reflect.set(e,t,n,r);return e===Sn(r)&&(o?B(n,s)&&At(e,"set",t,n):At(e,"add",t,n)),i}deleteProperty(e,t){const n=d(e,t);e[t];const r=Reflect.deleteProperty(e,t);return r&&n&&At(e,"delete",t,void 0),r}has(e,t){const n=Reflect.has(e,t);return y(t)&&jt.has(t)||Pt(e,0,t),n}ownKeys(e){return Pt(e,0,f(e)?"length":Ct),Reflect.ownKeys(e)}}class Dt extends Lt{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const Nt=new Mt,Ft=new Dt,Ut=new Mt(!0),zt=e=>e,Ht=e=>Reflect.getPrototypeOf(e);function Wt(e,t,n=!1,r=!1){const s=Sn(e=e.__v_raw),o=Sn(t);n||(B(t,o)&&Pt(s,0,t),Pt(s,0,o));const{has:i}=Ht(s),a=r?zt:n?En:Tn;return i.call(s,t)?a(e.get(t)):i.call(s,o)?a(e.get(o)):void(e!==s&&e.get(t))}function qt(e,t=!1){const n=this.__v_raw,r=Sn(n),s=Sn(e);return t||(B(e,s)&&Pt(r,0,e),Pt(r,0,s)),e===s?n.has(e):n.has(e)||n.has(s)}function Vt(e,t=!1){return e=e.__v_raw,!t&&Pt(Sn(e),0,Ct),Reflect.get(e,"size",e)}function Xt(e){e=Sn(e);const t=Sn(this);return Ht(t).has.call(t,e)||(t.add(e),At(t,"add",e,e)),this}function Kt(e,t){t=Sn(t);const n=Sn(this),{has:r,get:s}=Ht(n);let o=r.call(n,e);o||(e=Sn(e),o=r.call(n,e));const i=s.call(n,e);return n.set(e,t),o?B(t,i)&&At(n,"set",e,t):At(n,"add",e,t),this}function Yt(e){const t=Sn(this),{has:n,get:r}=Ht(t);let s=n.call(t,e);s||(e=Sn(e),s=n.call(t,e)),r&&r.call(t,e);const o=t.delete(e);return s&&At(t,"delete",e,void 0),o}function Jt(){const e=Sn(this),t=0!==e.size,n=e.clear();return t&&At(e,"clear",void 0,void 0),n}function Gt(e,t){return function(n,r){const s=this,o=s.__v_raw,i=Sn(o),a=t?zt:e?En:Tn;return!e&&Pt(i,0,Ct),o.forEach((e,t)=>n.call(r,a(e),a(t),s))}}function Zt(e,t,n){return function(...r){const s=this.__v_raw,o=Sn(s),i=p(o),a="entries"===e||e===Symbol.iterator&&i,l="keys"===e&&i,c=s[e](...r),u=n?zt:t?En:Tn;return!t&&Pt(o,0,l?Ot:Ct),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:a?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function Qt(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function en(){const e={get(e){return Wt(this,e)},get size(){return Vt(this)},has:qt,add:Xt,set:Kt,delete:Yt,clear:Jt,forEach:Gt(!1,!1)},t={get(e){return Wt(this,e,!1,!0)},get size(){return Vt(this)},has:qt,add:Xt,set:Kt,delete:Yt,clear:Jt,forEach:Gt(!1,!0)},n={get(e){return Wt(this,e,!0)},get size(){return Vt(this,!0)},has(e){return qt.call(this,e,!0)},add:Qt("add"),set:Qt("set"),delete:Qt("delete"),clear:Qt("clear"),forEach:Gt(!0,!1)},r={get(e){return Wt(this,e,!0,!0)},get size(){return Vt(this,!0)},has(e){return qt.call(this,e,!0)},add:Qt("add"),set:Qt("set"),delete:Qt("delete"),clear:Qt("clear"),forEach:Gt(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(s=>{e[s]=Zt(s,!1,!1),n[s]=Zt(s,!0,!1),t[s]=Zt(s,!1,!0),r[s]=Zt(s,!0,!0)}),[e,n,t,r]}const[tn,nn,rn,sn]=en();function on(e,t){const n=t?e?sn:rn:e?nn:tn;return(t,r,s)=>"__v_isReactive"===r?!e:"__v_isReadonly"===r?e:"__v_raw"===r?t:Reflect.get(d(n,r)&&r in t?n:t,r,s)}const an={get:on(!1,!1)},ln={get:on(!1,!0)},cn={get:on(!0,!1)},un=new WeakMap,hn=new WeakMap,dn=new WeakMap,fn=new WeakMap;function pn(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>x(e).slice(8,-1))(e))}function gn(e){return bn(e)?e:yn(e,!1,Nt,an,un)}function vn(e){return yn(e,!1,Ut,ln,hn)}function mn(e){return yn(e,!0,Ft,cn,dn)}function yn(e,t,n,r,s){if(!_(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const o=s.get(e);if(o)return o;const i=pn(e);if(0===i)return e;const a=new Proxy(e,2===i?r:n);return s.set(e,a),a}function _n(e){return bn(e)?_n(e.__v_raw):!(!e||!e.__v_isReactive)}function bn(e){return!(!e||!e.__v_isReadonly)}function wn(e){return!(!e||!e.__v_isShallow)}function xn(e){return _n(e)||bn(e)}function Sn(e){const t=e&&e.__v_raw;return t?Sn(t):e}function kn(e){return Object.isExtensible(e)&&R(e,"__v_skip",!0),e}const Tn=e=>_(e)?gn(e):e,En=e=>_(e)?mn(e):e;class Cn{constructor(e,t,n,r){this.getter=e,this._setter=t,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new ut(()=>e(this._value),()=>Pn(this,2===this.effect._dirtyLevel?2:3)),this.effect.computed=this,this.effect.active=this._cacheable=!r,this.__v_isReadonly=n}get value(){const e=Sn(this);return e._cacheable&&!e.effect.dirty||!B(e._value,e._value=e.effect.run())||Pn(e,4),On(e),e.effect._dirtyLevel>=2&&Pn(e,2),e._value}set value(e){this._setter(e)}get _dirty(){return this.effect.dirty}set _dirty(e){this.effect.dirty=e}}function On(e){var t;gt&&it&&(e=Sn(e),xt(it,null!=(t=e.dep)?t:e.dep=Tt(()=>e.dep=void 0,e instanceof Cn?e:void 0)))}function Pn(e,t=4,n){const r=(e=Sn(e)).dep;r&&kt(r,t)}function An(e){return!(!e||!0!==e.__v_isRef)}function $n(e){return Bn(e,!1)}function jn(e){return Bn(e,!0)}function Bn(e,t){return An(e)?e:new In(e,t)}class In{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:Sn(e),this._value=t?e:Tn(e)}get value(){return On(this),this._value}set value(e){const t=this.__v_isShallow||wn(e)||bn(e);e=t?e:Sn(e),B(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:Tn(e),Pn(this,4))}}function Rn(e){return An(e)?e.value:e}const Ln={get:(e,t,n)=>Rn(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const s=e[t];return An(s)&&!An(n)?(s.value=n,!0):Reflect.set(e,t,n,r)}};function Mn(e){return _n(e)?e:new Proxy(e,Ln)}class Dn{constructor(e,t,n){this._object=e,this._key=t,this._defaultValue=n,this.__v_isRef=!0}get value(){const e=this._object[this._key];return void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return e=Sn(this._object),t=this._key,null==(n=Et.get(e))?void 0:n.get(t);var e,t,n}}function Nn(e,t,n){const r=e[t];return An(r)?r:new Dn(e,t,n)}function Fn(e,t,n,r){try{return r?e(...r):e()}catch(s){zn(s,t,n)}}function Un(e,t,n,r){if(v(e)){const s=Fn(e,t,n,r);return s&&b(s)&&s.catch(e=>{zn(e,t,n)}),s}const s=[];for(let o=0;o<e.length;o++)s.push(Un(e[o],t,n,r));return s}function zn(e,t,n,r=!0){t&&t.vnode;if(t){let r=t.parent;const s=t.proxy,o=`https://vuejs.org/error-reference/#runtime-${n}`;for(;r;){const t=r.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,s,o))return;r=r.parent}const i=t.appContext.config.errorHandler;if(i)return void Fn(i,null,10,[e,s,o])}!function(e){console.error(e)}(e,0,0,r)}let Hn=!1,Wn=!1;const qn=[];let Vn=0;const Xn=[];let Kn=null,Yn=0;const Jn=Promise.resolve();let Gn=null;function Zn(e){const t=Gn||Jn;return e?t.then(this?e.bind(this):e):t}function Qn(e){qn.length&&qn.includes(e,Hn&&e.allowRecurse?Vn+1:Vn)||(null==e.id?qn.push(e):qn.splice(function(e){let t=Vn+1,n=qn.length;for(;t<n;){const r=t+n>>>1,s=qn[r],o=rr(s);o<e||o===e&&s.pre?t=r+1:n=r}return t}(e.id),0,e),er())}function er(){Hn||Wn||(Wn=!0,Gn=Jn.then(or))}function tr(e,t,n=(Hn?Vn+1:0)){for(;n<qn.length;n++){const t=qn[n];if(t&&t.pre){if(e&&t.id!==e.uid)continue;qn.splice(n,1),n--,t()}}}function nr(e){if(Xn.length){const e=[...new Set(Xn)].sort((e,t)=>rr(e)-rr(t));if(Xn.length=0,Kn)return void Kn.push(...e);for(Kn=e,Yn=0;Yn<Kn.length;Yn++)Kn[Yn]();Kn=null,Yn=0}}const rr=e=>null==e.id?1/0:e.id,sr=(e,t)=>{const n=rr(e)-rr(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function or(e){Wn=!1,Hn=!0,qn.sort(sr);try{for(Vn=0;Vn<qn.length;Vn++){const e=qn[Vn];e&&!1!==e.active&&Fn(e,null,14)}}finally{Vn=0,qn.length=0,nr(),Hn=!1,Gn=null,(qn.length||Xn.length)&&or()}}function ir(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||r;let o=n;const i=t.startsWith("update:"),a=i&&t.slice(7);if(a&&a in s){const e=`${"modelValue"===a?"model":a}Modifiers`,{number:t,trim:i}=s[e]||r;i&&(o=n.map(e=>m(e)?e.trim():e)),t&&(o=n.map(L))}let l,c=s[l=j(t)]||s[l=j(O(t))];!c&&i&&(c=s[l=j(A(t))]),c&&Un(c,e,6,ar(e,c,o));const u=s[l+"Once"];if(u){if(e.emitted){if(e.emitted[l])return}else e.emitted={};e.emitted[l]=!0,Un(u,e,6,ar(e,u,o))}}function ar(e,t,n){if(1!==n.length)return n;if(v(t)){if(t.length<2)return n}else if(!t.find(e=>e.length>=2))return n;const r=n[0];if(r&&d(r,"type")&&d(r,"timeStamp")&&d(r,"target")&&d(r,"currentTarget")&&d(r,"detail")){const t=e.proxy,r=t.$gcd(t,!0);r&&n.push(r)}return n}function lr(e,t,n=!1){const r=t.emitsCache,s=r.get(e);if(void 0!==s)return s;const o=e.emits;let i={},a=!1;if(!v(e)){const r=e=>{const n=lr(e,t,!0);n&&(a=!0,c(i,n))};!n&&t.mixins.length&&t.mixins.forEach(r),e.extends&&r(e.extends),e.mixins&&e.mixins.forEach(r)}return o||a?(f(o)?o.forEach(e=>i[e]=null):c(i,o),_(e)&&r.set(e,i),i):(_(e)&&r.set(e,null),null)}function cr(e,t){return!(!e||!a(t))&&(t=t.slice(2).replace(/Once$/,""),d(e,t[0].toLowerCase()+t.slice(1))||d(e,A(t))||d(e,t))}let ur=null,hr=null;function dr(e){const t=ur;return ur=e,hr=e&&e.type.__scopeId||null,t}function fr(e,t=ur,n){if(!t)return e;if(e._n)return e;const r=(...n)=>{r._d&&wo(-1);const s=dr(t);let o;try{o=e(...n)}finally{dr(s),r._d&&wo(1)}return o};return r._n=!0,r._c=!0,r._d=!0,r}function pr(e){const{type:t,vnode:n,proxy:r,withProxy:s,props:o,propsOptions:[i],slots:a,attrs:c,emit:u,render:h,renderCache:d,data:f,setupState:p,ctx:g,inheritAttrs:v}=e;let m,y;const _=dr(e);try{if(4&n.shapeFlag){const e=s||r,t=e;m=Ro(h.call(t,e,d,o,p,f,g)),y=c}else{const e=t;0,m=Ro(e.length>1?e(o,{attrs:c,slots:a,emit:u}):e(o,null)),y=t.props?c:gr(c)}}catch(w){mo.length=0,zn(w,e,1),m=$o(go)}let b=m;if(y&&!1!==v){const e=Object.keys(y),{shapeFlag:t}=b;e.length&&7&t&&(i&&e.some(l)&&(y=vr(y,i)),b=jo(b,y))}return n.dirs&&(b=jo(b),b.dirs=b.dirs?b.dirs.concat(n.dirs):n.dirs),n.transition&&(b.transition=n.transition),m=b,dr(_),m}const gr=e=>{let t;for(const n in e)("class"===n||"style"===n||a(n))&&((t||(t={}))[n]=e[n]);return t},vr=(e,t)=>{const n={};for(const r in e)l(r)&&r.slice(9)in t||(n[r]=e[r]);return n};function mr(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let s=0;s<r.length;s++){const o=r[s];if(t[o]!==e[o]&&!cr(n,o))return!0}return!1}const yr="components";function _r(e,t){return xr(yr,e,!0,t)||e}const br=Symbol.for("v-ndc");function wr(e){return m(e)?xr(yr,e,!1)||e:e||br}function xr(e,t,n=!0,r=!1){const s=ur||zo;if(s){const n=s.type;{const e=Qo(n,!1);if(e&&(e===t||e===O(t)||e===$(O(t))))return n}const o=Sr(s[e]||n[e],t)||Sr(s.appContext[e],t);return!o&&r?n:o}}function Sr(e,t){return e&&(e[t]||e[O(t)]||e[$(O(t))])}const kr=e=>e.__isSuspense;const Tr=Symbol.for("v-scx");function Er(e,t){return Pr(e,null,t)}const Cr={};function Or(e,t,n){return Pr(e,t,n)}function Pr(e,t,{immediate:n,deep:s,flush:i,once:a,onTrack:l,onTrigger:c}=r){if(t&&a){const e=t;t=(...t)=>{e(...t),E()}}const h=zo,d=e=>!0===s?e:jr(e,!1===s?1:void 0);let p,g,m=!1,y=!1;if(An(e)?(p=()=>e.value,m=wn(e)):_n(e)?(p=()=>d(e),m=!0):f(e)?(y=!0,m=e.some(e=>_n(e)||wn(e)),p=()=>e.map(e=>An(e)?e.value:_n(e)?d(e):v(e)?Fn(e,h,2):void 0)):p=v(e)?t?()=>Fn(e,h,2):()=>(g&&g(),Un(e,h,3,[b])):o,t&&s){const e=p;p=()=>jr(e())}let _,b=e=>{g=k.onStop=()=>{Fn(e,h,4),g=k.onStop=void 0}};if(Yo){if(b=o,t?n&&Un(t,h,3,[p(),y?[]:void 0,b]):p(),"sync"!==i)return o;{const e=Vs(Tr);_=e.__watcherHandles||(e.__watcherHandles=[])}}let w=y?new Array(e.length).fill(Cr):Cr;const x=()=>{if(k.active&&k.dirty)if(t){const e=k.run();(s||m||(y?e.some((e,t)=>B(e,w[t])):B(e,w)))&&(g&&g(),Un(t,h,3,[e,w===Cr?void 0:y&&w[0]===Cr?[]:w,b]),w=e)}else k.run()};let S;x.allowRecurse=!!t,"sync"===i?S=x:"post"===i?S=()=>io(x,h&&h.suspense):(x.pre=!0,h&&(x.id=h.uid),S=()=>Qn(x));const k=new ut(p,o,S),T=ct(),E=()=>{k.stop(),T&&u(T.effects,k)};return t?n?x():w=k.run():"post"===i?io(k.run.bind(k),h&&h.suspense):k.run(),_&&_.push(E),E}function Ar(e,t,n){const r=this.proxy,s=m(e)?e.includes(".")?$r(r,e):()=>r[e]:e.bind(r,r);let o;v(t)?o=t:(o=t.handler,n=t);const i=Vo(this),a=Pr(s,o.bind(r),n);return i(),a}function $r(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function jr(e,t,n=0,r){if(!_(e)||e.__v_skip)return e;if(t&&t>0){if(n>=t)return e;n++}if((r=r||new Set).has(e))return e;if(r.add(e),An(e))jr(e.value,t,n,r);else if(f(e))for(let s=0;s<e.length;s++)jr(e[s],t,n,r);else if(g(e)||p(e))e.forEach(e=>{jr(e,t,n,r)});else if(S(e))for(const s in e)jr(e[s],t,n,r);return e}function Br(e,t){if(null===ur)return e;const n=Zo(ur)||ur.proxy,s=e.dirs||(e.dirs=[]);for(let o=0;o<t.length;o++){let[e,i,a,l=r]=t[o];e&&(v(e)&&(e={mounted:e,updated:e}),e.deep&&jr(i),s.push({dir:e,instance:n,value:i,oldValue:void 0,arg:a,modifiers:l}))}return e}function Ir(e,t,n,r){const s=e.dirs,o=t&&t.dirs;for(let i=0;i<s.length;i++){const a=s[i];o&&(a.oldValue=o[i].value);let l=a.dir[r];l&&(yt(),Un(l,n,8,[e.el,a,e,t]),_t())}}const Rr=Symbol("_leaveCb"),Lr=Symbol("_enterCb");const Mr=[Function,Array],Dr={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Mr,onEnter:Mr,onAfterEnter:Mr,onEnterCancelled:Mr,onBeforeLeave:Mr,onLeave:Mr,onAfterLeave:Mr,onLeaveCancelled:Mr,onBeforeAppear:Mr,onAppear:Mr,onAfterAppear:Mr,onAppearCancelled:Mr},Nr={name:"BaseTransition",props:Dr,setup(e,{slots:t}){const n=Ho(),r=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return ds(()=>{e.isMounted=!0}),gs(()=>{e.isUnmounting=!0}),e}();return()=>{const s=t.default&&qr(t.default(),!0);if(!s||!s.length)return;let o=s[0];if(s.length>1)for(const e of s)if(e.type!==go){o=e;break}const i=Sn(e),{mode:a}=i;if(r.isLeaving)return zr(o);const l=Hr(o);if(!l)return zr(o);const c=Ur(l,i,r,n);Wr(l,c);const u=n.subTree,h=u&&Hr(u);if(h&&h.type!==go&&!Eo(l,h)){const e=Ur(h,i,r,n);if(Wr(h,e),"out-in"===a)return r.isLeaving=!0,e.afterLeave=()=>{r.isLeaving=!1,!1!==n.update.active&&(n.effect.dirty=!0,n.update())},zr(o);"in-out"===a&&l.type!==go&&(e.delayLeave=(e,t,n)=>{Fr(r,h)[String(h.key)]=h,e[Rr]=()=>{t(),e[Rr]=void 0,delete c.delayedLeave},c.delayedLeave=n})}return o}}};function Fr(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function Ur(e,t,n,r){const{appear:s,mode:o,persisted:i=!1,onBeforeEnter:a,onEnter:l,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:h,onLeave:d,onAfterLeave:p,onLeaveCancelled:g,onBeforeAppear:v,onAppear:m,onAfterAppear:y,onAppearCancelled:_}=t,b=String(e.key),w=Fr(n,e),x=(e,t)=>{e&&Un(e,r,9,t)},S=(e,t)=>{const n=t[1];x(e,t),f(e)?e.every(e=>e.length<=1)&&n():e.length<=1&&n()},k={mode:o,persisted:i,beforeEnter(t){let r=a;if(!n.isMounted){if(!s)return;r=v||a}t[Rr]&&t[Rr](!0);const o=w[b];o&&Eo(e,o)&&o.el[Rr]&&o.el[Rr](),x(r,[t])},enter(e){let t=l,r=c,o=u;if(!n.isMounted){if(!s)return;t=m||l,r=y||c,o=_||u}let i=!1;const a=e[Lr]=t=>{i||(i=!0,x(t?o:r,[e]),k.delayedLeave&&k.delayedLeave(),e[Lr]=void 0)};t?S(t,[e,a]):a()},leave(t,r){const s=String(e.key);if(t[Lr]&&t[Lr](!0),n.isUnmounting)return r();x(h,[t]);let o=!1;const i=t[Rr]=n=>{o||(o=!0,r(),x(n?g:p,[t]),t[Rr]=void 0,w[s]===e&&delete w[s])};w[s]=e,d?S(d,[t,i]):i()},clone:e=>Ur(e,t,n,r)};return k}function zr(e){if(Jr(e))return(e=jo(e)).children=null,e}function Hr(e){return Jr(e)?e.children?e.children[0]:void 0:e}function Wr(e,t){6&e.shapeFlag&&e.component?Wr(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function qr(e,t=!1,n){let r=[],s=0;for(let o=0;o<e.length;o++){let i=e[o];const a=null==n?i.key:String(n)+String(null!=i.key?i.key:o);i.type===fo?(128&i.patchFlag&&s++,r=r.concat(qr(i.children,t,a))):(t||i.type!==go)&&r.push(null!=a?jo(i,{key:a}):i)}if(s>1)for(let o=0;o<r.length;o++)r[o].patchFlag=-2;return r}
/*! #__NO_SIDE_EFFECTS__ */function Vr(e,t){return v(e)?(()=>c({name:e.name},t,{setup:e}))():e}const Xr=e=>!!e.type.__asyncLoader;
/*! #__NO_SIDE_EFFECTS__ */function Kr(e){v(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:r,delay:s=200,timeout:o,suspensible:i=!0,onError:a}=e;let l,c=null,u=0;const h=()=>{let e;return c||(e=c=t().catch(e=>{if(e=e instanceof Error?e:new Error(String(e)),a)return new Promise((t,n)=>{a(e,()=>t((u++,c=null,h())),()=>n(e),u+1)});throw e}).then(t=>e!==c&&c?c:(t&&(t.__esModule||"Module"===t[Symbol.toStringTag])&&(t=t.default),l=t,t)))};return Vr({name:"AsyncComponentWrapper",__asyncLoader:h,get __asyncResolved(){return l},setup(){const e=zo;if(l)return()=>Yr(l,e);const t=t=>{c=null,zn(t,e,13,!r)};if(i&&e.suspense||Yo)return h().then(t=>()=>Yr(t,e)).catch(e=>(t(e),()=>r?$o(r,{error:e}):null));const a=$n(!1),u=$n(),d=$n(!!s);return s&&setTimeout(()=>{d.value=!1},s),null!=o&&setTimeout(()=>{if(!a.value&&!u.value){const e=new Error(`Async component timed out after ${o}ms.`);t(e),u.value=e}},o),h().then(()=>{a.value=!0,e.parent&&Jr(e.parent.vnode)&&(e.parent.effect.dirty=!0,Qn(e.parent.update))}).catch(e=>{t(e),u.value=e}),()=>a.value&&l?Yr(l,e):u.value&&r?$o(r,{error:u.value}):n&&!d.value?$o(n):void 0}})}function Yr(e,t){const{ref:n,props:r,children:s,ce:o}=t.vnode,i=$o(e,r,s);return i.ref=n,i.ce=o,delete t.vnode.ce,i}const Jr=e=>e.type.__isKeepAlive;class Gr{constructor(e){this.max=e,this._cache=new Map,this._keys=new Set,this._max=parseInt(e,10)}get(e){const{_cache:t,_keys:n,_max:r}=this,s=t.get(e);if(s)n.delete(e),n.add(e);else if(n.add(e),r&&n.size>r){const e=n.values().next().value;this.pruneCacheEntry(t.get(e)),this.delete(e)}return s}set(e,t){this._cache.set(e,t)}delete(e){this._cache.delete(e),this._keys.delete(e)}forEach(e,t){this._cache.forEach(e.bind(t))}}const Zr={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number],matchBy:{type:String,default:"name"},cache:Object},setup(e,{slots:t}){const n=Ho(),r=n.ctx;if(!r.renderer)return()=>{const e=t.default&&t.default();return e&&1===e.length?e[0]:e};const s=e.cache||new Gr(e.max);s.pruneCacheEntry=i;let o=null;function i(t){var r;!o||!Eo(t,o)||"key"===e.matchBy&&t.key!==o.key?(os(r=t),u(r,n,a,!0)):o&&os(o)}const a=n.suspense,{renderer:{p:l,m:c,um:u,o:{createElement:h}}}=r,d=h("div");function f(t){s.forEach((n,r)=>{const o=as(n,e.matchBy);!o||t&&t(o)||(s.delete(r),i(n))})}r.activate=(e,t,n,r,s)=>{const o=e.component;if(o.ba){const e=o.isDeactivated;o.isDeactivated=!1,I(o.ba),o.isDeactivated=e}c(e,t,n,0,a),l(o.vnode,e,t,n,o,a,r,e.slotScopeIds,s),io(()=>{o.isDeactivated=!1,o.a&&I(o.a);const t=e.props&&e.props.onVnodeMounted;t&&No(t,o.parent,e)},a)},r.deactivate=e=>{const t=e.component;t.bda&&ls(t.bda),c(e,d,null,1,a),io(()=>{t.bda&&t.bda.forEach(e=>e.__called=!1),t.da&&I(t.da);const n=e.props&&e.props.onVnodeUnmounted;n&&No(n,t.parent,e),t.isDeactivated=!0},a)},Or(()=>[e.include,e.exclude,e.matchBy],([e,t])=>{e&&f(t=>es(e,t)),t&&f(e=>!es(t,e))},{flush:"post",deep:!0});let p=null;const g=()=>{null!=p&&s.set(p,is(n.subTree))};return ds(g),ps(g),gs(()=>{s.forEach((t,r)=>{s.delete(r),i(t);const{subTree:o,suspense:a}=n,l=is(o);if(t.type===l.type&&("key"!==e.matchBy||t.key===l.key)){l.component.bda&&I(l.component.bda),os(l);const e=l.component.da;return void(e&&io(e,a))}})}),()=>{if(p=null,!t.default)return null;const n=t.default(),r=n[0];if(n.length>1)return o=null,n;if(!To(r)||!(4&r.shapeFlag)&&!kr(r.type))return o=null,r;let i=is(r);const a=i.type,l=as(i,e.matchBy),{include:c,exclude:u}=e;if(c&&(!l||!es(c,l))||u&&l&&es(u,l))return o=i,r;const h=null==i.key?a:i.key,d=s.get(h);return i.el&&(i=jo(i),kr(r.type)&&(r.ssContent=i)),p=h,d&&(i.el=d.el,i.component=d.component,i.transition&&Wr(i,i.transition),i.shapeFlag|=512),i.shapeFlag|=256,o=i,kr(r.type)?r:i}}},Qr=Zr;function es(e,t){return f(e)?e.some(e=>es(e,t)):m(e)?e.split(",").includes(t):"[object RegExp]"===x(e)&&e.test(t)}function ts(e,t){rs(e,"a",t)}function ns(e,t){rs(e,"da",t)}function rs(e,t,n=zo){const r=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(r.__called=!1,cs(t,r,n),n){let e=n.parent;for(;e&&e.parent;)Jr(e.parent.vnode)&&ss(r,t,n,e),e=e.parent}}function ss(e,t,n,r){const s=cs(t,e,r,!0);vs(()=>{u(r[t],s)},n)}function os(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function is(e){return kr(e.type)?e.ssContent:e}function as(e,t){if("name"===t){const t=e.type;return Qo(Xr(e)?t.__asyncResolved||{}:t)}return String(e.key)}function ls(e){for(let t=0;t<e.length;t++){const n=e[t];n.__called||(n(),n.__called=!0)}}function cs(e,t,n=zo,r=!1){if(n){if(s=e,Je.indexOf(s)>-1&&n.$pageInstance){if(n.type.__reserved)return;if(n!==n.$pageInstance&&(n=n.$pageInstance,function(e){return[he,se].indexOf(e)>-1}(e))){const r=n.proxy;Un(t.bind(r),n,e,he===e?[r.$page.options]:[])}}const o=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...r)=>{if(n.isUnmounted)return;yt();const s=Vo(n),o=Un(t,n,e,r);return s(),_t(),o});return r?o.unshift(i):o.push(i),i}var s}const us=e=>(t,n=zo)=>(!Yo||"sp"===e)&&cs(e,(...e)=>t(...e),n),hs=us("bm"),ds=us("m"),fs=us("bu"),ps=us("u"),gs=us("bum"),vs=us("um"),ms=us("sp"),ys=us("rtg"),_s=us("rtc");function bs(e,t=zo){cs("ec",e,t)}function ws(e,t,n,r){let s;const o=n;if(f(e)||m(e)){s=new Array(e.length);for(let n=0,r=e.length;n<r;n++)s[n]=t(e[n],n,void 0,o)}else if("number"==typeof e){s=new Array(e);for(let n=0;n<e;n++)s[n]=t(n+1,n,void 0,o)}else if(_(e))if(e[Symbol.iterator])s=Array.from(e,(e,n)=>t(e,n,void 0,o));else{const n=Object.keys(e);s=new Array(n.length);for(let r=0,i=n.length;r<i;r++){const i=n[r];s[r]=t(e[i],i,r,o)}}else s=[];return s}function xs(e,t,n={},r,s){if(ur.isCE||ur.parent&&Xr(ur.parent)&&ur.parent.isCE)return"default"!==t&&(n.name=t),$o("slot",n,r);let o=e[t];o&&o._c&&(o._d=!1),_o();const i=o&&Ss(o(n)),a=ko(fo,{key:n.key||i&&i.key||`_${t}`},i||[],i&&1===e._?64:-2);return!s&&a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),o&&o._c&&(o._d=!0),a}function Ss(e){return e.some(e=>!To(e)||e.type!==go&&!(e.type===fo&&!Ss(e.children)))?e:null}const ks=e=>{if(!e)return null;if(Ko(e)){return Zo(e)||e.proxy}return ks(e.parent)},Ts=c(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>ks(e.parent),$root:e=>ks(e.root),$emit:e=>e.emit,$options:e=>Bs(e),$forceUpdate:e=>e.f||(e.f=(e=>function(){e.effect.dirty=!0,Qn(e.update)})(e)),$nextTick:e=>e.n||(e.n=Zn.bind(e.proxy)),$watch:e=>Ar.bind(e)}),Es=(e,t)=>e!==r&&!e.__isScriptSetup&&d(e,t),Cs={get({_:e},t){const{ctx:n,setupState:s,data:o,props:i,accessCache:a,type:l,appContext:c}=e;let u;if("$"!==t[0]){const l=a[t];if(void 0!==l)switch(l){case 1:return s[t];case 2:return o[t];case 4:return n[t];case 3:return i[t]}else{if(Es(s,t))return a[t]=1,s[t];if(o!==r&&d(o,t))return a[t]=2,o[t];if((u=e.propsOptions[0])&&d(u,t))return a[t]=3,i[t];if(n!==r&&d(n,t))return a[t]=4,n[t];Ps&&(a[t]=0)}}const h=Ts[t];let f,p;return h?("$attrs"===t&&Pt(e,0,t),h(e)):(f=l.__cssModules)&&(f=f[t])?f:n!==r&&d(n,t)?(a[t]=4,n[t]):(p=c.config.globalProperties,d(p,t)?p[t]:void 0)},set({_:e},t,n){const{data:s,setupState:o,ctx:i}=e;return Es(o,t)?(o[t]=n,!0):s!==r&&d(s,t)?(s[t]=n,!0):!d(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(i[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:o,propsOptions:i}},a){let l;return!!n[a]||e!==r&&d(e,a)||Es(t,a)||(l=i[0])&&d(l,a)||d(s,a)||d(Ts,a)||d(o.config.globalProperties,a)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:d(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Os(e){return f(e)?e.reduce((e,t)=>(e[t]=null,e),{}):e}let Ps=!0;function As(e){const t=Bs(e),n=e.proxy,r=e.ctx;Ps=!1,t.beforeCreate&&$s(t.beforeCreate,e,"bc");const{data:s,computed:i,methods:a,watch:l,provide:c,inject:u,created:h,beforeMount:d,mounted:p,beforeUpdate:g,updated:m,activated:y,deactivated:b,beforeDestroy:w,beforeUnmount:x,destroyed:S,unmounted:k,render:T,renderTracked:E,renderTriggered:C,errorCaptured:O,serverPrefetch:P,expose:A,inheritAttrs:$,components:j,directives:B,filters:I}=t;if(u&&function(e,t){f(e)&&(e=Ms(e));for(const n in e){const r=e[n];let s;s=_(r)?"default"in r?Vs(r.from||n,r.default,!0):Vs(r.from||n):Vs(r),An(s)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>s.value,set:e=>s.value=e}):t[n]=s}}(u,r,null),a)for(const o in a){const e=a[o];v(e)&&(r[o]=e.bind(n))}if(s){const t=s.call(n,n);_(t)&&(e.data=gn(t))}if(Ps=!0,i)for(const f in i){const e=i[f],t=v(e)?e.bind(n,n):v(e.get)?e.get.bind(n,n):o,s=!v(e)&&v(e.set)?e.set.bind(n):o,a=ei({get:t,set:s});Object.defineProperty(r,f,{enumerable:!0,configurable:!0,get:()=>a.value,set:e=>a.value=e})}if(l)for(const o in l)js(l[o],r,n,o);if(c){const e=v(c)?c.call(n):c;Reflect.ownKeys(e).forEach(t=>{qs(t,e[t])})}function R(e,t){f(t)?t.forEach(t=>e(t.bind(n))):t&&e(t.bind(n))}if(h&&$s(h,e,"c"),R(hs,d),R(ds,p),R(fs,g),R(ps,m),R(ts,y),R(ns,b),R(bs,O),R(_s,E),R(ys,C),R(gs,x),R(vs,k),R(ms,P),f(A))if(A.length){const t=e.exposed||(e.exposed={});A.forEach(e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})})}else e.exposed||(e.exposed={});T&&e.render===o&&(e.render=T),null!=$&&(e.inheritAttrs=$),j&&(e.components=j),B&&(e.directives=B);const L=e.appContext.config.globalProperties.$applyOptions;L&&L(t,e,n)}function $s(e,t,n){Un(f(e)?e.map(e=>e.bind(t.proxy)):e.bind(t.proxy),t,n)}function js(e,t,n,r){const s=r.includes(".")?$r(n,r):()=>n[r];if(m(e)){const n=t[e];v(n)&&Or(s,n)}else if(v(e))Or(s,e.bind(n));else if(_(e))if(f(e))e.forEach(e=>js(e,t,n,r));else{const r=v(e.handler)?e.handler.bind(n):t[e.handler];v(r)&&Or(s,r,e)}}function Bs(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:s,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,a=o.get(t);let l;return a?l=a:s.length||n||r?(l={},s.length&&s.forEach(e=>Is(l,e,i,!0)),Is(l,t,i)):l=t,_(t)&&o.set(t,l),l}function Is(e,t,n,r=!1){const{mixins:s,extends:o}=t;o&&Is(e,o,n,!0),s&&s.forEach(t=>Is(e,t,n,!0));for(const i in t)if(r&&"expose"===i);else{const r=Rs[i]||n&&n[i];e[i]=r?r(e[i],t[i]):t[i]}return e}const Rs={data:Ls,props:Fs,emits:Fs,methods:Ns,computed:Ns,beforeCreate:Ds,created:Ds,beforeMount:Ds,mounted:Ds,beforeUpdate:Ds,updated:Ds,beforeDestroy:Ds,beforeUnmount:Ds,destroyed:Ds,unmounted:Ds,activated:Ds,deactivated:Ds,errorCaptured:Ds,serverPrefetch:Ds,components:Ns,directives:Ns,watch:function(e,t){if(!e)return t;if(!t)return e;const n=c(Object.create(null),e);for(const r in t)n[r]=Ds(e[r],t[r]);return n},provide:Ls,inject:function(e,t){return Ns(Ms(e),Ms(t))}};function Ls(e,t){return t?e?function(){return c(v(e)?e.call(this,this):e,v(t)?t.call(this,this):t)}:t:e}function Ms(e){if(f(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Ds(e,t){return e?[...new Set([].concat(e,t))]:t}function Ns(e,t){return e?c(Object.create(null),e,t):t}function Fs(e,t){return e?f(e)&&f(t)?[...new Set([...e,...t])]:c(Object.create(null),Os(e),Os(null!=t?t:{})):t}function Us(){return{app:null,config:{isNativeTag:i,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let zs=0;function Hs(e,t){return function(t,n=null){v(t)||(t=c({},t)),null==n||_(n)||(n=null);const r=Us(),s=new WeakSet;let o=!1;const i=r.app={_uid:zs++,_component:t,_props:n,_container:null,_context:r,_instance:null,version:ni,get config(){return r.config},set config(e){},use:(e,...t)=>(s.has(e)||(e&&v(e.install)?(s.add(e),e.install(i,...t)):v(e)&&(s.add(e),e(i,...t))),i),mixin:e=>(r.mixins.includes(e)||r.mixins.push(e),i),component:(e,t)=>t?(r.components[e]=t,i):r.components[e],directive:(e,t)=>t?(r.directives[e]=t,i):r.directives[e],mount(s,a,l){if(!o){const a=$o(t,n);return a.appContext=r,!0===l?l="svg":!1===l&&(l=void 0),e(a,s,l),o=!0,i._container=s,s.__vue_app__=i,i._instance=a.component,Zo(a.component)||a.component.proxy}},unmount(){o&&(e(null,i._container),delete i._container.__vue_app__)},provide:(e,t)=>(r.provides[e]=t,i),runWithContext(e){const t=Ws;Ws=i;try{return e()}finally{Ws=t}}};return i}}let Ws=null;function qs(e,t){if(zo){let n=zo.provides;const r=zo.parent&&zo.parent.provides;r===n&&(n=zo.provides=Object.create(r)),n[e]=t,"app"===zo.type.mpType&&zo.appContext.app.provide(e,t)}else;}function Vs(e,t,n=!1){const r=zo||ur;if(r||Ws){const s=r?null==r.parent?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:Ws._context.provides;if(s&&e in s)return s[e];if(arguments.length>1)return n&&v(t)?t.call(r&&r.proxy):t}}function Xs(e,t,n,s){const[o,i]=e.propsOptions;let a,l=!1;if(t)for(let r in t){if(T(r))continue;const c=t[r];let u;o&&d(o,u=O(r))?i&&i.includes(u)?(a||(a={}))[u]=c:n[u]=c:cr(e.emitsOptions,r)||r in s&&c===s[r]||(s[r]=c,l=!0)}if(i){const t=Sn(n),s=a||r;for(let r=0;r<i.length;r++){const a=i[r];n[a]=Ks(o,t,a,s[a],e,!d(s,a))}}return l}function Ks(e,t,n,r,s,o){const i=e[n];if(null!=i){const e=d(i,"default");if(e&&void 0===r){const e=i.default;if(i.type!==Function&&!i.skipFactory&&v(e)){const{propsDefaults:o}=s;if(n in o)r=o[n];else{const i=Vo(s);r=o[n]=e.call(null,t),i()}}else r=e}i[0]&&(o&&!e?r=!1:!i[1]||""!==r&&r!==A(n)||(r=!0))}return r}function Ys(e,t,n=!1){const o=t.propsCache,i=o.get(e);if(i)return i;const a=e.props,l={},u=[];let h=!1;if(!v(e)){const r=e=>{h=!0;const[n,r]=Ys(e,t,!0);c(l,n),r&&u.push(...r)};!n&&t.mixins.length&&t.mixins.forEach(r),e.extends&&r(e.extends),e.mixins&&e.mixins.forEach(r)}if(!a&&!h)return _(e)&&o.set(e,s),s;if(f(a))for(let s=0;s<a.length;s++){const e=O(a[s]);Js(e)&&(l[e]=r)}else if(a)for(const r in a){const e=O(r);if(Js(e)){const t=a[r],n=l[e]=f(t)||v(t)?{type:t}:c({},t);if(n){const t=Qs(Boolean,n.type),r=Qs(String,n.type);n[0]=t>-1,n[1]=r<0||t<r,(t>-1||d(n,"default"))&&u.push(e)}}}const p=[l,u];return _(e)&&o.set(e,p),p}function Js(e){return"$"!==e[0]&&!T(e)}function Gs(e){if(null===e)return"null";if("function"==typeof e)return e.name||"";if("object"==typeof e){return e.constructor&&e.constructor.name||""}return""}function Zs(e,t){return Gs(e)===Gs(t)}function Qs(e,t){return f(t)?t.findIndex(t=>Zs(t,e)):v(t)&&Zs(t,e)?0:-1}const eo=e=>"_"===e[0]||"$stable"===e,to=e=>f(e)?e.map(Ro):[Ro(e)],no=(e,t,n)=>{if(t._n)return t;const r=fr((...e)=>to(t(...e)),n);return r._c=!1,r},ro=(e,t,n)=>{const r=e._ctx;for(const s in e){if(eo(s))continue;const n=e[s];if(v(n))t[s]=no(0,n,r);else if(null!=n){const e=to(n);t[s]=()=>e}}},so=(e,t)=>{const n=to(t);e.slots.default=()=>n};function oo(e,t,n,s,o=!1){if(f(e))return void e.forEach((e,r)=>oo(e,t&&(f(t)?t[r]:t),n,s,o));if(Xr(s)&&!o)return;const i=4&s.shapeFlag?Zo(s.component)||s.component.proxy:s.el,a=o?null:i,{i:l,r:c}=e,h=t&&t.r,p=l.refs===r?l.refs={}:l.refs,g=l.setupState;if(null!=h&&h!==c&&(m(h)?(p[h]=null,d(g,h)&&(g[h]=null)):An(h)&&(h.value=null)),v(c))Fn(c,l,12,[a,p]);else{const t=m(c),r=An(c);if(t||r){const s=()=>{if(e.f){const n=t?d(g,c)?g[c]:p[c]:c.value;o?f(n)&&u(n,i):f(n)?n.includes(i)||n.push(i):t?(p[c]=[i],d(g,c)&&(g[c]=p[c])):(c.value=[i],e.k&&(p[e.k]=c.value))}else t?(p[c]=a,d(g,c)&&(g[c]=a)):r&&(c.value=a,e.k&&(p[e.k]=a))};a?(s.id=-1,io(s,n)):s()}}}const io=function(e,t){var n;t&&t.pendingBranch?f(e)?t.effects.push(...e):t.effects.push(e):(f(n=e)?Xn.push(...n):Kn&&Kn.includes(n,n.allowRecurse?Yn+1:Yn)||Xn.push(n),er())};function ao(e){return function(e){D().__VUE__=!0;const{insert:t,remove:n,patchProp:i,forcePatchProp:a,createElement:l,createText:u,createComment:h,setText:f,setElementText:p,parentNode:g,nextSibling:v,setScopeId:m=o,insertStaticContent:y}=e,_=(e,t,n,r=null,s=null,o=null,i=void 0,a=null,l=!!t.dynamicChildren)=>{if(e===t)return;e&&!Eo(e,t)&&(r=ee(e),Y(e,s,o,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:h}=t;switch(c){case po:w(e,t,n,r);break;case go:x(e,t,n,r);break;case vo:null==e&&S(t,n,r,i);break;case fo:N(e,t,n,r,s,o,i,a,l);break;default:1&h?C(e,t,n,r,s,o,i,a,l):6&h?F(e,t,n,r,s,o,i,a,l):(64&h||128&h)&&c.process(e,t,n,r,s,o,i,a,l,re)}null!=u&&s&&oo(u,e&&e.ref,o,t||e,!t)},w=(e,n,r,s)=>{if(null==e)t(n.el=u(n.children),r,s);else{const t=n.el=e.el;n.children!==e.children&&f(t,n.children)}},x=(e,n,r,s)=>{null==e?t(n.el=h(n.children||""),r,s):n.el=e.el},S=(e,t,n,r)=>{[e.el,e.anchor]=y(e.children,t,n,r,e.el,e.anchor)},k=({el:e,anchor:n},r,s)=>{let o;for(;e&&e!==n;)o=v(e),t(e,r,s),e=o;t(n,r,s)},E=({el:e,anchor:t})=>{let r;for(;e&&e!==t;)r=v(e),n(e),e=r;n(t)},C=(e,t,n,r,s,o,i,a,l)=>{"svg"===t.type?i="svg":"math"===t.type&&(i="mathml"),null==e?P(t,n,r,s,o,i,a,l):B(e,t,s,o,i,a,l)},P=(e,n,r,s,o,a,c,u)=>{let h,d;const{props:f,shapeFlag:g,transition:v,dirs:m}=e;if(h=e.el=l(e.type,a,f&&f.is,f),8&g?p(h,e.children):16&g&&j(e.children,h,null,s,o,lo(e,a),c,u),m&&Ir(e,null,s,"created"),$(h,e,e.scopeId,c,s),f){for(const t in f)"value"===t||T(t)||i(h,t,null,f[t],a,e.children,s,o,Q);"value"in f&&i(h,"value",null,f.value,a),(d=f.onVnodeBeforeMount)&&No(d,s,e)}Object.defineProperty(h,"__vueParentComponent",{value:s,enumerable:!1}),m&&Ir(e,null,s,"beforeMount");const y=function(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}(o,v);y&&v.beforeEnter(h),t(h,n,r),((d=f&&f.onVnodeMounted)||y||m)&&io(()=>{d&&No(d,s,e),y&&v.enter(h),m&&Ir(e,null,s,"mounted")},o)},$=(e,t,n,r,s)=>{if(n&&m(e,n),r)for(let o=0;o<r.length;o++)m(e,r[o]);if(s){if(t===s.subTree){const t=s.vnode;$(e,t,t.scopeId,t.slotScopeIds,s.parent)}}},j=(e,t,n,r,s,o,i,a,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=a?Lo(e[c]):Ro(e[c]);_(null,l,t,n,r,s,o,i,a)}},B=(e,t,n,s,o,l,c)=>{const u=t.el=e.el;let{patchFlag:h,dynamicChildren:d,dirs:f}=t;h|=16&e.patchFlag;const g=e.props||r,v=t.props||r;let m;if(n&&co(n,!1),(m=v.onVnodeBeforeUpdate)&&No(m,n,t,e),f&&Ir(t,e,n,"beforeUpdate"),n&&co(n,!0),d?L(e.dynamicChildren,d,u,n,s,lo(t,o),l):c||q(e,t,u,null,n,s,lo(t,o),l,!1),h>0){if(16&h)M(u,t,g,v,n,s,o);else if(2&h&&g.class!==v.class&&i(u,"class",null,v.class,o),4&h&&i(u,"style",g.style,v.style,o),8&h){const r=t.dynamicProps;for(let t=0;t<r.length;t++){const l=r[t],c=g[l],h=v[l];(h!==c||"value"===l||a&&a(u,l))&&i(u,l,c,h,o,e.children,n,s,Q)}}1&h&&e.children!==t.children&&p(u,t.children)}else c||null!=d||M(u,t,g,v,n,s,o);((m=v.onVnodeUpdated)||f)&&io(()=>{m&&No(m,n,t,e),f&&Ir(t,e,n,"updated")},s)},L=(e,t,n,r,s,o,i)=>{for(let a=0;a<t.length;a++){const l=e[a],c=t[a],u=l.el&&(l.type===fo||!Eo(l,c)||70&l.shapeFlag)?g(l.el):n;_(l,c,u,null,r,s,o,i,!0)}},M=(e,t,n,s,o,l,c)=>{if(n!==s){if(n!==r)for(const r in n)T(r)||r in s||i(e,r,n[r],null,c,t.children,o,l,Q);for(const r in s){if(T(r))continue;const u=s[r],h=n[r];(u!==h&&"value"!==r||a&&a(e,r))&&i(e,r,h,u,c,t.children,o,l,Q)}"value"in s&&i(e,"value",n.value,s.value,c)}},N=(e,n,r,s,o,i,a,l,c)=>{const h=n.el=e?e.el:u(""),d=n.anchor=e?e.anchor:u("");let{patchFlag:f,dynamicChildren:p,slotScopeIds:g}=n;g&&(l=l?l.concat(g):g),null==e?(t(h,r,s),t(d,r,s),j(n.children||[],r,d,o,i,a,l,c)):f>0&&64&f&&p&&e.dynamicChildren?(L(e.dynamicChildren,p,r,o,i,a,l),(null!=n.key||o&&n===o.subTree)&&uo(e,n,!0)):q(e,n,r,d,o,i,a,l,c)},F=(e,t,n,r,s,o,i,a,l)=>{t.slotScopeIds=a,null==e?512&t.shapeFlag?s.ctx.activate(t,n,r,i,l):U(t,n,r,s,o,i,l):z(e,t,l)},U=(e,t,n,s,o,i,a)=>{const l=e.component=function(e,t,n){const s=e.type,o=(t?t.appContext:e.appContext)||Fo,i={uid:Uo++,vnode:e,type:s,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,scope:new at(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Ys(s,o),emitsOptions:lr(s,o),emit:null,emitted:null,propsDefaults:r,inheritAttrs:s.inheritAttrs,ctx:r,data:r,props:r,attrs:r,slots:r,refs:r,setupState:r,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,bda:null,da:null,ba:null,a:null,rtg:null,rtc:null,ec:null,sp:null};i.ctx={_:i},i.root=t?t.root:i,i.emit=ir.bind(null,i),i.$pageInstance=t&&t.$pageInstance,e.ce&&e.ce(i);return i}(e,s,o);if(Jr(e)&&(l.ctx.renderer=re),function(e,t=!1){t&&qo(t);const{props:n,children:r}=e.vnode,s=Ko(e);(function(e,t,n,r=!1){const s={},o={};R(o,Co,1),e.propsDefaults=Object.create(null),Xs(e,t,s,o);for(const i in e.propsOptions[0])i in s||(s[i]=void 0);n?e.props=r?s:vn(s):e.type.props?e.props=s:e.props=o,e.attrs=o})(e,n,s,t),((e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=Sn(t),R(t,"_",n)):ro(t,e.slots={})}else e.slots={},t&&so(e,t);R(e.slots,Co,1)})(e,r);const o=s?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=kn(new Proxy(e.ctx,Cs));const{setup:r}=n;if(r){const n=e.setupContext=r.length>1?function(e){const t=t=>{e.exposed=t||{}};return{get attrs(){return function(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get:(t,n)=>(Pt(e,0,"$attrs"),t[n])}))}(e)},slots:e.slots,emit:e.emit,expose:t}}(e):null,s=Vo(e);yt();const o=Fn(r,e,0,[e.props,n]);if(_t(),s(),b(o)){if(o.then(Xo,Xo),t)return o.then(t=>{Jo(e,t)}).catch(t=>{zn(t,e,0)});e.asyncDep=o}else Jo(e,o)}else Go(e)}(e,t):void 0;t&&qo(!1)}(l),l.asyncDep){if(o&&o.registerDep(l,H),!e.el){const e=l.subTree=$o(go);x(null,e,t,n)}}else H(l,e,t,n,o,i,a)},z=(e,t,n)=>{const r=t.component=e.component;if(function(e,t,n){const{props:r,children:s,component:o}=e,{props:i,children:a,patchFlag:l}=t,c=o.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!s&&!a||a&&a.$stable)||r!==i&&(r?!i||mr(r,i,c):!!i);if(1024&l)return!0;if(16&l)return r?mr(r,i,c):!!i;if(8&l){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(i[n]!==r[n]&&!cr(c,n))return!0}}return!1}(e,t,n)){if(r.asyncDep&&!r.asyncResolved)return void W(r,t,n);r.next=t,function(e){const t=qn.indexOf(e);t>Vn&&qn.splice(t,1)}(r.update),r.effect.dirty=!0,r.update()}else t.el=e.el,r.vnode=t},H=(e,t,n,r,s,i,a)=>{const l=()=>{if(e.isMounted){let{next:t,bu:n,u:r,parent:o,vnode:c}=e;{const n=ho(e);if(n)return t&&(t.el=c.el,W(e,t,a)),void n.asyncDep.then(()=>{e.isUnmounted||l()})}let u,h=t;co(e,!1),t?(t.el=c.el,W(e,t,a)):t=c,n&&I(n),(u=t.props&&t.props.onVnodeBeforeUpdate)&&No(u,o,t,c),co(e,!0);const d=pr(e),f=e.subTree;e.subTree=d,_(f,d,g(f.el),ee(f),e,s,i),t.el=d.el,null===h&&function({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r!==e)break;(e=t.vnode).el=n,t=t.parent}}(e,d.el),r&&io(r,s),(u=t.props&&t.props.onVnodeUpdated)&&io(()=>No(u,o,t,c),s)}else{let o;const{el:a,props:l}=t,{bm:c,m:u,parent:h}=e,d=Xr(t);co(e,!1),c&&I(c),!d&&(o=l&&l.onVnodeBeforeMount)&&No(o,h,t),co(e,!0);{const o=e.subTree=pr(e);_(null,o,n,r,e,s,i),t.el=o.el}if(u&&io(u,s),!d&&(o=l&&l.onVnodeMounted)){const e=t;io(()=>No(o,h,e),s)}(256&t.shapeFlag||h&&Xr(h.vnode)&&256&h.vnode.shapeFlag)&&(e.ba&&ls(e.ba),e.a&&io(e.a,s)),e.isMounted=!0,t=n=r=null}},c=e.effect=new ut(l,o,()=>Qn(u),e.scope),u=e.update=()=>{c.dirty&&c.run()};u.id=e.uid,co(e,!0),u()},W=(e,t,n)=>{t.component=e;const s=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,r){const{props:s,attrs:o,vnode:{patchFlag:i}}=e,a=Sn(s),[l]=e.propsOptions;let c=!1;if(!(r||i>0)||16&i){let r;Xs(e,t,s,o)&&(c=!0);for(const o in a)t&&(d(t,o)||(r=A(o))!==o&&d(t,r))||(l?!n||void 0===n[o]&&void 0===n[r]||(s[o]=Ks(l,a,o,void 0,e,!0)):delete s[o]);if(o!==a)for(const e in o)t&&d(t,e)||(delete o[e],c=!0)}else if(8&i){const n=e.vnode.dynamicProps;for(let r=0;r<n.length;r++){let i=n[r];if(cr(e.emitsOptions,i))continue;const u=t[i];if(l)if(d(o,i))u!==o[i]&&(o[i]=u,c=!0);else{const t=O(i);s[t]=Ks(l,a,t,u,e,!1)}else u!==o[i]&&(o[i]=u,c=!0)}}c&&At(e,"set","$attrs")}(e,t.props,s,n),((e,t,n)=>{const{vnode:s,slots:o}=e;let i=!0,a=r;if(32&s.shapeFlag){const e=t._;e?n&&1===e?i=!1:(c(o,t),n||1!==e||delete o._):(i=!t.$stable,ro(t,o)),a=t}else t&&(so(e,t),a={default:1});if(i)for(const r in o)eo(r)||null!=a[r]||delete o[r]})(e,t.children,n),yt(),tr(e),_t()},q=(e,t,n,r,s,o,i,a,l=!1)=>{const c=e&&e.children,u=e?e.shapeFlag:0,h=t.children,{patchFlag:d,shapeFlag:f}=t;if(d>0){if(128&d)return void X(c,h,n,r,s,o,i,a,l);if(256&d)return void V(c,h,n,r,s,o,i,a,l)}8&f?(16&u&&Q(c,s,o),h!==c&&p(n,h)):16&u?16&f?X(c,h,n,r,s,o,i,a,l):Q(c,s,o,!0):(8&u&&p(n,""),16&f&&j(h,n,r,s,o,i,a,l))},V=(e,t,n,r,o,i,a,l,c)=>{t=t||s;const u=(e=e||s).length,h=t.length,d=Math.min(u,h);let f;for(f=0;f<d;f++){const r=t[f]=c?Lo(t[f]):Ro(t[f]);_(e[f],r,n,null,o,i,a,l,c)}u>h?Q(e,o,i,!0,!1,d):j(t,n,r,o,i,a,l,c,d)},X=(e,t,n,r,o,i,a,l,c)=>{let u=0;const h=t.length;let d=e.length-1,f=h-1;for(;u<=d&&u<=f;){const r=e[u],s=t[u]=c?Lo(t[u]):Ro(t[u]);if(!Eo(r,s))break;_(r,s,n,null,o,i,a,l,c),u++}for(;u<=d&&u<=f;){const r=e[d],s=t[f]=c?Lo(t[f]):Ro(t[f]);if(!Eo(r,s))break;_(r,s,n,null,o,i,a,l,c),d--,f--}if(u>d){if(u<=f){const e=f+1,s=e<h?t[e].el:r;for(;u<=f;)_(null,t[u]=c?Lo(t[u]):Ro(t[u]),n,s,o,i,a,l,c),u++}}else if(u>f)for(;u<=d;)Y(e[u],o,i,!0),u++;else{const p=u,g=u,v=new Map;for(u=g;u<=f;u++){const e=t[u]=c?Lo(t[u]):Ro(t[u]);null!=e.key&&v.set(e.key,u)}let m,y=0;const b=f-g+1;let w=!1,x=0;const S=new Array(b);for(u=0;u<b;u++)S[u]=0;for(u=p;u<=d;u++){const r=e[u];if(y>=b){Y(r,o,i,!0);continue}let s;if(null!=r.key)s=v.get(r.key);else for(m=g;m<=f;m++)if(0===S[m-g]&&Eo(r,t[m])){s=m;break}void 0===s?Y(r,o,i,!0):(S[s-g]=u+1,s>=x?x=s:w=!0,_(r,t[s],n,null,o,i,a,l,c),y++)}const k=w?function(e){const t=e.slice(),n=[0];let r,s,o,i,a;const l=e.length;for(r=0;r<l;r++){const l=e[r];if(0!==l){if(s=n[n.length-1],e[s]<l){t[r]=s,n.push(r);continue}for(o=0,i=n.length-1;o<i;)a=o+i>>1,e[n[a]]<l?o=a+1:i=a;l<e[n[o]]&&(o>0&&(t[r]=n[o-1]),n[o]=r)}}o=n.length,i=n[o-1];for(;o-- >0;)n[o]=i,i=t[i];return n}(S):s;for(m=k.length-1,u=b-1;u>=0;u--){const e=g+u,s=t[e],d=e+1<h?t[e+1].el:r;0===S[u]?_(null,s,n,d,o,i,a,l,c):w&&(m<0||u!==k[m]?K(s,n,d,2):m--)}}},K=(e,n,r,s,o=null)=>{const{el:i,type:a,transition:l,children:c,shapeFlag:u}=e;if(6&u)return void K(e.component.subTree,n,r,s);if(128&u)return void e.suspense.move(n,r,s);if(64&u)return void a.move(e,n,r,re);if(a===fo){t(i,n,r);for(let e=0;e<c.length;e++)K(c[e],n,r,s);return void t(e.anchor,n,r)}if(a===vo)return void k(e,n,r);if(2!==s&&1&u&&l)if(0===s)l.beforeEnter(i),t(i,n,r),io(()=>l.enter(i),o);else{const{leave:e,delayLeave:s,afterLeave:o}=l,a=()=>t(i,n,r),c=()=>{e(i,()=>{a(),o&&o()})};s?s(i,a,c):c()}else t(i,n,r)},Y=(e,t,n,r=!1,s=!1)=>{const{type:o,props:i,ref:a,children:l,dynamicChildren:c,shapeFlag:u,patchFlag:h,dirs:d}=e;if(null!=a&&oo(a,null,n,e,!0),256&u)return void t.ctx.deactivate(e);const f=1&u&&d,p=!Xr(e);let g;if(p&&(g=i&&i.onVnodeBeforeUnmount)&&No(g,t,e),6&u)Z(e.component,n,r);else{if(128&u)return void e.suspense.unmount(n,r);f&&Ir(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,s,re,r):c&&(o!==fo||h>0&&64&h)?Q(c,t,n,!1,!0):(o===fo&&384&h||!s&&16&u)&&Q(l,t,n),r&&J(e)}(p&&(g=i&&i.onVnodeUnmounted)||f)&&io(()=>{g&&No(g,t,e),f&&Ir(e,null,t,"unmounted")},n)},J=e=>{const{type:t,el:r,anchor:s,transition:o}=e;if(t===fo)return void G(r,s);if(t===vo)return void E(e);const i=()=>{n(r),o&&!o.persisted&&o.afterLeave&&o.afterLeave()};if(1&e.shapeFlag&&o&&!o.persisted){const{leave:t,delayLeave:n}=o,s=()=>t(r,i);n?n(e.el,i,s):s()}else i()},G=(e,t)=>{let r;for(;e!==t;)r=v(e),n(e),e=r;n(t)},Z=(e,t,n)=>{const{bum:r,scope:s,update:o,subTree:i,um:a}=e;r&&I(r),s.stop(),o&&(o.active=!1,Y(i,e,t,n)),a&&io(a,t),io(()=>{e.isUnmounted=!0},t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},Q=(e,t,n,r=!1,s=!1,o=0)=>{for(let i=o;i<e.length;i++)Y(e[i],t,n,r,s)},ee=e=>6&e.shapeFlag?ee(e.component.subTree):128&e.shapeFlag?e.suspense.next():v(e.anchor||e.el);let te=!1;const ne=(e,t,n)=>{null==e?t._vnode&&Y(t._vnode,null,null,!0):_(t._vnode||null,e,t,null,null,null,n),te||(te=!0,tr(),nr(),te=!1),t._vnode=e},re={p:_,um:Y,m:K,r:J,mt:U,mc:j,pc:q,pbc:L,n:ee,o:e};let se;return{render:ne,hydrate:se,createApp:Hs(ne)}}(e)}function lo({type:e,props:t},n){return"svg"===n&&"foreignObject"===e||"mathml"===n&&"annotation-xml"===e&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function co({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function uo(e,t,n=!1){const r=e.children,s=t.children;if(f(r)&&f(s))for(let o=0;o<r.length;o++){const e=r[o];let t=s[o];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=s[o]=Lo(s[o]),t.el=e.el),n||uo(e,t)),t.type===po&&(t.el=e.el)}}function ho(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:ho(t)}const fo=Symbol.for("v-fgt"),po=Symbol.for("v-txt"),go=Symbol.for("v-cmt"),vo=Symbol.for("v-stc"),mo=[];let yo=null;function _o(e=!1){mo.push(yo=e?null:[])}let bo=1;function wo(e){bo+=e}function xo(e){return e.dynamicChildren=bo>0?yo||s:null,mo.pop(),yo=mo[mo.length-1]||null,bo>0&&yo&&yo.push(e),e}function So(e,t,n,r,s,o){return xo(Ao(e,t,n,r,s,o,!0))}function ko(e,t,n,r,s){return xo($o(e,t,n,r,s,!0))}function To(e){return!!e&&!0===e.__v_isVNode}function Eo(e,t){return e.type===t.type&&e.key===t.key}const Co="__vInternal",Oo=({key:e})=>null!=e?e:null,Po=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?m(e)||An(e)||v(e)?{i:ur,r:e,k:t,f:!!n}:e:null);function Ao(e,t=null,n=null,r=0,s=null,o=(e===fo?0:1),i=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Oo(t),ref:t&&Po(t),scopeId:hr,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:r,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:ur};return a?(Mo(l,n),128&o&&e.normalize(l)):n&&(l.shapeFlag|=m(n)?8:16),bo>0&&!i&&yo&&(l.patchFlag>0||6&o)&&32!==l.patchFlag&&yo.push(l),l}const $o=function(e,t=null,n=null,r=0,s=null,o=!1){e&&e!==br||(e=go);if(To(e)){const r=jo(e,t,!0);return n&&Mo(r,n),bo>0&&!o&&yo&&(6&r.shapeFlag?yo[yo.indexOf(e)]=r:yo.push(r)),r.patchFlag|=-2,r}i=e,v(i)&&"__vccOpts"in i&&(e=e.__vccOpts);var i;if(t){t=function(e){return e?xn(e)||Co in e?c({},e):e:null}(t);let{class:e,style:n}=t;e&&!m(e)&&(t.class=Ie(e)),_(n)&&(xn(n)&&!f(n)&&(n=c({},n)),t.style=Be(n))}const a=m(e)?1:kr(e)?128:(e=>e.__isTeleport)(e)?64:_(e)?4:v(e)?2:0;return Ao(e,t,n,r,s,a,o,!0)};function jo(e,t,n=!1){const{props:r,ref:s,patchFlag:o,children:i}=e,a=t?Do(r||{},t):r;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&Oo(a),ref:t&&t.ref?n&&s?f(s)?s.concat(Po(t)):[s,Po(t)]:Po(t):s,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:i,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==fo?-1===o?16:16|o:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&jo(e.ssContent),ssFallback:e.ssFallback&&jo(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function Bo(e=" ",t=0){return $o(po,null,e,t)}function Io(e="",t=!1){return t?(_o(),ko(go,null,e)):$o(go,null,e)}function Ro(e){return null==e||"boolean"==typeof e?$o(go):f(e)?$o(fo,null,e.slice()):"object"==typeof e?Lo(e):$o(po,null,String(e))}function Lo(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:jo(e)}function Mo(e,t){let n=0;const{shapeFlag:r}=e;if(null==t)t=null;else if(f(t))n=16;else if("object"==typeof t){if(65&r){const n=t.default;return void(n&&(n._c&&(n._d=!1),Mo(e,n()),n._c&&(n._d=!0)))}{n=32;const r=t._;r||Co in t?3===r&&ur&&(1===ur.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=ur}}else v(t)?(t={default:t,_ctx:ur},n=32):(t=String(t),64&r?(n=16,t=[Bo(t)]):n=8);e.children=t,e.shapeFlag|=n}function Do(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const e in r)if("class"===e)t.class!==r.class&&(t.class=Ie([t.class,r.class]));else if("style"===e)t.style=Be([t.style,r.style]);else if(a(e)){const n=t[e],s=r[e];!s||n===s||f(n)&&n.includes(s)||(t[e]=n?[].concat(n,s):s)}else""!==e&&(t[e]=r[e])}return t}function No(e,t,n,r=null){Un(e,t,7,[n,r])}const Fo=Us();let Uo=0;let zo=null;const Ho=()=>zo||ur;let Wo,qo;{const e=D(),t=(t,n)=>{let r;return(r=e[t])||(r=e[t]=[]),r.push(n),e=>{r.length>1?r.forEach(t=>t(e)):r[0](e)}};Wo=t("__VUE_INSTANCE_SETTERS__",e=>zo=e),qo=t("__VUE_SSR_SETTERS__",e=>Yo=e)}const Vo=e=>{const t=zo;return Wo(e),e.scope.on(),()=>{e.scope.off(),Wo(t)}},Xo=()=>{zo&&zo.scope.off(),Wo(null)};function Ko(e){return 4&e.vnode.shapeFlag}let Yo=!1;function Jo(e,t,n){v(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:_(t)&&(e.setupState=Mn(t)),Go(e)}function Go(e,t,n){const r=e.type;e.render||(e.render=r.render||o);{const t=Vo(e);yt();try{As(e)}finally{_t(),t()}}}function Zo(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(Mn(kn(e.exposed)),{get:(t,n)=>n in t?t[n]:n in Ts?Ts[n](e):void 0,has:(e,t)=>t in e||t in Ts}))}function Qo(e,t=!0){return v(e)?e.displayName||e.name:e.name||t&&e.__name}const ei=(e,t)=>{const n=function(e,t,n=!1){let r,s;const i=v(e);return i?(r=e,s=o):(r=e.get,s=e.set),new Cn(r,s,i||!s,n)}(e,0,Yo);return n};function ti(e,t,n){const r=arguments.length;return 2===r?_(t)&&!f(t)?To(t)?$o(e,null,[t]):$o(e,t):$o(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):3===r&&To(n)&&(n=[n]),$o(e,t,n))}const ni="3.4.21",ri="undefined"!=typeof document?document:null,si=ri&&ri.createElement("template"),oi={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const s="svg"===t?ri.createElementNS("http://www.w3.org/2000/svg",e):"mathml"===t?ri.createElementNS("http://www.w3.org/1998/Math/MathML",e):n?ri.createElement(e,{is:n}):ri.createElement(e);return"select"===e&&r&&null!=r.multiple&&s.setAttribute("multiple",r.multiple),s},createText:e=>ri.createTextNode(e),createComment:e=>ri.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>ri.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,s,o){const i=n?n.previousSibling:t.lastChild;if(s&&(s===o||s.nextSibling))for(;t.insertBefore(s.cloneNode(!0),n),s!==o&&(s=s.nextSibling););else{si.innerHTML="svg"===r?`<svg>${e}</svg>`:"mathml"===r?`<math>${e}</math>`:e;const s=si.content;if("svg"===r||"mathml"===r){const e=s.firstChild;for(;e.firstChild;)s.appendChild(e.firstChild);s.removeChild(e)}t.insertBefore(s,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},ii="transition",ai="animation",li=Symbol("_vtc"),ci=(e,{slots:t})=>ti(Nr,function(e){const t={};for(const c in e)c in ui||(t[c]=e[c]);if(!1===e.css)return t;const{name:n="v",type:r,duration:s,enterFromClass:o=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=o,appearActiveClass:u=i,appearToClass:h=a,leaveFromClass:d=`${n}-leave-from`,leaveActiveClass:f=`${n}-leave-active`,leaveToClass:p=`${n}-leave-to`}=e,g=function(e){if(null==e)return null;if(_(e))return[fi(e.enter),fi(e.leave)];{const t=fi(e);return[t,t]}}(s),v=g&&g[0],m=g&&g[1],{onBeforeEnter:y,onEnter:b,onEnterCancelled:w,onLeave:x,onLeaveCancelled:S,onBeforeAppear:k=y,onAppear:T=b,onAppearCancelled:E=w}=t,C=(e,t,n)=>{gi(e,t?h:a),gi(e,t?u:i),n&&n()},O=(e,t)=>{e._isLeaving=!1,gi(e,d),gi(e,p),gi(e,f),t&&t()},P=e=>(t,n)=>{const s=e?T:b,i=()=>C(t,e,n);hi(s,[t,i]),vi(()=>{gi(t,e?l:o),pi(t,e?h:a),di(s)||yi(t,r,v,i)})};return c(t,{onBeforeEnter(e){hi(y,[e]),pi(e,o),pi(e,i)},onBeforeAppear(e){hi(k,[e]),pi(e,l),pi(e,u)},onEnter:P(!1),onAppear:P(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>O(e,t);pi(e,d),document.body.offsetHeight,pi(e,f),vi(()=>{e._isLeaving&&(gi(e,d),pi(e,p),di(x)||yi(e,r,m,n))}),hi(x,[e,n])},onEnterCancelled(e){C(e,!1),hi(w,[e])},onAppearCancelled(e){C(e,!0),hi(E,[e])},onLeaveCancelled(e){O(e),hi(S,[e])}})}(e),t);ci.displayName="Transition";const ui={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};ci.props=c({},Dr,ui);const hi=(e,t=[])=>{f(e)?e.forEach(e=>e(...t)):e&&e(...t)},di=e=>!!e&&(f(e)?e.some(e=>e.length>1):e.length>1);function fi(e){const t=(e=>{const t=m(e)?Number(e):NaN;return isNaN(t)?e:t})(e);return t}function pi(e,t){t.split(/\s+/).forEach(t=>t&&e.classList.add(t)),(e[li]||(e[li]=new Set)).add(t)}function gi(e,t){t.split(/\s+/).forEach(t=>t&&e.classList.remove(t));const n=e[li];n&&(n.delete(t),n.size||(e[li]=void 0))}function vi(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let mi=0;function yi(e,t,n,r){const s=e._endId=++mi,o=()=>{s===e._endId&&r()};if(n)return setTimeout(o,n);const{type:i,timeout:a,propCount:l}=function(e,t){const n=window.getComputedStyle(e),r=e=>(n[e]||"").split(", "),s=r(`${ii}Delay`),o=r(`${ii}Duration`),i=_i(s,o),a=r(`${ai}Delay`),l=r(`${ai}Duration`),c=_i(a,l);let u=null,h=0,d=0;t===ii?i>0&&(u=ii,h=i,d=o.length):t===ai?c>0&&(u=ai,h=c,d=l.length):(h=Math.max(i,c),u=h>0?i>c?ii:ai:null,d=u?u===ii?o.length:l.length:0);const f=u===ii&&/\b(transform|all)(,|$)/.test(r(`${ii}Property`).toString());return{type:u,timeout:h,propCount:d,hasTransform:f}}(e,t);if(!i)return r();const c=i+"end";let u=0;const h=()=>{e.removeEventListener(c,d),o()},d=t=>{t.target===e&&++u>=l&&h()};setTimeout(()=>{u<l&&h()},a+1),e.addEventListener(c,d)}function _i(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((t,n)=>bi(t)+bi(e[n])))}function bi(e){return"auto"===e?0:1e3*Number(e.slice(0,-1).replace(",","."))}const wi=Symbol("_vod"),xi=Symbol("_vsh"),Si={beforeMount(e,{value:t},{transition:n}){e[wi]="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):ki(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!=!n&&(r?t?(r.beforeEnter(e),ki(e,!0),r.enter(e)):r.leave(e,()=>{ki(e,!1)}):ki(e,t))},beforeUnmount(e,{value:t}){ki(e,t)}};function ki(e,t){e.style.display=t?e[wi]:"none",e[xi]=!t}const Ti=Symbol(""),Ei=/(^|;)\s*display\s*:/;const Ci=/\s*!important$/;function Oi(e,t,n){if(f(n))n.forEach(n=>Oi(e,t,n));else if(null==n&&(n=""),n=Di(n),t.startsWith("--"))e.setProperty(t,n);else{const r=function(e,t){const n=Ai[t];if(n)return n;let r=O(t);if("filter"!==r&&r in e)return Ai[t]=r;r=$(r);for(let s=0;s<Pi.length;s++){const n=Pi[s]+r;if(n in e)return Ai[t]=n}return t}(e,t);Ci.test(n)?e.setProperty(A(r),n.replace(Ci,""),"important"):e[r]=n}}const Pi=["Webkit","Moz","ms"],Ai={};const{unit:$i,unitRatio:ji,unitPrecision:Bi}={unit:"rem",unitRatio:10/320,unitPrecision:5},Ii=(Ri=$i,Li=ji,Mi=Bi,e=>e.replace(Ue,(e,t)=>{if(!t)return e;const n=function(e,t){const n=Math.pow(10,t+1),r=Math.floor(e*n);return 10*Math.round(r/10)/n}(parseFloat(t)*Li,Mi);return 0===n?"0":`${n}${Ri}`}));var Ri,Li,Mi;const Di=e=>m(e)?Ii(e):e,Ni="http://www.w3.org/1999/xlink";const Fi=Symbol("_vei");function Ui(e,t,n,r,s=null){const o=e[Fi]||(e[Fi]={}),i=o[t];if(r&&i)i.value=r;else{const[n,a]=function(e){let t;if(zi.test(e)){let n;for(t={};n=e.match(zi);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}const n=":"===e[2]?e.slice(3):A(e.slice(2));return[n,t]}(t);if(r){const i=o[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();const r=t&&t.proxy,s=r&&r.$nne,{value:o}=n;if(s&&f(o)){const n=Vi(e,o);for(let r=0;r<n.length;r++){const o=n[r];Un(o,t,5,o.__wwe?[e]:s(e))}return}Un(Vi(e,n.value),t,5,s&&!o.__wwe?s(e,o,t):[e])};return n.value=e,n.attached=qi(),n}(r,s);!function(e,t,n,r){e.addEventListener(t,n,r)}(e,n,i,a)}else i&&(!function(e,t,n,r){e.removeEventListener(t,n,r)}(e,n,i,a),o[t]=void 0)}}const zi=/(?:Once|Passive|Capture)$/;let Hi=0;const Wi=Promise.resolve(),qi=()=>Hi||(Wi.then(()=>Hi=0),Hi=Date.now());function Vi(e,t){if(f(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(e=>{const t=t=>!t._stopped&&e&&e(t);return t.__wwe=e.__wwe,t})}return t}const Xi=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123;const Ki=["ctrl","shift","alt","meta"],Yi={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>Ki.some(n=>e[`${n}Key`]&&!t.includes(n))},Ji=(e,t)=>{const n=e._withMods||(e._withMods={}),r=t.join(".");return n[r]||(n[r]=(n,...r)=>{for(let e=0;e<t.length;e++){const r=Yi[t[e]];if(r&&r(n,t))return}return e(n,...r)})},Gi=c({patchProp:(e,t,n,r,s,o,i,c,u)=>{if(0===t.indexOf("change:"))return function(e,t,n,r=null){if(!n||!r)return;const s=t.replace("change:",""),{attrs:o}=r,i=o[s],a=(e.__wxsProps||(e.__wxsProps={}))[s];if(a===i)return;e.__wxsProps[s]=i;const l=r.proxy;Zn(()=>{n(i,a,l.$gcd(l,!0),l.$gcd(l,!1))})}(e,t,r,i);const h="svg"===s;"class"===t?function(e,t,n){const{__wxsAddClass:r,__wxsRemoveClass:s}=e;s&&s.length&&(t=(t||"").split(/\s+/).filter(e=>-1===s.indexOf(e)).join(" "),s.length=0),r&&r.length&&(t=(t||"")+" "+r.join(" "));const o=e[li];o&&(t=(t?[t,...o]:[...o]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,r,h):"style"===t?function(e,t,n){const r=e.style,s=m(n);let o=!1;if(n&&!s){if(t)if(m(t))for(const e of t.split(";")){const t=e.slice(0,e.indexOf(":")).trim();null==n[t]&&Oi(r,t,"")}else for(const e in t)null==n[e]&&Oi(r,e,"");for(const e in n)"display"===e&&(o=!0),Oi(r,e,n[e])}else if(s){if(t!==n){const e=r[Ti];e&&(n+=";"+e),r.cssText=n,o=Ei.test(n)}}else t&&e.removeAttribute("style");wi in e&&(e[wi]=o?r.display:"",e[xi]&&(r.display="none"));const{__wxsStyle:i}=e;if(i)for(const a in i)Oi(r,a,i[a])}(e,n,r):a(t)?l(t)||Ui(e,t,0,r,i):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,r){if(r)return"innerHTML"===t||"textContent"===t||!!(t in e&&Xi(t)&&v(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){const t=e.tagName;if("IMG"===t||"VIDEO"===t||"CANVAS"===t||"SOURCE"===t)return!1}if(Xi(t)&&m(n))return!1;return t in e}(e,t,r,h))?function(e,t,n,r,s,o,i){if("innerHTML"===t||"textContent"===t)return r&&i(r,s,o),void(e[t]=null==n?"":n);const a=e.tagName;if("value"===t&&"PROGRESS"!==a&&!a.includes("-")){const r=null==n?"":n;return("OPTION"===a?e.getAttribute("value")||"":e.value)===r&&"_value"in e||(e.value=r),null==n&&e.removeAttribute(t),void(e._value=n)}let l=!1;if(""===n||null==n){const r=typeof e[t];"boolean"===r?n=V(n):null==n&&"string"===r?(n="",l=!0):"number"===r&&(n=0,l=!0)}try{e[t]=n}catch(c){}l&&e.removeAttribute(t)}(e,t,r,o,i,c,u):("true-value"===t?e._trueValue=r:"false-value"===t&&(e._falseValue=r),function(e,t,n,r){if(r&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(Ni,t.slice(6,t.length)):e.setAttributeNS(Ni,t,n);else{const r=q(t);null==n||r&&!V(n)?e.removeAttribute(t):e.setAttribute(t,r?"":n)}}(e,t,r,h))},forcePatchProp:(e,t)=>0===t.indexOf("change:")||("class"===t&&e.__wxsClassChanged?(e.__wxsClassChanged=!1,!0):!("style"!==t||!e.__wxsStyleChanged)&&(e.__wxsStyleChanged=!1,!0))},oi);let Zi;const Qi=(...e)=>{const t=(Zi||(Zi=ao(Gi))).createApp(...e),{mount:n}=t;return t.mount=e=>{const r=function(e){if(m(e)){return document.querySelector(e)}return e}
/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */(e);if(!r)return;const s=t._component;v(s)||s.render||s.template||(s.template=r.innerHTML),r.innerHTML="";const o=n(r,!1,function(e){if(e instanceof SVGElement)return"svg";if("function"==typeof MathMLElement&&e instanceof MathMLElement)return"mathml"}(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),o},t};const ea="undefined"!=typeof document;function ta(e){return"object"==typeof e||"displayName"in e||"props"in e||"__vccOpts"in e}const na=Object.assign;function ra(e,t){const n={};for(const r in t){const s=t[r];n[r]=oa(s)?s.map(e):e(s)}return n}const sa=()=>{},oa=Array.isArray,ia=/#/g,aa=/&/g,la=/\//g,ca=/=/g,ua=/\?/g,ha=/\+/g,da=/%5B/g,fa=/%5D/g,pa=/%5E/g,ga=/%60/g,va=/%7B/g,ma=/%7C/g,ya=/%7D/g,_a=/%20/g;function ba(e){return encodeURI(""+e).replace(ma,"|").replace(da,"[").replace(fa,"]")}function wa(e){return ba(e).replace(ha,"%2B").replace(_a,"+").replace(ia,"%23").replace(aa,"%26").replace(ga,"`").replace(va,"{").replace(ya,"}").replace(pa,"^")}function xa(e){return wa(e).replace(ca,"%3D")}function Sa(e){return null==e?"":function(e){return ba(e).replace(ia,"%23").replace(ua,"%3F")}(e).replace(la,"%2F")}function ka(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const Ta=/\/$/;function Ea(e,t,n="/"){let r,s={},o="",i="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(r=t.slice(0,l),o=t.slice(l+1,a>-1?a:t.length),s=e(o)),a>-1&&(r=r||t.slice(0,a),i=t.slice(a,t.length)),r=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),s=r[r.length-1];".."!==s&&"."!==s||r.push("");let o,i,a=n.length-1;for(o=0;o<r.length;o++)if(i=r[o],"."!==i){if(".."!==i)break;a>1&&a--}return n.slice(0,a).join("/")+"/"+r.slice(o).join("/")}(null!=r?r:t,n),{fullPath:r+(o&&"?")+o+i,path:r,query:s,hash:ka(i)}}function Ca(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function Oa(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Pa(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Aa(e[n],t[n]))return!1;return!0}function Aa(e,t){return oa(e)?$a(e,t):oa(t)?$a(t,e):e===t}function $a(e,t){return oa(t)?e.length===t.length&&e.every((e,n)=>e===t[n]):1===e.length&&e[0]===t}const ja={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Ba,Ia,Ra,La;function Ma(e){if(!e)if(ea){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(Ta,"")}(Ia=Ba||(Ba={})).pop="pop",Ia.push="push",(La=Ra||(Ra={})).back="back",La.forward="forward",La.unknown="";const Da=/^[^#]+#/;function Na(e,t){return e.replace(Da,"#")+t}const Fa=()=>({left:window.scrollX,top:window.scrollY});function Ua(e){let t;if("el"in e){const n=e.el,r="string"==typeof n&&n.startsWith("#"),s="string"==typeof n?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!s)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}(s,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function za(e,t){return(history.state?history.state.position-t:-1)+e}const Ha=new Map;function Wa(e,t){const{pathname:n,search:r,hash:s}=t,o=e.indexOf("#");if(o>-1){let t=s.includes(e.slice(o))?e.slice(o).length:1,n=s.slice(t);return"/"!==n[0]&&(n="/"+n),Ca(n,"")}return Ca(n,e)+r+s}function qa(e,t,n,r=!1,s=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:s?Fa():null}}function Va(e){const{history:t,location:n}=window,r={value:Wa(e,n)},s={value:t.state};function o(r,o,i){const a=e.indexOf("#"),l=a>-1?(n.host&&document.querySelector("base")?e:e.slice(a))+r:location.protocol+"//"+location.host+e+r;try{t[i?"replaceState":"pushState"](o,"",l),s.value=o}catch(c){console.error(c),n[i?"replace":"assign"](l)}}return s.value||o(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:r,state:s,push:function(e,n){const i=na({},s.value,t.state,{forward:e,scroll:Fa()});o(i.current,i,!0),o(e,na({},qa(r.value,e,null),{position:i.position+1},n),!1),r.value=e},replace:function(e,n){o(e,na({},t.state,qa(s.value.back,e,s.value.forward,!0),n,{position:s.value.position}),!0),r.value=e}}}function Xa(e){const t=Va(e=Ma(e)),n=function(e,t,n,r){let s=[],o=[],i=null;const a=({state:o})=>{const a=Wa(e,location),l=n.value,c=t.value;let u=0;if(o){if(n.value=a,t.value=o,i&&i===l)return void(i=null);u=c?o.position-c.position:0}else r(a);s.forEach(e=>{e(n.value,l,{delta:u,type:Ba.pop,direction:u?u>0?Ra.forward:Ra.back:Ra.unknown})})};function l(){const{history:e}=window;e.state&&e.replaceState(na({},e.state,{scroll:Fa()}),"")}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:function(){i=n.value},listen:function(e){s.push(e);const t=()=>{const t=s.indexOf(e);t>-1&&s.splice(t,1)};return o.push(t),t},destroy:function(){for(const e of o)e();o=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace);const r=na({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:Na.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function Ka(e){return"string"==typeof e||"symbol"==typeof e}const Ya=Symbol("");var Ja,Ga;function Za(e,t){return na(new Error,{type:e,[Ya]:!0},t)}function Qa(e,t){return e instanceof Error&&Ya in e&&(null==t||!!(e.type&t))}(Ga=Ja||(Ja={}))[Ga.aborted=4]="aborted",Ga[Ga.cancelled=8]="cancelled",Ga[Ga.duplicated=16]="duplicated";const el="[^/]+?",tl={sensitive:!1,strict:!1,start:!0,end:!0},nl=/[.+*?^${}()[\]/\\]/g;function rl(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function sl(e,t){let n=0;const r=e.score,s=t.score;for(;n<r.length&&n<s.length;){const e=rl(r[n],s[n]);if(e)return e;n++}if(1===Math.abs(s.length-r.length)){if(ol(r))return 1;if(ol(s))return-1}return s.length-r.length}function ol(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const il={type:0,value:""},al=/[a-zA-Z0-9_]/;function ll(e,t,n){const r=function(e,t){const n=na({},tl,t),r=[];let s=n.start?"^":"";const o=[];for(const l of e){const e=l.length?[]:[90];n.strict&&!l.length&&(s+="/");for(let t=0;t<l.length;t++){const r=l[t];let i=40+(n.sensitive?.25:0);if(0===r.type)t||(s+="/"),s+=r.value.replace(nl,"\\$&"),i+=40;else if(1===r.type){const{value:e,repeatable:n,optional:c,regexp:u}=r;o.push({name:e,repeatable:n,optional:c});const h=u||el;if(h!==el){i+=10;try{new RegExp(`(${h})`)}catch(a){throw new Error(`Invalid custom RegExp for param "${e}" (${h}): `+a.message)}}let d=n?`((?:${h})(?:/(?:${h}))*)`:`(${h})`;t||(d=c&&l.length<2?`(?:/${d})`:"/"+d),c&&(d+="?"),s+=d,i+=20,c&&(i+=-8),n&&(i+=-20),".*"===h&&(i+=-50)}e.push(i)}r.push(e)}if(n.strict&&n.end){const e=r.length-1;r[e][r[e].length-1]+=.7000000000000001}n.strict||(s+="/?"),n.end?s+="$":n.strict&&!s.endsWith("/")&&(s+="(?:/|$)");const i=new RegExp(s,n.sensitive?"":"i");return{re:i,score:r,keys:o,parse:function(e){const t=e.match(i),n={};if(!t)return null;for(let r=1;r<t.length;r++){const e=t[r]||"",s=o[r-1];n[s.name]=e&&s.repeatable?e.split("/"):e}return n},stringify:function(t){let n="",r=!1;for(const s of e){r&&n.endsWith("/")||(n+="/"),r=!1;for(const e of s)if(0===e.type)n+=e.value;else if(1===e.type){const{value:o,repeatable:i,optional:a}=e,l=o in t?t[o]:"";if(oa(l)&&!i)throw new Error(`Provided param "${o}" is an array but it is not repeatable (* or + modifiers)`);const c=oa(l)?l.join("/"):l;if(!c){if(!a)throw new Error(`Missing required param "${o}"`);s.length<2&&(n.endsWith("/")?n=n.slice(0,-1):r=!0)}n+=c}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[il]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${c}": ${e}`)}let n=0,r=n;const s=[];let o;function i(){o&&s.push(o),o=[]}let a,l=0,c="",u="";function h(){c&&(0===n?o.push({type:0,value:c}):1===n||2===n||3===n?(o.length>1&&("*"===a||"+"===a)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:c,regexp:u,repeatable:"*"===a||"+"===a,optional:"*"===a||"?"===a})):t("Invalid state to consume buffer"),c="")}function d(){c+=a}for(;l<e.length;)if(a=e[l++],"\\"!==a||2===n)switch(n){case 0:"/"===a?(c&&h(),i()):":"===a?(h(),n=1):d();break;case 4:d(),n=r;break;case 1:"("===a?n=2:al.test(a)?d():(h(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&l--);break;case 2:")"===a?"\\"==u[u.length-1]?u=u.slice(0,-1)+a:n=3:u+=a;break;case 3:h(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&l--,u="";break;default:t("Unknown state")}else r=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${c}"`),h(),i(),s}(e.path),n),s=na(r,{record:e,parent:t,children:[],alias:[]});return t&&!s.record.aliasOf==!t.record.aliasOf&&t.children.push(s),s}function cl(e,t){const n=[],r=new Map;function s(e,n,r){const a=!r,l=hl(e);l.aliasOf=r&&r.record;const c=gl(t,e),u=[l];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(hl(na({},l,{components:r?r.record.components:l.components,path:e,aliasOf:r?r.record:l})))}let h,d;for(const t of u){const{path:u}=t;if(n&&"/"!==u[0]){const e=n.record.path,r="/"===e[e.length-1]?"":"/";t.path=n.record.path+(u&&r+u)}if(h=ll(t,n,c),r?r.alias.push(h):(d=d||h,d!==h&&d.alias.push(h),a&&e.name&&!fl(h)&&o(e.name)),vl(h)&&i(h),l.children){const e=l.children;for(let t=0;t<e.length;t++)s(e[t],h,r&&r.children[t])}r=r||h}return d?()=>{o(d)}:sa}function o(e){if(Ka(e)){const t=r.get(e);t&&(r.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(o),t.alias.forEach(o))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&r.delete(e.record.name),e.children.forEach(o),e.alias.forEach(o))}}function i(e){const t=function(e,t){let n=0,r=t.length;for(;n!==r;){const s=n+r>>1;sl(e,t[s])<0?r=s:n=s+1}const s=function(e){let t=e;for(;t=t.parent;)if(vl(t)&&0===sl(e,t))return t;return}(e);s&&(r=t.lastIndexOf(s,r-1));return r}(e,n);n.splice(t,0,e),e.record.name&&!fl(e)&&r.set(e.record.name,e)}return t=gl({strict:!1,end:!0,sensitive:!1},t),e.forEach(e=>s(e)),{addRoute:s,resolve:function(e,t){let s,o,i,a={};if("name"in e&&e.name){if(s=r.get(e.name),!s)throw Za(1,{location:e});i=s.record.name,a=na(ul(t.params,s.keys.filter(e=>!e.optional).concat(s.parent?s.parent.keys.filter(e=>e.optional):[]).map(e=>e.name)),e.params&&ul(e.params,s.keys.map(e=>e.name))),o=s.stringify(a)}else if(null!=e.path)o=e.path,s=n.find(e=>e.re.test(o)),s&&(a=s.parse(o),i=s.record.name);else{if(s=t.name?r.get(t.name):n.find(e=>e.re.test(t.path)),!s)throw Za(1,{location:e,currentLocation:t});i=s.record.name,a=na({},t.params,e.params),o=s.stringify(a)}const l=[];let c=s;for(;c;)l.unshift(c.record),c=c.parent;return{name:i,path:o,params:a,matched:l,meta:pl(l)}},removeRoute:o,clearRoutes:function(){n.length=0,r.clear()},getRoutes:function(){return n},getRecordMatcher:function(e){return r.get(e)}}}function ul(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function hl(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:dl(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function dl(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]="object"==typeof n?n[r]:n;return t}function fl(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function pl(e){return e.reduce((e,t)=>na(e,t.meta),{})}function gl(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function vl({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function ml(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let r=0;r<n.length;++r){const e=n[r].replace(ha," "),s=e.indexOf("="),o=ka(s<0?e:e.slice(0,s)),i=s<0?null:ka(e.slice(s+1));if(o in t){let e=t[o];oa(e)||(e=t[o]=[e]),e.push(i)}else t[o]=i}return t}function yl(e){let t="";for(let n in e){const r=e[n];if(n=xa(n),null==r){void 0!==r&&(t+=(t.length?"&":"")+n);continue}(oa(r)?r.map(e=>e&&wa(e)):[r&&wa(r)]).forEach(e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))})}return t}function _l(e){const t={};for(const n in e){const r=e[n];void 0!==r&&(t[n]=oa(r)?r.map(e=>null==e?null:""+e):null==r?r:""+r)}return t}const bl=Symbol(""),wl=Symbol(""),xl=Symbol(""),Sl=Symbol(""),kl=Symbol("");function Tl(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function El(e,t,n,r,s,o=e=>e()){const i=r&&(r.enterCallbacks[s]=r.enterCallbacks[s]||[]);return()=>new Promise((a,l)=>{const c=e=>{var o;!1===e?l(Za(4,{from:n,to:t})):e instanceof Error?l(e):"string"==typeof(o=e)||o&&"object"==typeof o?l(Za(2,{from:t,to:e})):(i&&r.enterCallbacks[s]===i&&"function"==typeof e&&i.push(e),a())},u=o(()=>e.call(r&&r.instances[s],t,n,c));let h=Promise.resolve(u);e.length<3&&(h=h.then(c)),h.catch(e=>l(e))})}function Cl(e,t,n,r,s=e=>e()){const o=[];for(const i of e)for(const e in i.components){let a=i.components[e];if("beforeRouteEnter"===t||i.instances[e])if(ta(a)){const l=(a.__vccOpts||a)[t];l&&o.push(El(l,n,r,i,e,s))}else{let l=a();o.push(()=>l.then(o=>{if(!o)throw new Error(`Couldn't resolve component "${e}" at "${i.path}"`);const a=(l=o).__esModule||"Module"===l[Symbol.toStringTag]||l.default&&ta(l.default)?o.default:o;var l;i.mods[e]=o,i.components[e]=a;const c=(a.__vccOpts||a)[t];return c&&El(c,n,r,i,e,s)()}))}}return o}function Ol(e){const t=Vs(xl),n=Vs(Sl),r=ei(()=>{const n=Rn(e.to);return t.resolve(n)}),s=ei(()=>{const{matched:e}=r.value,{length:t}=e,s=e[t-1],o=n.matched;if(!s||!o.length)return-1;const i=o.findIndex(Oa.bind(null,s));if(i>-1)return i;const a=Al(e[t-2]);return t>1&&Al(s)===a&&o[o.length-1].path!==a?o.findIndex(Oa.bind(null,e[t-2])):i}),o=ei(()=>s.value>-1&&function(e,t){for(const n in t){const r=t[n],s=e[n];if("string"==typeof r){if(r!==s)return!1}else if(!oa(s)||s.length!==r.length||r.some((e,t)=>e!==s[t]))return!1}return!0}(n.params,r.value.params)),i=ei(()=>s.value>-1&&s.value===n.matched.length-1&&Pa(n.params,r.value.params));return{route:r,href:ei(()=>r.value.href),isActive:o,isExactActive:i,navigate:function(n={}){if(function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(n)){const n=t[Rn(e.replace)?"replace":"push"](Rn(e.to)).catch(sa);return e.viewTransition&&"undefined"!=typeof document&&"startViewTransition"in document&&document.startViewTransition(()=>n),n}return Promise.resolve()}}}const Pl=Vr({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Ol,setup(e,{slots:t}){const n=gn(Ol(e)),{options:r}=Vs(xl),s=ei(()=>({[$l(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[$l(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const r=t.default&&(1===(o=t.default(n)).length?o[0]:o);var o;return e.custom?r:ti("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:s.value},r)}}});function Al(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const $l=(e,t,n)=>null!=e?e:null!=t?t:n;function jl(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const Bl=Vr({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=Vs(kl),s=ei(()=>e.route||r.value),o=Vs(wl,0),i=ei(()=>{let e=Rn(o);const{matched:t}=s.value;let n;for(;(n=t[e])&&!n.components;)e++;return e}),a=ei(()=>s.value.matched[i.value]);qs(wl,ei(()=>i.value+1)),qs(bl,a),qs(kl,s);const l=$n();return Or(()=>[l.value,a.value,e.name],([e,t,n],[r,s,o])=>{t&&(t.instances[n]=e,s&&s!==t&&e&&e===r&&(t.leaveGuards.size||(t.leaveGuards=s.leaveGuards),t.updateGuards.size||(t.updateGuards=s.updateGuards))),!e||!t||s&&Oa(t,s)&&r||(t.enterCallbacks[n]||[]).forEach(t=>t(e))},{flush:"post"}),()=>{const r=s.value,o=e.name,i=a.value,c=i&&i.components[o];if(!c)return jl(n.default,{Component:c,route:r});const u=i.props[o],h=u?!0===u?r.params:"function"==typeof u?u(r):u:null,d=ti(c,na({},h,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(i.instances[o]=null)},ref:l}));return jl(n.default,{Component:d,route:r})||d}}});function Il(e){const t=cl(e.routes,e),n=e.parseQuery||ml,r=e.stringifyQuery||yl,s=e.history,o=Tl(),i=Tl(),a=Tl(),l=jn(ja);let c=ja;ea&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=ra.bind(null,e=>""+e),h=ra.bind(null,Sa),d=ra.bind(null,ka);function f(e,o){if(o=na({},o||l.value),"string"==typeof e){const r=Ea(n,e,o.path),i=t.resolve({path:r.path},o),a=s.createHref(r.fullPath);return na(r,i,{params:d(i.params),hash:ka(r.hash),redirectedFrom:void 0,href:a})}let i;if(null!=e.path)i=na({},e,{path:Ea(n,e.path,o.path).path});else{const t=na({},e.params);for(const e in t)null==t[e]&&delete t[e];i=na({},e,{params:h(t)}),o.params=h(o.params)}const a=t.resolve(i,o),c=e.hash||"";a.params=u(d(a.params));const f=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(r,na({},e,{hash:(p=c,ba(p).replace(va,"{").replace(ya,"}").replace(pa,"^")),path:a.path}));var p;const g=s.createHref(f);return na({fullPath:f,hash:c,query:r===yl?_l(e.query):e.query||{}},a,{redirectedFrom:void 0,href:g})}function p(e){return"string"==typeof e?Ea(n,e,l.value.path):na({},e)}function g(e,t){if(c!==e)return Za(8,{from:t,to:e})}function v(e){return y(e)}function m(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let r="function"==typeof n?n(e):n;return"string"==typeof r&&(r=r.includes("?")||r.includes("#")?r=p(r):{path:r},r.params={}),na({query:e.query,hash:e.hash,params:null!=r.path?{}:e.params},r)}}function y(e,t){const n=c=f(e),s=l.value,o=e.state,i=e.force,a=!0===e.replace,u=m(n);if(u)return y(na(p(u),{state:"object"==typeof u?na({},o,u.state):o,force:i,replace:a}),t||n);const h=n;let d;return h.redirectedFrom=t,!i&&function(e,t,n){const r=t.matched.length-1,s=n.matched.length-1;return r>-1&&r===s&&Oa(t.matched[r],n.matched[s])&&Pa(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(r,s,n)&&(d=Za(16,{to:h,from:s}),$(s,s,!0,!1)),(d?Promise.resolve(d):w(h,s)).catch(e=>Qa(e)?Qa(e,2)?e:A(e):P(e,h,s)).then(e=>{if(e){if(Qa(e,2))return y(na({replace:a},p(e.to),{state:"object"==typeof e.to?na({},o,e.to.state):o,force:i}),t||h)}else e=S(h,s,!0,a,o);return x(h,s,e),e})}function _(e,t){const n=g(e,t);return n?Promise.reject(n):Promise.resolve()}function b(e){const t=I.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function w(e,t){let n;const[r,s,a]=function(e,t){const n=[],r=[],s=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const o=t.matched[i];o&&(e.matched.find(e=>Oa(e,o))?r.push(o):n.push(o));const a=e.matched[i];a&&(t.matched.find(e=>Oa(e,a))||s.push(a))}return[n,r,s]}(e,t);n=Cl(r.reverse(),"beforeRouteLeave",e,t);for(const o of r)o.leaveGuards.forEach(r=>{n.push(El(r,e,t))});const l=_.bind(null,e,t);return n.push(l),L(n).then(()=>{n=[];for(const r of o.list())n.push(El(r,e,t));return n.push(l),L(n)}).then(()=>{n=Cl(s,"beforeRouteUpdate",e,t);for(const r of s)r.updateGuards.forEach(r=>{n.push(El(r,e,t))});return n.push(l),L(n)}).then(()=>{n=[];for(const r of a)if(r.beforeEnter)if(oa(r.beforeEnter))for(const s of r.beforeEnter)n.push(El(s,e,t));else n.push(El(r.beforeEnter,e,t));return n.push(l),L(n)}).then(()=>(e.matched.forEach(e=>e.enterCallbacks={}),n=Cl(a,"beforeRouteEnter",e,t,b),n.push(l),L(n))).then(()=>{n=[];for(const r of i.list())n.push(El(r,e,t));return n.push(l),L(n)}).catch(e=>Qa(e,8)?e:Promise.reject(e))}function x(e,t,n){a.list().forEach(r=>b(()=>r(e,t,n)))}function S(e,t,n,r,o){const i=g(e,t);if(i)return i;const a=t===ja,c=ea?history.state:{};n&&(r||a?s.replace(e.fullPath,na({scroll:a&&c&&c.scroll},o)):s.push(e.fullPath,o)),l.value=e,$(e,t,n,a),A()}let k;function T(){k||(k=s.listen((e,t,n)=>{if(!R.listening)return;const r=f(e),o=m(r);if(o)return void y(na(o,{replace:!0,force:!0}),r).catch(sa);c=r;const i=l.value;var a,u;ea&&(a=za(i.fullPath,n.delta),u=Fa(),Ha.set(a,u)),w(r,i).catch(e=>Qa(e,12)?e:Qa(e,2)?(y(na(p(e.to),{force:!0}),r).then(e=>{Qa(e,20)&&!n.delta&&n.type===Ba.pop&&s.go(-1,!1)}).catch(sa),Promise.reject()):(n.delta&&s.go(-n.delta,!1),P(e,r,i))).then(e=>{(e=e||S(r,i,!1))&&(n.delta&&!Qa(e,8)?s.go(-n.delta,!1):n.type===Ba.pop&&Qa(e,20)&&s.go(-1,!1)),x(r,i,e)}).catch(sa)}))}let E,C=Tl(),O=Tl();function P(e,t,n){A(e);const r=O.list();return r.length?r.forEach(r=>r(e,t,n)):console.error(e),Promise.reject(e)}function A(e){return E||(E=!e,T(),C.list().forEach(([t,n])=>e?n(e):t()),C.reset()),e}function $(t,n,r,s){const{scrollBehavior:o}=e;if(!ea||!o)return Promise.resolve();const i=!r&&function(e){const t=Ha.get(e);return Ha.delete(e),t}(za(t.fullPath,0))||(s||!r)&&history.state&&history.state.scroll||null;return Zn().then(()=>o(t,n,i)).then(e=>e&&Ua(e)).catch(e=>P(e,t,n))}const j=e=>s.go(e);let B;const I=new Set,R={currentRoute:l,listening:!0,addRoute:function(e,n){let r,s;return Ka(e)?(r=t.getRecordMatcher(e),s=n):s=e,t.addRoute(s,r)},removeRoute:function(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)},clearRoutes:t.clearRoutes,hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map(e=>e.record)},resolve:f,options:e,push:v,replace:function(e){return v(na(p(e),{replace:!0}))},go:j,back:()=>j(-1),forward:()=>j(1),beforeEach:o.add,beforeResolve:i.add,afterEach:a.add,onError:O.add,isReady:function(){return E&&l.value!==ja?Promise.resolve():new Promise((e,t)=>{C.add([e,t])})},install(e){e.component("RouterLink",Pl),e.component("RouterView",Bl),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>Rn(l)}),ea&&!B&&l.value===ja&&(B=!0,v(s.location).catch(e=>{}));const t={};for(const r in ja)Object.defineProperty(t,r,{get:()=>l.value[r],enumerable:!0});e.provide(xl,this),e.provide(Sl,vn(t)),e.provide(kl,l);const n=e.unmount;I.add(e),e.unmount=function(){I.delete(e),I.size<1&&(c=ja,k&&k(),k=null,l.value=ja,B=!1,E=!1),n()}}};function L(e){return e.reduce((e,t)=>e.then(()=>b(t)),Promise.resolve())}return R}function Rl(e){return Vs(Sl)}const Ll=["{","}"];const Ml=/^(?:\d)+/,Dl=/^(?:\w)+/;const Nl="zh-Hans",Fl="zh-Hant",Ul="en",zl="fr",Hl="es",Wl=Object.prototype.hasOwnProperty,ql=(e,t)=>Wl.call(e,t),Vl=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t,n=Ll){if(!t)return[e];let r=this._caches[e];return r||(r=function(e,[t,n]){const r=[];let s=0,o="";for(;s<e.length;){let i=e[s++];if(i===t){o&&r.push({type:"text",value:o}),o="";let t="";for(i=e[s++];void 0!==i&&i!==n;)t+=i,i=e[s++];const a=i===n,l=Ml.test(t)?"list":a&&Dl.test(t)?"named":"unknown";r.push({value:t,type:l})}else o+=i}return o&&r.push({type:"text",value:o}),r}(e,n),this._caches[e]=r),function(e,t){const n=[];let r=0;const s=Array.isArray(t)?"list":(o=t,null!==o&&"object"==typeof o?"named":"unknown");var o;if("unknown"===s)return n;for(;r<e.length;){const o=e[r];switch(o.type){case"text":n.push(o.value);break;case"list":n.push(t[parseInt(o.value,10)]);break;case"named":"named"===s&&n.push(t[o.value])}r++}return n}(r,t)}};function Xl(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return Nl;if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?Nl:e.indexOf("-hant")>-1?Fl:(n=e,["-tw","-hk","-mo","-cht"].find(e=>-1!==n.indexOf(e))?Fl:Nl);var n;let r=[Ul,zl,Hl];t&&Object.keys(t).length>0&&(r=Object.keys(t));const s=function(e,t){return t.find(t=>0===e.indexOf(t))}(e,r);return s||void 0}class Kl{constructor({locale:e,fallbackLocale:t,messages:n,watcher:r,formater:s}){this.locale=Ul,this.fallbackLocale=Ul,this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=s||Vl,this.messages=n||{},this.setLocale(e||Ul),r&&this.watchLocale(r)}setLocale(e){const t=this.locale;this.locale=Xl(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach(e=>{e(this.locale,t)})}getLocale(){return this.locale}watchLocale(e){const t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t,n=!0){const r=this.messages[e];r?n?Object.assign(r,t):Object.keys(t).forEach(e=>{ql(r,e)||(r[e]=t[e])}):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){let r=this.message;return"string"==typeof t?(t=Xl(t,this.messages))&&(r=this.messages[t]):n=t,ql(r,e)?this.formater.interpolate(r[e],n).join(""):(console.warn(`Cannot translate the value of keypath ${e}. Use the value of keypath as default.`),e)}}function Yl(e,t={},n,r){if("string"!=typeof e){const n=[t,e];e=n[0],t=n[1]}"string"!=typeof e&&(e="undefined"!=typeof uni&&Vh?Vh():"undefined"!=typeof global&&global.getLocale?global.getLocale():Ul),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||Ul);const s=new Kl({locale:e,fallbackLocale:n,messages:t,watcher:r});let o=(e,t)=>{{let e=!1;o=function(t,n){const r=Gp().$vm;return r&&(r.$locale,e||(e=!0,function(e,t){e.$watchLocale?e.$watchLocale(e=>{t.setLocale(e)}):e.$watch(()=>e.$locale,e=>{t.setLocale(e)})}(r,s))),s.t(t,n)}}return o(e,t)};return{i18n:s,f:(e,t,n)=>s.f(e,t,n),t:(e,t)=>o(e,t),add:(e,t,n=!0)=>s.add(e,t,n),watch:e=>s.watchLocale(e),getLocale:()=>s.getLocale(),setLocale:e=>s.setLocale(e)}}function Jl(e,t){return e.indexOf(t[0])>-1}const Gl=Me(()=>"undefined"!=typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length);let Zl;function Ql(e){return Jl(e,te)?nc().f(e,function(){const e=Vh(),t=__uniConfig.locales;return t[e]||t[__uniConfig.fallbackLocale]||t.en||{}}(),te):e}function ec(e,t){if(1===t.length){if(e){const n=e=>m(e)&&Jl(e,te),r=t[0];let s=[];if(f(e)&&(s=e.filter(e=>n(e[r]))).length)return s;const o=e[t[0]];if(n(o))return e}return}const n=t.shift();return ec(e&&e[n],t)}function tc(e,t){const n=ec(e,t);if(!n)return!1;const r=t[t.length-1];if(f(n))n.forEach(e=>tc(e,[r]));else{let e=n[r];Object.defineProperty(n,r,{get:()=>Ql(e),set(t){e=t}})}return!0}function nc(){if(!Zl){let e;if(e=navigator.cookieEnabled&&window.localStorage&&localStorage.UNI_LOCALE||__uniConfig.locale||navigator.language,Zl=Yl(e),Gl()){const t=Object.keys(__uniConfig.locales||{});t.length&&t.forEach(e=>Zl.add(e,__uniConfig.locales[e])),Zl.setLocale(e)}}return Zl}function rc(e,t,n){return t.reduce((t,r,s)=>(t[e+r]=n[s],t),{})}const sc=Me(()=>{const e="uni.async.",t=["error"];nc().add(Ul,rc(e,t,["The connection timed out, click the screen to try again."]),!1),nc().add(Hl,rc(e,t,["Se agotó el tiempo de conexión, haga clic en la pantalla para volver a intentarlo."]),!1),nc().add(zl,rc(e,t,["La connexion a expiré, cliquez sur l'écran pour réessayer."]),!1),nc().add(Nl,rc(e,t,["连接服务器超时，点击屏幕重试"]),!1),nc().add(Fl,rc(e,t,["連接服務器超時，點擊屏幕重試"]),!1)}),oc=Me(()=>{const e="uni.showActionSheet.",t=["cancel"];nc().add(Ul,rc(e,t,["Cancel"]),!1),nc().add(Hl,rc(e,t,["Cancelar"]),!1),nc().add(zl,rc(e,t,["Annuler"]),!1),nc().add(Nl,rc(e,t,["取消"]),!1),nc().add(Fl,rc(e,t,["取消"]),!1)}),ic=Me(()=>{const e="uni.showToast.",t=["unpaired"];nc().add(Ul,rc(e,t,["Please note showToast must be paired with hideToast"]),!1),nc().add(Hl,rc(e,t,["Tenga en cuenta que showToast debe estar emparejado con hideToast"]),!1),nc().add(zl,rc(e,t,["Veuillez noter que showToast doit être associé à hideToast"]),!1),nc().add(Nl,rc(e,t,["请注意 showToast 与 hideToast 必须配对使用"]),!1),nc().add(Fl,rc(e,t,["請注意 showToast 與 hideToast 必須配對使用"]),!1)}),ac=Me(()=>{const e="uni.showLoading.",t=["unpaired"];nc().add(Ul,rc(e,t,["Please note showLoading must be paired with hideLoading"]),!1),nc().add(Hl,rc(e,t,["Tenga en cuenta que showLoading debe estar emparejado con hideLoading"]),!1),nc().add(zl,rc(e,t,["Veuillez noter que showLoading doit être associé à hideLoading"]),!1),nc().add(Nl,rc(e,t,["请注意 showLoading 与 hideLoading 必须配对使用"]),!1),nc().add(Fl,rc(e,t,["請注意 showLoading 與 hideLoading 必須配對使用"]),!1)}),lc=Me(()=>{const e="uni.chooseFile.",t=["notUserActivation"];nc().add(Ul,rc(e,t,["File chooser dialog can only be shown with a user activation"]),!1),nc().add(Hl,rc(e,t,["El cuadro de diálogo del selector de archivos solo se puede mostrar con la activación del usuario"]),!1),nc().add(zl,rc(e,t,["La boîte de dialogue du sélecteur de fichier ne peut être affichée qu'avec une activation par l'utilisateur"]),!1),nc().add(Nl,rc(e,t,["文件选择器对话框只能在由用户激活时显示"]),!1),nc().add(Fl,rc(e,t,["文件選擇器對話框只能在由用戶激活時顯示"]),!1)});function cc(e){const t=new tt;return{on:(e,n)=>t.on(e,n),once:(e,n)=>t.once(e,n),off:(e,n)=>t.off(e,n),emit:(e,...n)=>t.emit(e,...n),subscribe(n,r,s=!1){t[s?"once":"on"](`${e}.${n}`,r)},unsubscribe(n,r){t.off(`${e}.${n}`,r)},subscribeHandler(n,r,s){t.emit(`${e}.${n}`,r,s)}}}const uc="invokeViewApi",hc="invokeServiceApi";let dc=1;const fc=Object.create(null);function pc(e,t){return e+"."+t}function gc({id:e,name:t,args:n},r){t=pc(r,t);const s=t=>{e&&jv.publishHandler(uc+"."+e,t)},o=fc[t];o?o(n,s):s({})}const vc=c(cc("service"),{invokeServiceMethod:(e,t,n)=>{const{subscribe:r,publishHandler:s}=jv,o=n?dc++:0;n&&r(hc+"."+o,n,!0),s(hc,{id:o,name:e,args:t})}}),mc=ze(!0);let yc;function _c(){yc&&(clearTimeout(yc),yc=null)}let bc=0,wc=0;function xc(e){if(_c(),1!==e.touches.length)return;const{pageX:t,pageY:n}=e.touches[0];bc=t,wc=n,yc=setTimeout(function(){const t=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget});t.touches=e.touches,t.changedTouches=e.changedTouches,e.target.dispatchEvent(t)},350)}function Sc(e){if(!yc)return;if(1!==e.touches.length)return _c();const{pageX:t,pageY:n}=e.touches[0];return Math.abs(t-bc)>10||Math.abs(n-wc)>10?_c():void 0}function kc(e,t){const n=Number(e);return isNaN(n)?t:n}function Tc(){const e=__uniConfig.globalStyle||{},t=kc(e.rpxCalcMaxDeviceWidth,960),n=kc(e.rpxCalcBaseDeviceWidth,375);function r(){let e=function(){const e=/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation,t=e&&90===Math.abs(window.orientation);var n=e?Math[t?"max":"min"](screen.width,screen.height):screen.width;return Math.min(window.innerWidth,document.documentElement.clientWidth,n)||n}();e=e<=t?e:n,document.documentElement.style.fontSize=e/23.4375+"px"}r(),document.addEventListener("DOMContentLoaded",r),window.addEventListener("load",r),window.addEventListener("resize",r)}function Ec(){Tc(),Ne(),window.addEventListener("touchstart",xc,mc),window.addEventListener("touchmove",Sc,mc),window.addEventListener("touchend",_c,mc),window.addEventListener("touchcancel",_c,mc)}function Cc(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Oc,Pc,Ac=["top","left","right","bottom"],$c={};function jc(){return Pc="CSS"in window&&"function"==typeof CSS.supports?CSS.supports("top: env(safe-area-inset-top)")?"env":CSS.supports("top: constant(safe-area-inset-top)")?"constant":"":""}function Bc(){if(Pc="string"==typeof Pc?Pc:jc()){var e=[],t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t={passive:!0}}});window.addEventListener("test",null,n)}catch(a){}var r=document.createElement("div");s(r,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),Ac.forEach(function(e){i(r,e)}),document.body.appendChild(r),o(),Oc=!0}else Ac.forEach(function(e){$c[e]=0});function s(e,t){var n=e.style;Object.keys(t).forEach(function(e){var r=t[e];n[e]=r})}function o(t){t?e.push(t):e.forEach(function(e){e()})}function i(e,n){var r=document.createElement("div"),i=document.createElement("div"),a=document.createElement("div"),l=document.createElement("div"),c={position:"absolute",width:"100px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:Pc+"(safe-area-inset-"+n+")"};s(r,c),s(i,c),s(a,{transition:"0s",animation:"none",width:"400px",height:"400px"}),s(l,{transition:"0s",animation:"none",width:"250%",height:"250%"}),r.appendChild(a),i.appendChild(l),e.appendChild(r),e.appendChild(i),o(function(){r.scrollTop=i.scrollTop=1e4;var e=r.scrollTop,s=i.scrollTop;function o(){this.scrollTop!==(this===r?e:s)&&(r.scrollTop=i.scrollTop=1e4,e=r.scrollTop,s=i.scrollTop,function(e){Rc.length||setTimeout(function(){var e={};Rc.forEach(function(t){e[t]=$c[t]}),Rc.length=0,Lc.forEach(function(t){t(e)})},0);Rc.push(e)}(n))}r.addEventListener("scroll",o,t),i.addEventListener("scroll",o,t)});var u=getComputedStyle(r);Object.defineProperty($c,n,{configurable:!0,get:function(){return parseFloat(u.paddingBottom)}})}}function Ic(e){return Oc||Bc(),$c[e]}var Rc=[];var Lc=[];const Mc=Cc({get support(){return 0!=("string"==typeof Pc?Pc:jc()).length},get top(){return Ic("top")},get left(){return Ic("left")},get right(){return Ic("right")},get bottom(){return Ic("bottom")},onChange:function(e){jc()&&(Oc||Bc(),"function"==typeof e&&Lc.push(e))},offChange:function(e){var t=Lc.indexOf(e);t>=0&&Lc.splice(t,1)}}),Dc=Ji(()=>{},["prevent"]);function Nc(e,t){return parseInt((e.getPropertyValue(t).match(/\d+/)||["0"])[0])}function Fc(){const e=Nc(document.documentElement.style,"--window-top");return e?e+Mc.top:0}function Uc(e){const t=document.documentElement.style;Object.keys(e).forEach(n=>{t.setProperty(n,e[n])})}function zc(e){return Uc(e)}function Hc(e){return Symbol(e)}function Wc(e){return-1!==(e+="").indexOf("rpx")||-1!==e.indexOf("upx")}function qc(e,t=!1){if(t)return function(e){if(!Wc(e))return e;return e.replace(/(\d+(\.\d+)?)[ru]px/g,(e,t)=>zh(parseFloat(t))+"px")}(e);if(m(e)){const t=parseInt(e)||0;return Wc(e)?zh(t):t}return e}const Vc="M1.952 18.080q-0.32-0.352-0.416-0.88t0.128-0.976l0.16-0.352q0.224-0.416 0.64-0.528t0.8 0.176l6.496 4.704q0.384 0.288 0.912 0.272t0.88-0.336l17.312-14.272q0.352-0.288 0.848-0.256t0.848 0.352l-0.416-0.416q0.32 0.352 0.32 0.816t-0.32 0.816l-18.656 18.912q-0.32 0.352-0.8 0.352t-0.8-0.32l-7.936-8.064z",Xc="M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM15.136 8.672h1.728q0.128 0 0.224 0.096t0.096 0.256l-0.384 10.24q0 0.064-0.048 0.112t-0.112 0.048h-1.248q-0.096 0-0.144-0.048t-0.048-0.112l-0.384-10.24q0-0.16 0.096-0.256t0.224-0.096zM16 23.328q-0.48 0-0.832-0.352t-0.352-0.848 0.352-0.848 0.832-0.352 0.832 0.352 0.352 0.848-0.352 0.848-0.832 0.352z",Kc="M21.781 7.844l-9.063 8.594 9.063 8.594q0.25 0.25 0.25 0.609t-0.25 0.578q-0.25 0.25-0.578 0.25t-0.578-0.25l-9.625-9.125q-0.156-0.125-0.203-0.297t-0.047-0.359q0-0.156 0.047-0.328t0.203-0.297l9.625-9.125q0.25-0.25 0.578-0.25t0.578 0.25q0.25 0.219 0.25 0.578t-0.25 0.578z";function Yc(e,t="#000",n=27){return $o("svg",{width:n,height:n,viewBox:"0 0 32 32"},[$o("path",{d:e,fill:t},null,8,["d","fill"])],8,["width","height"])}function Jc(){{const{$pageInstance:e}=Ho();return e&&e.proxy.$page.id}}function Gc(){const e=Tp(),t=e.length;if(t)return e[t-1]}function Zc(){const e=Gc();if(e)return e.$page.meta}function Qc(){const e=Zc();return e?e.id:-1}function eu(){const e=Gc();if(e)return e.$vm}const tu=["navigationBar","pullToRefresh"];function nu(e,t){const n=JSON.parse(JSON.stringify(__uniConfig.globalStyle||{})),r=c({id:t},n,e);tu.forEach(t=>{r[t]=c({},n[t],e[t])});const{navigationBar:s}=r;return s.titleText&&s.titleImage&&(s.titleText=""),r}function ru(e,t,n){if(m(e))n=t,t=e,e=eu();else if("number"==typeof e){const t=Tp().find(t=>t.$page.id===e);e=t?t.$vm:eu()}if(!e)return;const r=e.$[t];return r&&((e,t)=>{let n;for(let r=0;r<e.length;r++)n=e[r](t);return n})(r,n)}function su(e){e.preventDefault()}let ou,iu=0;function au({onPageScroll:e,onReachBottom:t,onReachBottomDistance:n}){let r=!1,s=!1,o=!0;const i=()=>{function i(){if((()=>{const{scrollHeight:e}=document.documentElement,t=window.innerHeight,r=window.scrollY,o=r>0&&e>t&&r+t+n>=e,i=Math.abs(e-iu)>n;return!o||s&&!i?(!o&&s&&(s=!1),!1):(iu=e,s=!0,!0)})())return t&&t(),o=!1,setTimeout(function(){o=!0},350),!0}e&&e(window.pageYOffset),t&&o&&(i()||(ou=setTimeout(i,300))),r=!1};return function(){clearTimeout(ou),r||requestAnimationFrame(i),r=!0}}function lu(e,t){if(0===t.indexOf("/"))return t;if(0===t.indexOf("./"))return lu(e,t.slice(2));const n=t.split("/"),r=n.length;let s=0;for(;s<r&&".."===n[s];s++);n.splice(0,s),t=n.join("/");const o=e.length>0?e.split("/"):[];return o.splice(o.length-s-1,s+1),Le(o.concat(n).join("/"))}function cu(e,t=!1){return t?__uniRoutes.find(t=>t.path===e||t.alias===e):__uniRoutes.find(t=>t.path===e)}class uu{constructor(e){this.$bindClass=!1,this.$bindStyle=!1,this.$vm=e,this.$el=function(e,t=!1){const{vnode:n}=e;if(je(n.el))return t?n.el?[n.el]:[]:n.el;const{subTree:r}=e;if(16&r.shapeFlag){const e=r.children.filter(e=>e.el&&je(e.el));if(e.length>0)return t?e.map(e=>e.el):e[0].el}return t?n.el?[n.el]:[]:n.el}(e.$),this.$el.getAttribute&&(this.$bindClass=!!this.$el.getAttribute("class"),this.$bindStyle=!!this.$el.getAttribute("style"))}selectComponent(e){if(!this.$el||!e)return;const t=pu(this.$el.querySelector(e));return t?hu(t,!1):void 0}selectAllComponents(e){if(!this.$el||!e)return[];const t=[],n=this.$el.querySelectorAll(e);for(let r=0;r<n.length;r++){const e=pu(n[r]);e&&t.push(hu(e,!1))}return t}forceUpdate(e){"class"===e?this.$bindClass?(this.$el.__wxsClassChanged=!0,this.$vm.$forceUpdate()):this.updateWxsClass():"style"===e&&(this.$bindStyle?(this.$el.__wxsStyleChanged=!0,this.$vm.$forceUpdate()):this.updateWxsStyle())}updateWxsClass(){const{__wxsAddClass:e}=this.$el;e.length&&(this.$el.className=e.join(" "))}updateWxsStyle(){const{__wxsStyle:e}=this.$el;e&&this.$el.setAttribute("style",function(e){let t="";if(!e||m(e))return t;for(const n in e){const r=e[n],s=n.startsWith("--")?n:A(n);(m(r)||"number"==typeof r)&&(t+=`${s}:${r};`)}return t}(e))}setStyle(e){return this.$el&&e?(m(e)&&(e=H(e)),S(e)&&(this.$el.__wxsStyle=e,this.forceUpdate("style")),this):this}addClass(e){if(!this.$el||!e)return this;const t=this.$el.__wxsAddClass||(this.$el.__wxsAddClass=[]);return-1===t.indexOf(e)&&(t.push(e),this.forceUpdate("class")),this}removeClass(e){if(!this.$el||!e)return this;const{__wxsAddClass:t}=this.$el;if(t){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const n=this.$el.__wxsRemoveClass||(this.$el.__wxsRemoveClass=[]);return-1===n.indexOf(e)&&(n.push(e),this.forceUpdate("class")),this}hasClass(e){return this.$el&&this.$el.classList.contains(e)}getDataset(){return this.$el&&this.$el.dataset}callMethod(e,t={}){const n=this.$vm[e];v(n)?n(JSON.parse(JSON.stringify(t))):this.$vm.ownerId&&jv.publishHandler("onWxsInvokeCallMethod",{nodeId:this.$el.__id,ownerId:this.$vm.ownerId,method:e,args:t})}requestAnimationFrame(e){return window.requestAnimationFrame(e)}getState(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}triggerEvent(e,t={}){return this.$vm.$emit(e,t),this}getComputedStyle(e){if(this.$el){const t=window.getComputedStyle(this.$el);return e&&e.length?e.reduce((e,n)=>(e[n]=t[n],e),{}):t}return{}}setTimeout(e,t){return window.setTimeout(e,t)}clearTimeout(e){return window.clearTimeout(e)}getBoundingClientRect(){return this.$el.getBoundingClientRect()}}function hu(e,t=!0){if(t&&e&&(e=$e(e.$)),e&&e.$el)return e.$el.__wxsComponentDescriptor||(e.$el.__wxsComponentDescriptor=new uu(e)),e.$el.__wxsComponentDescriptor}function du(e,t){return hu(e,t)}function fu(e,t,n,r=!0){if(t){e.__instance||(e.__instance=!0,Object.defineProperty(e,"instance",{get:()=>du(n.proxy,!1)}));const s=function(e,t,n=!0){if(!t)return!1;if(n&&e.length<2)return!1;const r=$e(t);if(!r)return!1;const s=r.$.type;return!(!s.$wxs&&!s.$renderjs)&&r}(t,n,r);if(s)return[e,du(s,!1)]}}function pu(e){if(e)return e.__vueParentComponent&&e.__vueParentComponent.proxy}function gu(e,t=!1){const{type:n,timeStamp:r,target:s,currentTarget:o}=e;let i,a;i=He(t?s:function(e){for(;e&&0!==e.tagName.indexOf("UNI-");)e=e.parentElement;return e}(s)),a=He(o);const l={type:n,timeStamp:r,target:i,detail:{},currentTarget:a};return e._stopped&&(l._stopped=!0),e.type.startsWith("touch")&&(l.touches=e.touches,l.changedTouches=e.changedTouches),function(e,t){c(e,{preventDefault:()=>t.preventDefault(),stopPropagation:()=>t.stopPropagation()})}(l,e),l}function vu(e,t){return{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY-t,pageX:e.pageX,pageY:e.pageY-t}}function mu(e,t){const n=[];for(let r=0;r<e.length;r++){const{identifier:s,pageX:o,pageY:i,clientX:a,clientY:l,force:c}=e[r];n.push({identifier:s,pageX:o,pageY:i-t,clientX:a,clientY:l-t,force:c||0})}return n}const yu=Object.defineProperty({__proto__:null,$nne:function(e,t,n){const{currentTarget:r}=e;if(!(e instanceof Event&&r instanceof HTMLElement))return[e];const s=0!==r.tagName.indexOf("UNI-");if(s)return fu(e,t,n,!1)||[e];const o=gu(e,s);if("click"===e.type)!function(e,t){const{x:n,y:r}=t,s=Fc();e.detail={x:n,y:r-s},e.touches=e.changedTouches=[vu(t,s)]}(o,e);else if((e=>0===e.type.indexOf("mouse")||["contextmenu"].includes(e.type))(e))!function(e,t){const n=Fc();e.pageX=t.pageX,e.pageY=t.pageY-n,e.clientX=t.clientX,e.clientY=t.clientY-n,e.touches=e.changedTouches=[vu(t,n)]}(o,e);else if((e=>"undefined"!=typeof TouchEvent&&e instanceof TouchEvent||0===e.type.indexOf("touch")||["longpress"].indexOf(e.type)>=0)(e)){const t=Fc();o.touches=mu(e.touches,t),o.changedTouches=mu(e.changedTouches,t)}else if((e=>!e.type.indexOf("key")&&e instanceof KeyboardEvent)(e)){["key","code"].forEach(t=>{Object.defineProperty(o,t,{get:()=>e[t]})})}return fu(o,t,n)||[o]},createNativeEvent:gu},Symbol.toStringTag,{value:"Module"});function _u(e){!function(e){const t=e.globalProperties;c(t,yu),t.$gcd=du}(e._context.config)}let bu=1;function wu(e){return(e||Qc())+"."+uc}const xu=c(cc("view"),{invokeOnCallback:(e,t)=>Bv.emit("api."+e,t),invokeViewMethod:(e,t,n,r)=>{const{subscribe:s,publishHandler:o}=Bv,i=r?bu++:0;r&&s(uc+"."+i,r,!0),o(wu(n),{id:i,name:e,args:t},n)},invokeViewMethodKeepAlive:(e,t,n,r)=>{const{subscribe:s,unsubscribe:o,publishHandler:i}=Bv,a=bu++,l=uc+"."+a;return s(l,n),i(wu(r),{id:a,name:e,args:t},r),()=>{o(l)}}});function Su(e){ru(Gc(),ge,e),Bv.invokeOnCallback("onWindowResize",e)}function ku(e){const t=Gc();ru(Gp(),se,e),ru(t,se)}function Tu(){ru(Gp(),oe),ru(Gc(),oe)}const Eu=[me,_e];function Cu(){Eu.forEach(e=>Bv.subscribe(e,function(e){return(t,n)=>{ru(parseInt(n),e,t)}}(e)))}function Ou(){!function(){const{on:e}=Bv;e(ge,Su),e(Pe,ku),e(Ae,Tu)}(),Cu()}function Pu(){if(this.$route){const e=this.$route.meta;return e.eventChannel||(e.eventChannel=new Ye(this.$page.id)),e.eventChannel}}function Au(e){e._context.config.globalProperties.getOpenerEventChannel=Pu}function $u(){return{path:"",query:{},scene:1001,referrerInfo:{appId:"",extraData:{}}}}function ju(e){return/^-?\d+[ur]px$/i.test(e)?e.replace(/(^-?\d+)[ur]px$/i,(e,t)=>`${zh(parseFloat(t))}px`):/^-?[\d\.]+$/.test(e)?`${e}px`:e||""}function Bu(e){const t=e.animation;if(!t||!t.actions||!t.actions.length)return;let n=0;const r=t.actions,s=t.actions.length;function o(){const t=r[n],i=t.option.transition,a=function(e){const t=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],r=["opacity","background-color"],s=["width","height","left","right","top","bottom"],o=e.animates,i=e.option,a=i.transition,l={},c=[];return o.forEach(e=>{let o=e.type,i=[...e.args];if(t.concat(n).includes(o))o.startsWith("rotate")||o.startsWith("skew")?i=i.map(e=>parseFloat(e)+"deg"):o.startsWith("translate")&&(i=i.map(ju)),n.indexOf(o)>=0&&(i.length=1),c.push(`${o}(${i.join(",")})`);else if(r.concat(s).includes(i[0])){o=i[0];const e=i[1];l[o]=s.includes(o)?ju(e):e}}),l.transform=l.webkitTransform=c.join(" "),l.transition=l.webkitTransition=Object.keys(l).map(e=>`${function(e){return e.replace(/[A-Z]/g,e=>`-${e.toLowerCase()}`).replace("webkit","-webkit")}(e)} ${a.duration}ms ${a.timingFunction} ${a.delay}ms`).join(","),l.transformOrigin=l.webkitTransformOrigin=i.transformOrigin,l}(t);Object.keys(a).forEach(t=>{e.$el.style[t]=a[t]}),n+=1,n<s&&setTimeout(o,i.duration+i.delay)}setTimeout(()=>{o()},0)}const Iu={props:["animation"],watch:{animation:{deep:!0,handler(){Bu(this)}}},mounted(){Bu(this)}},Ru=e=>{e.__reserved=!0;const{props:t,mixins:n}=e;return t&&t.animation||(n||(e.mixins=[])).push(Iu),Lu(e)},Lu=e=>(e.__reserved=!0,e.compatConfig={MODE:3},Vr(e));function Mu(e){return e.__wwe=!0,e}function Du(e,t){return(n,r,s)=>{e.value&&t(n,function(e,t,n,r){let s;return s=He(n),{type:r.type||e,timeStamp:t.timeStamp||0,target:s,currentTarget:s,detail:r}}(n,r,e.value,s||{}))}}const Nu={hoverClass:{type:String,default:"none"},hoverStopPropagation:{type:Boolean,default:!1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:400}};function Fu(e){const t=$n(!1);let n,r,s=!1;function o(){requestAnimationFrame(()=>{clearTimeout(r),r=setTimeout(()=>{t.value=!1},parseInt(e.hoverStayTime))})}function i(r){r._hoverPropagationStopped||e.hoverClass&&"none"!==e.hoverClass&&!e.disabled&&(e.hoverStopPropagation&&(r._hoverPropagationStopped=!0),s=!0,n=setTimeout(()=>{t.value=!0,s||o()},parseInt(e.hoverStartTime)))}function a(){s=!1,t.value&&o()}function l(){a(),window.removeEventListener("mouseup",l)}return{hovering:t,binding:{onTouchstartPassive:Mu(function(e){e.touches.length>1||i(e)}),onMousedown:Mu(function(e){s||(i(e),window.addEventListener("mouseup",l))}),onTouchend:Mu(function(){a()}),onMouseup:Mu(function(){s&&l()}),onTouchcancel:Mu(function(){s=!1,t.value=!1,clearTimeout(n)})}}}function Uu(e,t){return m(t)&&(t=[t]),t.reduce((t,n)=>(e[n]&&(t[n]=!0),t),Object.create(null))}const zu=Hc("uf"),Hu=Hc("ul");function Wu(e,t,n){const r=Jc();n&&!e||S(t)&&Object.keys(t).forEach(s=>{n?0!==s.indexOf("@")&&0!==s.indexOf("uni-")&&jv.on(`uni-${s}-${r}-${e}`,t[s]):0===s.indexOf("uni-")?jv.on(s,t[s]):e&&jv.on(`uni-${s}-${r}-${e}`,t[s])})}function qu(e,t,n){const r=Jc();n&&!e||S(t)&&Object.keys(t).forEach(s=>{n?0!==s.indexOf("@")&&0!==s.indexOf("uni-")&&jv.off(`uni-${s}-${r}-${e}`,t[s]):0===s.indexOf("uni-")?jv.off(s,t[s]):e&&jv.off(`uni-${s}-${r}-${e}`,t[s])})}const Vu=Ru({name:"Button",props:{id:{type:String,default:""},hoverClass:{type:String,default:"button-hover"},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:70},hoverStopPropagation:{type:Boolean,default:!1},disabled:{type:[Boolean,String],default:!1},formType:{type:String,default:""},openType:{type:String,default:""},loading:{type:[Boolean,String],default:!1},plain:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=$n(null),r=Vs(zu,!1),{hovering:s,binding:o}=Fu(e),i=Mu((t,s)=>{if(e.disabled)return t.stopImmediatePropagation();s&&n.value.click();const o=e.formType;if(o){if(!r)return;"submit"===o?r.submit(t):"reset"===o&&r.reset(t)}else;}),a=Vs(Hu,!1);return a&&(a.addHandler(i),gs(()=>{a.removeHandler(i)})),function(e,t){Wu(e.id,t),Or(()=>e.id,(e,n)=>{qu(n,t,!0),Wu(e,t,!0)}),vs(()=>{qu(e.id,t)})}(e,{"label-click":i}),()=>{const r=e.hoverClass,a=Uu(e,"disabled"),l=Uu(e,"loading"),c=Uu(e,"plain"),u=r&&"none"!==r;return $o("uni-button",Do({ref:n,onClick:i,id:e.id,class:u&&s.value?r:""},u&&o,a,l,c),[t.default&&t.default()],16,["onClick","id"])}}});function Xu(e){const{base:t}=__uniConfig.router;return 0===Le(e).indexOf(t)?Le(e):t+e}function Ku(e){const{base:t,assets:n}=__uniConfig.router;if("./"===t&&(0!==e.indexOf("./")||!e.includes("/static/")&&0!==e.indexOf("./"+(n||"assets")+"/")||(e=e.slice(1))),0===e.indexOf("/")){if(0!==e.indexOf("//"))return Xu(e.slice(1));e="https:"+e}if(ne.test(e)||re.test(e)||0===e.indexOf("blob:"))return e;const r=Tp();return r.length?Xu(lu(r[r.length-1].$page.route,e).slice(1)):e}const Yu=navigator.userAgent,Ju=/android/i.test(Yu),Gu=/iphone|ipad|ipod/i.test(Yu),Zu=Yu.match(/Windows NT ([\d|\d.\d]*)/i),Qu=/Macintosh|Mac/i.test(Yu),eh=/Linux|X11/i.test(Yu),th=Qu&&navigator.maxTouchPoints>0;function nh(){return/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation}function rh(e){return e&&90===Math.abs(window.orientation)}function sh(e,t){return e?Math[t?"max":"min"](screen.width,screen.height):screen.width}function oh(e){return Math.min(window.innerWidth,document.documentElement.clientWidth,e)||e}const ih=["original","compressed"],ah=["album","camera"];function lh(e,t){return!f(e)||0===e.length||e.find(e=>-1===t.indexOf(e))?t:e}function ch(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}let uh=1;const hh={};function dh(e,t,n){if("number"==typeof e){const r=hh[e];if(r)return r.keepAlive||delete hh[e],r.callback(t,n)}return t}const fh="success",ph="fail",gh="complete";function vh(e,t={},{beforeAll:n,beforeSuccess:r}={}){S(t)||(t={});const{success:s,fail:o,complete:i}=function(e){const t={};for(const n in e){const r=e[n];v(r)&&(t[n]=ch(r),delete e[n])}return t}(t),a=v(s),l=v(o),c=v(i),u=uh++;return function(e,t,n,r=!1){hh[e]={name:t,keepAlive:r,callback:n}}(u,e,u=>{(u=u||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(u.errMsg,e),v(n)&&n(u),u.errMsg===e+":ok"?(v(r)&&r(u,t),a&&s(u)):l&&o(u),c&&i(u)}),u}const mh="success",yh="fail",_h="complete",bh={},wh={};function xh(e,t){return function(n){return e(n,t)||n}}function Sh(e,t,n){let r=!1;for(let s=0;s<e.length;s++){const o=e[s];if(r)r=Promise.resolve(xh(o,n));else{const e=o(t,n);if(b(e)&&(r=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return r||{then:e=>e(t),catch(){}}}function kh(e,t={}){return[mh,yh,_h].forEach(n=>{const r=e[n];if(!f(r))return;const s=t[n];t[n]=function(e){Sh(r,e,t).then(e=>v(s)&&s(e)||e)}}),t}function Th(e,t){const n=[];f(bh.returnValue)&&n.push(...bh.returnValue);const r=wh[e];return r&&f(r.returnValue)&&n.push(...r.returnValue),n.forEach(e=>{t=e(t)||t}),t}function Eh(e){const t=Object.create(null);Object.keys(bh).forEach(e=>{"returnValue"!==e&&(t[e]=bh[e].slice())});const n=wh[e];return n&&Object.keys(n).forEach(e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))}),t}function Ch(e,t,n,r){const s=Eh(e);if(s&&Object.keys(s).length){if(f(s.invoke)){return Sh(s.invoke,n).then(n=>t(kh(Eh(e),n),...r))}return t(kh(s,n),...r)}return t(n,...r)}function Oh(e,t){return(n={},...r)=>function(e){return!(!S(e)||![fh,ph,gh].find(t=>v(e[t])))}(n)?Th(e,Ch(e,t,n,r)):Th(e,new Promise((s,o)=>{Ch(e,t,c(n,{success:s,fail:o}),r)}))}function Ph(e,t,n,r={}){const s=t+":fail"+(n?" "+n:"");return delete r.errCode,dh(e,c({errMsg:s},r))}function Ah(e,t,n,r){if(r&&r.beforeInvoke){const e=r.beforeInvoke(t);if(m(e))return e}const s=function(e,t){const n=e[0];if(!t||!t.formatArgs||!S(t.formatArgs)&&S(n))return;const r=t.formatArgs,s=Object.keys(r);for(let o=0;o<s.length;o++){const t=s[o],i=r[t];if(v(i)){const r=i(e[0][t],n);if(m(r))return r}else d(n,t)||(n[t]=i)}}(t,r);if(s)return s}function $h(e,t,n,r){return n=>{const s=vh(e,n,r),o=Ah(0,[n],0,r);return o?Ph(s,e,o):t(n,{resolve:t=>function(e,t,n){return dh(e,c(n||{},{errMsg:t+":ok"}))}(s,e,t),reject:(t,n)=>Ph(s,e,function(e){return!e||m(e)?e:e.stack?(console.error(e.message+"\n"+e.stack),e.message):e}(t),n)})}}function jh(e,t,n,r){return function(e,t,n,r){return(...e)=>{const n=Ah(0,e,0,r);if(n)throw new Error(n);return t.apply(null,e)}}(0,t,0,r)}function Bh(e,t,n,r){return Oh(e,function(e,t,n,r){return $h(e,t,0,r)}(e,t,0,r))}let Ih=!1,Rh=0,Lh=0,Mh=960,Dh=375,Nh=750;function Fh(){const{platform:e,pixelRatio:t,windowWidth:n}=function(){const e=nh(),t=oh(sh(e,rh(e)));return{platform:Gu?"ios":"other",pixelRatio:window.devicePixelRatio,windowWidth:t}}();Rh=n,Lh=t,Ih="ios"===e}function Uh(e,t){const n=Number(e);return isNaN(n)?t:n}const zh=jh(0,(e,t)=>{if(0===Rh&&(Fh(),function(){const e=__uniConfig.globalStyle||{};Mh=Uh(e.rpxCalcMaxDeviceWidth,960),Dh=Uh(e.rpxCalcBaseDeviceWidth,375),Nh=Uh(e.rpxCalcBaseDeviceWidth,750)}()),0===(e=Number(e)))return 0;let n=t||Rh;n=e===Nh||n<=Mh?n:Dh;let r=e/750*n;return r<0&&(r=-r),r=Math.floor(r+1e-4),0===r&&(r=1!==Lh&&Ih?.5:1),e<0?-r:r}),Hh=new tt,Wh=jh(0,(e,t)=>(Hh.on(e,t),()=>Hh.off(e,t))),qh=jh(0,(e,...t)=>{Hh.emit(e,...t)}),Vh=jh(0,()=>{const e=Gp();return e&&e.$vm?e.$vm.$locale:nc().getLocale()}),Xh={[ue]:[],[ce]:[],[ae]:[],[se]:[],[oe]:[]};const Kh={formatArgs:{count(e,t){(!e||e<=0)&&(t.count=9)},sizeType(e,t){t.sizeType=lh(e,ih)},sourceType(e,t){t.sourceType=lh(e,ah)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}},Yh={formatArgs:{urls(e,t){t.urls=e.map(e=>m(e)&&e?Ku(e):"")},current(e,t){"number"==typeof e?t.current=e>0&&e<t.urls.length?e:0:m(e)&&e&&(t.current=Ku(e))}}};const Jh={url:{type:String,required:!0}},Gh="navigateTo",Zh="redirectTo",Qh="reLaunch",ed="switchTab",td="preloadPage",nd=(ad(["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"]),ad(["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"]),ud(Gh)),rd=ud(Zh),sd=ud(Qh),od=ud(ed),id={formatArgs:{delta(e,t){e=parseInt(e+"")||1,t.delta=Math.min(Tp().length-1,e)}}};function ad(e){return{animationType:{type:String,validator(t){if(t&&-1===e.indexOf(t))return"`"+t+"` is not supported for `animationType` (supported values are: `"+e.join("`|`")+"`)"}},animationDuration:{type:Number}}}let ld;function cd(){ld=""}function ud(e){return{formatArgs:{url:hd(e)},beforeAll:cd}}function hd(e){return function(t,n){if(!t)return'Missing required args: "url"';const r=(t=function(e){if(0===e.indexOf("/"))return e;let t="";const n=Tp();return n.length&&(t=n[n.length-1].$page.route),lu(t,e)}(t)).split("?")[0],s=cu(r,!0);if(!s)return"page `"+t+"` is not found";if(e===Gh||e===Zh){if(s.meta.isTabBar)return`can not ${e} a tabbar page`}else if(e===ed&&!s.meta.isTabBar)return"can not switch to no-tabBar page";if(e!==ed&&e!==td||!s.meta.isTabBar||"appLaunch"===n.openType||(t=r),s.meta.isEntry&&(t=t.replace(s.alias,"/")),n.url=function(e){if(!m(e))return e;const t=e.indexOf("?");if(-1===t)return e;const n=e.slice(t+1).trim().replace(/^(\?|#|&)/,"");if(!n)return e;e=e.slice(0,t);const r=[];return n.split("&").forEach(e=>{const t=e.replace(/\+/g," ").split("="),n=t.shift(),s=t.length>0?t.join("="):"";r.push(n+"="+encodeURIComponent(s))}),r.length?e+"?"+r.join("&"):e}(t),"unPreloadPage"!==e)if(e!==td){if(ld===t&&"appLaunch"!==n.openType)return`${ld} locked`;__uniConfig.ready&&(ld=t)}else if(s.meta.isTabBar){const e=Tp(),t=s.path.slice(1);if(e.find(e=>e.route===t))return"tabBar page `"+t+"` already exists"}}}const dd={formatArgs:{itemColor:"#000"}},fd={formatArgs:{title:"",mask:!1}},pd=["success","loading","none","error"],gd={formatArgs:{title:"",icon(e,t){var n,r;t.icon=(r=pd,(n=e)&&-1!==r.indexOf(n)?n:r[0])},image(e,t){t.image=e?Ku(e):""},duration:1500,mask:!1}},vd="stopPullDownRefresh",md={};function yd(e){for(const n in md)if(d(md,n)){if(md[n]===e)return n}var t=(window.URL||window.webkitURL).createObjectURL(e);return md[t]=e,t}const _d=$u(),bd=$u();const wd=Ru({name:"ResizeSensor",props:{initial:{type:Boolean,default:!1}},emits:["resize"],setup(e,{emit:t}){const n=$n(null),r=function(e){return()=>{const{firstElementChild:t,lastElementChild:n}=e.value;t.scrollLeft=1e5,t.scrollTop=1e5,n.scrollLeft=1e5,n.scrollTop=1e5}}(n),s=function(e,t,n){const r=gn({width:-1,height:-1});return Or(()=>c({},r),e=>t("resize",e)),()=>{const t=e.value;t&&(r.width=t.offsetWidth,r.height=t.offsetHeight,n())}}(n,t,r);return function(e,t,n,r){ts(r),ds(()=>{t.initial&&Zn(n);const s=e.value;s.offsetParent!==s.parentElement&&(s.parentElement.style.position="relative"),"AnimationEvent"in window||r()})}(n,e,s,r),()=>$o("uni-resize-sensor",{ref:n,onAnimationstartOnce:s},[$o("div",{onScroll:s},[$o("div",null,null)],40,["onScroll"]),$o("div",{onScroll:s},[$o("div",null,null)],40,["onScroll"])],40,["onAnimationstartOnce"])}});function xd(){}const Sd={cursorSpacing:{type:[Number,String],default:0},showConfirmBar:{type:[Boolean,String],default:"auto"},adjustPosition:{type:[Boolean,String],default:!0},autoBlur:{type:[Boolean,String],default:!1}};function kd(e,t,n){function r(e){const t=ei(()=>0===String(navigator.vendor).indexOf("Apple"));e.addEventListener("focus",()=>{clearTimeout(undefined),document.addEventListener("click",xd,!1)});e.addEventListener("blur",()=>{t.value&&e.blur(),document.removeEventListener("click",xd,!1),t.value&&document.documentElement.scrollTo(document.documentElement.scrollLeft,document.documentElement.scrollTop)})}Or(()=>t.value,e=>e&&r(e))}const Td={src:{type:String,default:""},mode:{type:String,default:"scaleToFill"},lazyLoad:{type:[Boolean,String],default:!1},draggable:{type:Boolean,default:!1}},Ed={widthFix:["offsetWidth","height",(e,t)=>e/t],heightFix:["offsetHeight","width",(e,t)=>e*t]},Cd={aspectFit:["center center","contain"],aspectFill:["center center","cover"],widthFix:[,"100% 100%"],heightFix:[,"100% 100%"],top:["center top"],bottom:["center bottom"],center:["center center"],left:["left center"],right:["right center"],"top left":["left top"],"top right":["right top"],"bottom left":["left bottom"],"bottom right":["right bottom"]},Od=Ru({name:"Image",props:Td,setup(e,{emit:t}){const n=$n(null),r=function(e,t){const n=$n(""),r=ei(()=>{let e="auto",r="";const s=Cd[t.mode];return s?(s[0]&&(r=s[0]),s[1]&&(e=s[1])):(r="0% 0%",e="100% 100%"),`background-image:${n.value?'url("'+n.value+'")':"none"};background-position:${r};background-size:${e};`}),s=gn({rootEl:e,src:ei(()=>t.src?Ku(t.src):""),origWidth:0,origHeight:0,origStyle:{width:"",height:""},modeStyle:r,imgSrc:n});return ds(()=>{const t=e.value;s.origWidth=t.clientWidth||0,s.origHeight=t.clientHeight||0}),s}(n,e),s=Du(n,t),{fixSize:o}=function(e,t,n){const r=()=>{const{mode:r}=t,s=Ed[r];if(!s)return;const{origWidth:o,origHeight:i}=n,a=o&&i?o/i:0;if(!a)return;const l=e.value,c=l[s[0]];c&&(l.style[s[1]]=function(e){Pd&&e>10&&(e=2*Math.round(e/2));return e}(s[2](c,a))+"px")},s=()=>{const{style:t}=e.value,{origStyle:{width:r,height:s}}=n;t.width=r,t.height=s};return Or(()=>t.mode,(e,t)=>{Ed[t]&&s(),Ed[e]&&r()}),{fixSize:r,resetSize:s}}(n,e,r);return function(e,t,n,r,s){let o,i;const a=(t=0,n=0,r="")=>{e.origWidth=t,e.origHeight=n,e.imgSrc=r},l=l=>{if(!l)return c(),void a();o=o||new Image,o.onload=e=>{const{width:u,height:h}=o;a(u,h,l),Zn(()=>{r()}),o.draggable=t.draggable,i&&i.remove(),i=o,n.value.appendChild(o),c(),s("load",e,{width:u,height:h})},o.onerror=t=>{a(),c(),s("error",t,{errMsg:`GET ${e.src} 404 (Not Found)`})},o.src=l},c=()=>{o&&(o.onload=null,o.onerror=null,o=null)};Or(()=>e.src,e=>l(e)),Or(()=>e.imgSrc,e=>{!e&&i&&(i.remove(),i=null)}),ds(()=>l(e.src)),gs(()=>c())}(r,e,n,o,s),()=>$o("uni-image",{ref:n},[$o("div",{style:r.modeStyle},null,4),Ed[e.mode]?$o(wd,{onResize:o},null,8,["onResize"]):$o("span",null,null)],512)}});const Pd="Google Inc."===navigator.vendor;const Ad=ze(!0),$d=[];let jd=0,Bd=!1;const Id=e=>$d.forEach(t=>t.userAction=e);function Rd(e={userAction:!1}){if(!Bd){["touchstart","touchmove","touchend","mousedown","mouseup"].forEach(e=>{document.addEventListener(e,function(){!jd&&Id(!0),jd++,setTimeout(()=>{! --jd&&Id(!1)},0)},Ad)}),Bd=!0}$d.push(e)}function Ld(){const e=gn({userAction:!1});return ds(()=>{Rd(e)}),gs(()=>{!function(e){const t=$d.indexOf(e);t>=0&&$d.splice(t,1)}(e)}),{state:e}}function Md(e,t){const n=document.activeElement;if(!n)return t({});const r={};["input","textarea"].includes(n.tagName.toLowerCase())&&(r.start=n.selectionStart,r.end=n.selectionEnd),t(r)}const Dd=function(){var e,t,n;e=Qc(),n=Md,t=pc(e,t="getSelectedTextRange"),fc[t]||(fc[t]=n)};function Nd(e,t,n){"number"===t&&isNaN(Number(e))&&(e="");return null==e?"":String(e)}const Fd=["none","text","decimal","numeric","tel","search","email","url"],Ud=c({},{name:{type:String,default:""},modelValue:{type:[String,Number]},value:{type:[String,Number]},disabled:{type:[Boolean,String],default:!1},autoFocus:{type:[Boolean,String],default:!1},focus:{type:[Boolean,String],default:!1},cursor:{type:[Number,String],default:-1},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},type:{type:String,default:"text"},password:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},placeholderStyle:{type:String,default:""},placeholderClass:{type:String,default:""},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},ignoreCompositionEvent:{type:Boolean,default:!0},step:{type:String,default:"0.000000000000000001"},inputmode:{type:String,default:void 0,validator:e=>!!~Fd.indexOf(e)},cursorColor:{type:String,default:""}},Sd),zd=["input","focus","blur","update:value","update:modelValue","update:focus","compositionstart","compositionupdate","compositionend","keyboardheightchange"];function Hd(e,t,n,r){let s=null;s=Ke(n=>{t.value=Nd(n,e.type)},100,{setTimeout:setTimeout,clearTimeout:clearTimeout}),Or(()=>e.modelValue,s),Or(()=>e.value,s);const o=function(e,t){let n,r,s=0;const o=function(...o){const i=Date.now();clearTimeout(n),r=()=>{r=null,s=i,e.apply(this,o)},i-s<t?n=setTimeout(r,t-(i-s)):r()};return o.cancel=function(){clearTimeout(n),r=null},o.flush=function(){clearTimeout(n),r&&r()},o}((e,t)=>{s.cancel(),n("update:modelValue",t.value),n("update:value",t.value),r("input",e,t)},100);return hs(()=>{s.cancel(),o.cancel()}),{trigger:r,triggerInput:(e,t,n)=>{s.cancel(),o(e,t),n&&o.flush()}}}function Wd(e,t){Ld();const n=ei(()=>e.autoFocus||e.focus);function r(){if(!n.value)return;const e=t.value;e?e.focus():setTimeout(r,100)}Or(()=>e.focus,e=>{e?r():function(){const e=t.value;e&&e.blur()}()}),ds(()=>{n.value&&Zn(r)})}function qd(e,t,n,r){Dd();const{fieldRef:s,state:o,trigger:i}=function(e,t,n){const r=$n(null),s=Du(t,n),o=ei(()=>{const t=Number(e.selectionStart);return isNaN(t)?-1:t}),i=ei(()=>{const t=Number(e.selectionEnd);return isNaN(t)?-1:t}),a=ei(()=>{const t=Number(e.cursor);return isNaN(t)?-1:t}),l=ei(()=>{var t=Number(e.maxlength);return isNaN(t)?140:t});let c="";c=Nd(e.modelValue,e.type)||Nd(e.value,e.type);const u=gn({value:c,valueOrigin:c,maxlength:l,focus:e.focus,composing:!1,selectionStart:o,selectionEnd:i,cursor:a});return Or(()=>u.focus,e=>n("update:focus",e)),Or(()=>u.maxlength,e=>u.value=u.value.slice(0,e),{immediate:!1}),{fieldRef:r,state:u,trigger:s}}(e,t,n),{triggerInput:a}=Hd(e,o,n,i);Wd(e,s),kd(0,s);const{state:l}=function(){const e=gn({attrs:{}});return ds(()=>{let t=Ho();for(;t;){const n=t.type.__scopeId;n&&(e.attrs[n]=""),t=t.proxy&&"page"===t.proxy.$mpType?null:t.parent}}),{state:e}}();!function(e,t){const n=Vs(zu,!1);if(!n)return;const r=Ho(),s={submit(){const n=r.proxy;return[n[e],m(t)?n[t]:t.value]},reset(){m(t)?r.proxy[t]="":t.value=""}};n.addField(s),gs(()=>{n.removeField(s)})}("name",o),function(e,t,n,r,s,o){function i(){const n=e.value;n&&t.focus&&t.selectionStart>-1&&t.selectionEnd>-1&&"number"!==n.type&&(n.selectionStart=t.selectionStart,n.selectionEnd=t.selectionEnd)}function a(){const n=e.value;n&&t.focus&&t.selectionStart<0&&t.selectionEnd<0&&t.cursor>-1&&"number"!==n.type&&(n.selectionEnd=n.selectionStart=t.cursor)}function l(e){return"number"===e.type?null:e.selectionEnd}Or([()=>t.selectionStart,()=>t.selectionEnd],i),Or(()=>t.cursor,a),Or(()=>e.value,function(){const c=e.value;if(!c)return;const u=function(e,r){e.stopPropagation(),v(o)&&!1===o(e,t)||(t.value=c.value,t.composing&&n.ignoreCompositionEvent||s(e,{value:c.value,cursor:l(c)},r))};function h(e){n.ignoreCompositionEvent||r(e.type,e,{value:e.data})}c.addEventListener("change",e=>e.stopPropagation()),c.addEventListener("focus",function(e){t.focus=!0,r("focus",e,{value:t.value}),i(),a()}),c.addEventListener("blur",function(e){t.composing&&(t.composing=!1,u(e,!0)),t.focus=!1,r("blur",e,{value:t.value,cursor:l(e.target)})}),c.addEventListener("input",u),c.addEventListener("compositionstart",e=>{e.stopPropagation(),t.composing=!0,h(e)}),c.addEventListener("compositionend",e=>{e.stopPropagation(),t.composing&&(t.composing=!1,u(e)),h(e)}),c.addEventListener("compositionupdate",h)})}(s,o,e,i,a,r);return{fieldRef:s,state:o,scopedAttrsState:l,fixDisabledColor:0===String(navigator.vendor).indexOf("Apple")&&CSS.supports("image-orientation:from-image"),trigger:i}}function Vd(e,t,n,r,s){if(t.value)if("."===e.data){if("."===t.value.slice(-1))return n.value=r.value=t.value=t.value.slice(0,-1),!1;if(t.value&&!t.value.includes("."))return t.value+=".",s&&(s.fn=()=>{n.value=r.value=t.value=t.value.slice(0,-1),r.removeEventListener("blur",s.fn)},r.addEventListener("blur",s.fn)),!1}else if("deleteContentBackward"===e.inputType&&navigator.userAgent.includes("iPhone OS 16")&&"."===t.value.slice(-2,-1))return t.value=n.value=r.value=t.value.slice(0,-2),!0}const Xd=Ru({name:"Input",props:c({},Ud,{placeholderClass:{type:String,default:"input-placeholder"},textContentType:{type:String,default:""}}),emits:["confirm",...zd],setup(e,{emit:t,expose:n}){const r=["text","number","idcard","digit","password","tel"],s=["off","one-time-code"],o=ei(()=>{let t="";switch(e.type){case"text":t="text","search"===e.confirmType&&(t="search");break;case"idcard":t="text";break;case"digit":t="number";break;default:t=~r.includes(e.type)?e.type:"text"}return e.password?"password":t}),i=ei(()=>{const t=s.indexOf(e.textContentType),n=s.indexOf(A(e.textContentType));return s[-1!==t?t:-1!==n?n:0]});let a=function(e,t){if("number"===t.value){const t=void 0===e.modelValue?e.value:e.modelValue,n=$n(null!=t?t.toLocaleString():"");return Or(()=>e.modelValue,e=>{n.value=null!=e?e.toLocaleString():""}),Or(()=>e.value,e=>{n.value=null!=e?e.toLocaleString():""}),n}return $n("")}(e,o),l={fn:null};const c=$n(null),{fieldRef:u,state:h,scopedAttrsState:d,fixDisabledColor:f,trigger:p}=qd(e,c,t,(e,t)=>{const n=e.target;if("number"===o.value){if(l.fn&&(n.removeEventListener("blur",l.fn),l.fn=null),n.validity&&!n.validity.valid){if((!a.value||!n.value)&&"-"===e.data||"-"===a.value[0]&&"deleteContentBackward"===e.inputType)return a.value="-",t.value="",l.fn=()=>{a.value=n.value=""},n.addEventListener("blur",l.fn),!1;const r=Vd(e,a,t,n,l);return"boolean"==typeof r?r:(a.value=t.value=n.value="-"===a.value?"":a.value,!1)}{const r=Vd(e,a,t,n,l);if("boolean"==typeof r)return r;a.value=n.value}const r=t.maxlength;if(r>0&&n.value.length>r)return n.value=n.value.slice(0,r),t.value=n.value,!1}});Or(()=>h.value,t=>{"number"!==e.type||"-"===a.value&&""===t||(a.value=t.toString())});const g=["number","digit"],v=ei(()=>g.includes(e.type)?e.step:"");function m(t){if("Enter"!==t.key)return;const n=t.target;t.stopPropagation(),p("confirm",t,{value:n.value}),!e.confirmHold&&n.blur()}return n({$triggerInput:e=>{t("update:modelValue",e.value),t("update:value",e.value),h.value=e.value}}),()=>{let t=e.disabled&&f?$o("input",{key:"disabled-input",ref:u,value:h.value,tabindex:"-1",readonly:!!e.disabled,type:o.value,maxlength:h.maxlength,step:v.value,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},onFocus:e=>e.target.blur()},null,44,["value","readonly","type","maxlength","step","onFocus"]):$o("input",{key:"input",ref:u,value:h.value,onInput:e=>{h.value=e.target.value.toString()},disabled:!!e.disabled,type:o.value,maxlength:h.maxlength,step:v.value,enterkeyhint:e.confirmType,pattern:"number"===e.type?"[0-9]*":void 0,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},autocomplete:i.value,onKeyup:m,inputmode:e.inputmode},null,44,["value","onInput","disabled","type","maxlength","step","enterkeyhint","pattern","autocomplete","onKeyup","inputmode"]);return $o("uni-input",{ref:c},[$o("div",{class:"uni-input-wrapper"},[Br($o("div",Do(d.attrs,{style:e.placeholderStyle,class:["uni-input-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Si,!(h.value.length||"-"===a.value||a.value.includes("."))]]),"search"===e.confirmType?$o("form",{action:"",onSubmit:e=>e.preventDefault(),class:"uni-input-form"},[t],40,["onSubmit"]):t])],512)}}});const Kd=["class","style"],Yd=/^on[A-Z]+/,Jd=(e={})=>{const{excludeListeners:t=!1,excludeKeys:n=[]}=e,r=Ho(),s=jn({}),o=jn({}),i=jn({}),a=n.concat(Kd);return r.attrs=gn(r.attrs),Er(()=>{const e=(n=r.attrs,Object.keys(n).map(e=>[e,n[e]])).reduce((e,[n,r])=>(a.includes(n)?e.exclude[n]=r:Yd.test(n)?(t||(e.attrs[n]=r),e.listeners[n]=r):e.attrs[n]=r,e),{exclude:{},attrs:{},listeners:{}});var n;s.value=e.attrs,o.value=e.listeners,i.value=e.exclude}),{$attrs:s,$listeners:o,$excludeAttrs:i}};function Gd(e){const t=[];return f(e)&&e.forEach(e=>{To(e)?e.type===fo?t.push(...Gd(e.children)):t.push(e):f(e)&&t.push(...Gd(e))}),t}const Zd=Ru({inheritAttrs:!1,name:"MovableArea",props:{scaleArea:{type:Boolean,default:!1}},setup(e,{slots:t}){const n=$n(null),r=$n(!1);let{setContexts:s,events:o}=function(e,t){const n=$n(0),r=$n(0),s=gn({x:null,y:null}),o=$n(null);let i=null,a=[];function l(t){t&&1!==t&&(e.scaleArea?a.forEach(function(e){e._setScale(t)}):i&&i._setScale(t))}function c(e,n=a){let r=t.value;function s(e){for(let t=0;t<n.length;t++){const r=n[t];if(e===r.rootRef.value)return r}return e===r||e===document.body||e===document?null:s(e.parentNode)}return s(e)}const u=Mu(t=>{let n=t.touches;if(n&&n.length>1){let t={x:n[1].pageX-n[0].pageX,y:n[1].pageY-n[0].pageY};if(o.value=Qd(t),s.x=t.x,s.y=t.y,!e.scaleArea){let e=c(n[0].target),t=c(n[1].target);i=e&&e===t?e:null}}}),h=Mu(e=>{let t=e.touches;if(t&&t.length>1){e.preventDefault();let n={x:t[1].pageX-t[0].pageX,y:t[1].pageY-t[0].pageY};if(null!==s.x&&o.value&&o.value>0){l(Qd(n)/o.value)}s.x=n.x,s.y=n.y}}),d=Mu(t=>{let n=t.touches;n&&n.length||t.changedTouches&&(s.x=0,s.y=0,o.value=null,e.scaleArea?a.forEach(function(e){e._endScale()}):i&&i._endScale())});function f(){p(),a.forEach(function(e,t){e.setParent()})}function p(){let e=window.getComputedStyle(t.value),s=t.value.getBoundingClientRect();n.value=s.width-["Left","Right"].reduce(function(t,n){const r="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[r])},0),r.value=s.height-["Top","Bottom"].reduce(function(t,n){const r="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[r])},0)}return qs("movableAreaWidth",n),qs("movableAreaHeight",r),{setContexts(e){a=e},events:{_onTouchstart:u,_onTouchmove:h,_onTouchend:d,_resize:f}}}(e,n);const{$listeners:i,$attrs:a,$excludeAttrs:l}=Jd(),c=i.value;["onTouchstart","onTouchmove","onTouchend"].forEach(e=>{let t=c[e],n=o[`_${e}`];c[e]=t?[].concat(t,n):n}),ds(()=>{o._resize(),r.value=!0});let u=[];const h=[];function d(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n=n.el;const r=h.find(e=>n===e.rootRef.value);r&&e.push(kn(r))}s(e)}return qs("_isMounted",r),qs("movableAreaRootRef",n),qs("addMovableViewContext",e=>{h.push(e),d()}),qs("removeMovableViewContext",e=>{const t=h.indexOf(e);t>=0&&(h.splice(t,1),d())}),()=>{const e=t.default&&t.default();return u=Gd(e),$o("uni-movable-area",Do({ref:n},a.value,l.value,c),[$o(wd,{onResize:o._resize},null,8,["onResize"]),u],16)}}});function Qd(e){return Math.sqrt(e.x*e.x+e.y*e.y)}const ef=function(e,t,n,r){e.addEventListener(t,e=>{v(n)&&!1===n(e)&&((void 0===e.cancelable||e.cancelable)&&e.preventDefault(),e.stopPropagation())},{passive:!1})};let tf,nf;function rf(e,t,n){gs(()=>{document.removeEventListener("mousemove",tf),document.removeEventListener("mouseup",nf)});let r=0,s=0,o=0,i=0;const a=function(e,n,a,l){if(!1===t({cancelable:e.cancelable,target:e.target,currentTarget:e.currentTarget,preventDefault:e.preventDefault.bind(e),stopPropagation:e.stopPropagation.bind(e),touches:e.touches,changedTouches:e.changedTouches,detail:{state:n,x:a,y:l,dx:a-r,dy:l-s,ddx:a-o,ddy:l-i,timeStamp:e.timeStamp}}))return!1};let l,c,u=null;ef(e,"touchstart",function(e){if(l=!0,1===e.touches.length&&!u)return u=e,r=o=e.touches[0].pageX,s=i=e.touches[0].pageY,a(e,"start",r,s)}),ef(e,"mousedown",function(e){if(c=!0,!l&&!u)return u=e,r=o=e.pageX,s=i=e.pageY,a(e,"start",r,s)}),ef(e,"touchmove",function(e){if(1===e.touches.length&&u){const t=a(e,"move",e.touches[0].pageX,e.touches[0].pageY);return o=e.touches[0].pageX,i=e.touches[0].pageY,t}});const h=tf=function(e){if(!l&&c&&u){const t=a(e,"move",e.pageX,e.pageY);return o=e.pageX,i=e.pageY,t}};document.addEventListener("mousemove",h),ef(e,"touchend",function(e){if(0===e.touches.length&&u)return l=!1,u=null,a(e,"end",e.changedTouches[0].pageX,e.changedTouches[0].pageY)});const d=nf=function(e){if(c=!1,!l&&u)return u=null,a(e,"end",e.pageX,e.pageY)};document.addEventListener("mouseup",d),ef(e,"touchcancel",function(e){if(u){l=!1;const t=u;return u=null,a(e,n?"cancel":"end",t.touches[0].pageX,t.touches[0].pageY)}})}function sf(e,t,n){return e>t-n&&e<t+n}function of(e,t){return sf(e,0,t)}function af(){}function lf(e,t){this._m=e,this._f=1e3*t,this._startTime=0,this._v=0}function cf(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}function uf(e,t,n){this._springX=new cf(e,t,n),this._springY=new cf(e,t,n),this._springScale=new cf(e,t,n),this._startTime=0}af.prototype.x=function(e){return Math.sqrt(e)},lf.prototype.setV=function(e,t){const n=Math.pow(Math.pow(e,2)+Math.pow(t,2),.5);this._x_v=e,this._y_v=t,this._x_a=-this._f*this._x_v/n,this._y_a=-this._f*this._y_v/n,this._t=Math.abs(e/this._x_a)||Math.abs(t/this._y_a),this._lastDt=null,this._startTime=(new Date).getTime()},lf.prototype.setS=function(e,t){this._x_s=e,this._y_s=t},lf.prototype.s=function(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t,this._lastDt=e);let t=this._x_v*e+.5*this._x_a*Math.pow(e,2)+this._x_s,n=this._y_v*e+.5*this._y_a*Math.pow(e,2)+this._y_s;return(this._x_a>0&&t<this._endPositionX||this._x_a<0&&t>this._endPositionX)&&(t=this._endPositionX),(this._y_a>0&&n<this._endPositionY||this._y_a<0&&n>this._endPositionY)&&(n=this._endPositionY),{x:t,y:n}},lf.prototype.ds=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t),{dx:this._x_v+this._x_a*e,dy:this._y_v+this._y_a*e}},lf.prototype.delta=function(){return{x:-1.5*Math.pow(this._x_v,2)/this._x_a||0,y:-1.5*Math.pow(this._y_v,2)/this._y_a||0}},lf.prototype.dt=function(){return-this._x_v/this._x_a},lf.prototype.done=function(){const e=sf(this.s().x,this._endPositionX)||sf(this.s().y,this._endPositionY)||this._lastDt===this._t;return this._lastDt=null,e},lf.prototype.setEnd=function(e,t){this._endPositionX=e,this._endPositionY=t},lf.prototype.reconfigure=function(e,t){this._m=e,this._f=1e3*t},cf.prototype._solve=function(e,t){const n=this._c,r=this._m,s=this._k,o=n*n-4*r*s;if(0===o){const s=-n/(2*r),o=e,i=t/(s*e);return{x:function(e){return(o+i*e)*Math.pow(Math.E,s*e)},dx:function(e){const t=Math.pow(Math.E,s*e);return s*(o+i*e)*t+i*t}}}if(o>0){const s=(-n-Math.sqrt(o))/(2*r),i=(-n+Math.sqrt(o))/(2*r),a=(t-s*e)/(i-s),l=e-a;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,s*e)),n||(n=this._powER2T=Math.pow(Math.E,i*e)),l*t+a*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,s*e)),n||(n=this._powER2T=Math.pow(Math.E,i*e)),l*s*t+a*i*n}}}const i=Math.sqrt(4*r*s-n*n)/(2*r),a=-n/2*r,l=e,c=(t-a*e)/i;return{x:function(e){return Math.pow(Math.E,a*e)*(l*Math.cos(i*e)+c*Math.sin(i*e))},dx:function(e){const t=Math.pow(Math.E,a*e),n=Math.cos(i*e),r=Math.sin(i*e);return t*(c*i*n-l*i*r)+a*t*(c*r+l*n)}}},cf.prototype.x=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0},cf.prototype.dx=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0},cf.prototype.setEnd=function(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!of(t,.1)){t=t||0;let r=this._endPosition;this._solution&&(of(t,.1)&&(t=this._solution.dx((n-this._startTime)/1e3)),r=this._solution.x((n-this._startTime)/1e3),of(t,.1)&&(t=0),of(r,.1)&&(r=0),r+=this._endPosition),this._solution&&of(r-e,.1)&&of(t,.1)||(this._endPosition=e,this._solution=this._solve(r-this._endPosition,t),this._startTime=n)}},cf.prototype.snap=function(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}},cf.prototype.done=function(e){return e||(e=(new Date).getTime()),sf(this.x(),this._endPosition,.1)&&of(this.dx(),.1)},cf.prototype.reconfigure=function(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())},cf.prototype.springConstant=function(){return this._k},cf.prototype.damping=function(){return this._c},cf.prototype.configuration=function(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]},uf.prototype.setEnd=function(e,t,n,r){const s=(new Date).getTime();this._springX.setEnd(e,r,s),this._springY.setEnd(t,r,s),this._springScale.setEnd(n,r,s),this._startTime=s},uf.prototype.x=function(){const e=((new Date).getTime()-this._startTime)/1e3;return{x:this._springX.x(e),y:this._springY.x(e),scale:this._springScale.x(e)}},uf.prototype.done=function(){const e=(new Date).getTime();return this._springX.done(e)&&this._springY.done(e)&&this._springScale.done(e)},uf.prototype.reconfigure=function(e,t,n){this._springX.reconfigure(e,t,n),this._springY.reconfigure(e,t,n),this._springScale.reconfigure(e,t,n)};function hf(e,t){return+((1e3*e-1e3*t)/1e3).toFixed(1)}const df=Ru({name:"MovableView",props:{direction:{type:String,default:"none"},inertia:{type:[Boolean,String],default:!1},outOfBounds:{type:[Boolean,String],default:!1},x:{type:[Number,String],default:0},y:{type:[Number,String],default:0},damping:{type:[Number,String],default:20},friction:{type:[Number,String],default:2},disabled:{type:[Boolean,String],default:!1},scale:{type:[Boolean,String],default:!1},scaleMin:{type:[Number,String],default:.5},scaleMax:{type:[Number,String],default:10},scaleValue:{type:[Number,String],default:1},animation:{type:[Boolean,String],default:!0}},emits:["change","scale"],setup(e,{slots:t,emit:n}){const r=$n(null),s=Du(r,n),{setParent:o}=function(e,t,n){const r=Vs("_isMounted",$n(!1)),s=Vs("addMovableViewContext",()=>{}),o=Vs("removeMovableViewContext",()=>{});let i,a,l=$n(1),c=$n(1),u=$n(!1),h=$n(0),d=$n(0),f=null,p=null,g=!1,v=null,m=null;const y=new af,_=new af,b={historyX:[0,0],historyY:[0,0],historyT:[0,0]},w=ei(()=>{let t=Number(e.friction);return isNaN(t)||t<=0?2:t}),x=new lf(1,w.value);Or(()=>e.disabled,()=>{q()});const{_updateOldScale:S,_endScale:k,_setScale:T,scaleValueSync:E,_updateBoundary:C,_updateOffset:O,_updateWH:P,_scaleOffset:A,minX:$,minY:j,maxX:B,maxY:I,FAandSFACancel:R,_getLimitXY:L,_setTransform:M,_revise:D,dampingNumber:N,xMove:F,yMove:U,xSync:z,ySync:H,_STD:W}=function(e,t,n,r,s,o,i,a,l,c){const u=ei(()=>{let t=Number(e.scaleMin);return isNaN(t)?.5:t}),h=ei(()=>{let t=Number(e.scaleMax);return isNaN(t)?10:t}),d=$n(Number(e.scaleValue)||1);Or(d,e=>{M(e)}),Or(u,()=>{L()}),Or(h,()=>{L()}),Or(()=>e.scaleValue,e=>{d.value=Number(e)||0});const{_updateBoundary:f,_updateOffset:p,_updateWH:g,_scaleOffset:v,minX:m,minY:y,maxX:_,maxY:b}=function(e,t,n){const r=Vs("movableAreaWidth",$n(0)),s=Vs("movableAreaHeight",$n(0)),o=Vs("movableAreaRootRef"),i={x:0,y:0},a={x:0,y:0},l=$n(0),c=$n(0),u=$n(0),h=$n(0),d=$n(0),f=$n(0);function p(){let e=0-i.x+a.x,t=r.value-l.value-i.x-a.x;u.value=Math.min(e,t),d.value=Math.max(e,t);let n=0-i.y+a.y,o=s.value-c.value-i.y-a.y;h.value=Math.min(n,o),f.value=Math.max(n,o)}function g(){i.x=gf(e.value,o.value),i.y=vf(e.value,o.value)}function v(r){r=r||t.value,r=n(r);let s=e.value.getBoundingClientRect();c.value=s.height/t.value,l.value=s.width/t.value;let o=c.value*r,i=l.value*r;a.x=(i-l.value)/2,a.y=(o-c.value)/2}return{_updateBoundary:p,_updateOffset:g,_updateWH:v,_scaleOffset:a,minX:u,minY:h,maxX:d,maxY:f}}(t,r,R),{FAandSFACancel:w,_getLimitXY:x,_animationTo:S,_setTransform:k,_revise:T,dampingNumber:E,xMove:C,yMove:O,xSync:P,ySync:A,_STD:$}=function(e,t,n,r,s,o,i,a,l,c,u,h,d,f){const p=ei(()=>{let e=Number(t.damping);return isNaN(e)?20:e}),g=ei(()=>"all"===t.direction||"horizontal"===t.direction),v=ei(()=>"all"===t.direction||"vertical"===t.direction),m=$n(yf(t.x)),y=$n(yf(t.y));Or(()=>t.x,e=>{m.value=yf(e)}),Or(()=>t.y,e=>{y.value=yf(e)}),Or(m,e=>{T(e)}),Or(y,e=>{E(e)});const _=new uf(1,9*Math.pow(p.value,2)/40,p.value);function b(e,t){let n=!1;return e>s.value?(e=s.value,n=!0):e<i.value&&(e=i.value,n=!0),t>o.value?(t=o.value,n=!0):t<a.value&&(t=a.value,n=!0),{x:e,y:t,outOfBounds:n}}function w(){h&&h.cancel(),u&&u.cancel()}function x(e,n,s,o,i,a){w(),g.value||(e=l.value),v.value||(n=c.value),t.scale||(s=r.value);let h=b(e,n);e=h.x,n=h.y,t.animation?(_._springX._solution=null,_._springY._solution=null,_._springScale._solution=null,_._springX._endPosition=l.value,_._springY._endPosition=c.value,_._springScale._endPosition=r.value,_.setEnd(e,n,s,1),u=mf(_,function(){let e=_.x();S(e.x,e.y,e.scale,o,i,a)},function(){u.cancel()})):S(e,n,s,o,i,a)}function S(s,o,i,a="",u,h){null!==s&&"NaN"!==s.toString()&&"number"==typeof s||(s=l.value||0),null!==o&&"NaN"!==o.toString()&&"number"==typeof o||(o=c.value||0),s=Number(s.toFixed(1)),o=Number(o.toFixed(1)),i=Number(i.toFixed(1)),l.value===s&&c.value===o||u||f("change",{},{x:hf(s,n.x),y:hf(o,n.y),source:a}),t.scale||(i=r.value),i=+(i=d(i)).toFixed(3),h&&i!==r.value&&f("scale",{},{x:s,y:o,scale:i});let p="translateX("+s+"px) translateY("+o+"px) translateZ(0px) scale("+i+")";e.value&&(e.value.style.transform=p,e.value.style.webkitTransform=p,l.value=s,c.value=o,r.value=i)}function k(e){let t=b(l.value,c.value),n=t.x,s=t.y,o=t.outOfBounds;return o&&x(n,s,r.value,e),o}function T(e){if(g.value){if(e+n.x===l.value)return l;u&&u.cancel(),x(e+n.x,y.value+n.y,r.value)}return e}function E(e){if(v.value){if(e+n.y===c.value)return c;u&&u.cancel(),x(m.value+n.x,e+n.y,r.value)}return e}return{FAandSFACancel:w,_getLimitXY:b,_animationTo:x,_setTransform:S,_revise:k,dampingNumber:p,xMove:g,yMove:v,xSync:m,ySync:y,_STD:_}}(t,e,v,r,_,b,m,y,i,a,l,c,R,n);function j(t,n){if(e.scale){t=R(t),g(t),f();const e=x(i.value,a.value),r=e.x,s=e.y;n?S(r,s,t,"",!0,!0):pf(function(){k(r,s,t,"",!0,!0)})}}function B(){o.value=!0}function I(e){s.value=e}function R(e){return e=Math.max(.5,u.value,e),e=Math.min(10,h.value,e)}function L(){if(!e.scale)return!1;j(r.value,!0),I(r.value)}function M(t){return!!e.scale&&(j(t=R(t),!0),I(t),t)}function D(){o.value=!1,I(r.value)}function N(e){e&&(e=s.value*e,B(),j(e))}return{_updateOldScale:I,_endScale:D,_setScale:N,scaleValueSync:d,_updateBoundary:f,_updateOffset:p,_updateWH:g,_scaleOffset:v,minX:m,minY:y,maxX:_,maxY:b,FAandSFACancel:w,_getLimitXY:x,_animationTo:S,_setTransform:k,_revise:T,dampingNumber:E,xMove:C,yMove:O,xSync:P,ySync:A,_STD:$}}(e,n,t,l,c,u,h,d,f,p);function q(){u.value||e.disabled||(R(),b.historyX=[0,0],b.historyY=[0,0],b.historyT=[0,0],F.value&&(i=h.value),U.value&&(a=d.value),n.value.style.willChange="transform",v=null,m=null,g=!0)}function V(t){if(!u.value&&!e.disabled&&g){let n=h.value,r=d.value;if(null===m&&(m=Math.abs(t.detail.dx/t.detail.dy)>1?"htouchmove":"vtouchmove"),F.value&&(n=t.detail.dx+i,b.historyX.shift(),b.historyX.push(n),U.value||null!==v||(v=Math.abs(t.detail.dx/t.detail.dy)<1)),U.value&&(r=t.detail.dy+a,b.historyY.shift(),b.historyY.push(r),F.value||null!==v||(v=Math.abs(t.detail.dy/t.detail.dx)<1)),b.historyT.shift(),b.historyT.push(t.detail.timeStamp),!v){t.preventDefault();let s="touch";n<$.value?e.outOfBounds?(s="touch-out-of-bounds",n=$.value-y.x($.value-n)):n=$.value:n>B.value&&(e.outOfBounds?(s="touch-out-of-bounds",n=B.value+y.x(n-B.value)):n=B.value),r<j.value?e.outOfBounds?(s="touch-out-of-bounds",r=j.value-_.x(j.value-r)):r=j.value:r>I.value&&(e.outOfBounds?(s="touch-out-of-bounds",r=I.value+_.x(r-I.value)):r=I.value),pf(function(){M(n,r,l.value,s)})}}}function X(){if(!u.value&&!e.disabled&&g&&(n.value.style.willChange="auto",g=!1,!v&&!D("out-of-bounds")&&e.inertia)){const e=1e3*(b.historyX[1]-b.historyX[0])/(b.historyT[1]-b.historyT[0]),t=1e3*(b.historyY[1]-b.historyY[0])/(b.historyT[1]-b.historyT[0]),n=h.value,r=d.value;x.setV(e,t),x.setS(n,r);const s=x.delta().x,o=x.delta().y;let i=s+n,a=o+r;i<$.value?(i=$.value,a=r+($.value-n)*o/s):i>B.value&&(i=B.value,a=r+(B.value-n)*o/s),a<j.value?(a=j.value,i=n+(j.value-r)*s/o):a>I.value&&(a=I.value,i=n+(I.value-r)*s/o),x.setEnd(i,a),p=mf(x,function(){let e=x.s(),t=e.x,n=e.y;M(t,n,l.value,"friction")},function(){p.cancel()})}e.outOfBounds||e.inertia||R()}function K(){if(!r.value)return;R();let t=e.scale?E.value:1;O(),P(t),C();let n=L(z.value+A.x,H.value+A.y),s=n.x,o=n.y;M(s,o,t,"",!0),S(t)}return ds(()=>{rf(n.value,e=>{switch(e.detail.state){case"start":q();break;case"move":V(e);break;case"end":X()}}),K(),x.reconfigure(1,w.value),W.reconfigure(1,9*Math.pow(N.value,2)/40,N.value),n.value.style.transformOrigin="center";const e={rootRef:n,setParent:K,_endScale:k,_setScale:T};s(e),vs(()=>{o(e)})}),vs(()=>{R()}),{setParent:K}}(e,s,r);return()=>$o("uni-movable-view",{ref:r},[$o(wd,{onResize:o},null,8,["onResize"]),t.default&&t.default()],512)}});let ff=!1;function pf(e){ff||(ff=!0,requestAnimationFrame(function(){e(),ff=!1}))}function gf(e,t){if(e===t)return 0;let n=e.offsetLeft;return e.offsetParent?n+=gf(e.offsetParent,t):0}function vf(e,t){if(e===t)return 0;let n=e.offsetTop;return e.offsetParent?n+=vf(e.offsetParent,t):0}function mf(e,t,n){let r={id:0,cancelled:!1};return function e(t,n,r,s){if(!t||!t.cancelled){r(n);let o=n.done();o||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,r,s))),o&&s&&s(n)}}(r,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,r),model:e}}function yf(e){return/\d+[ur]px$/i.test(e)?zh(parseFloat(e)):Number(e)||0}class _f{constructor(e){this._drag=e,this._dragLog=Math.log(e),this._x=0,this._v=0,this._startTime=0}set(e,t){this._x=e,this._v=t,this._startTime=(new Date).getTime()}setVelocityByEnd(e){this._v=(e-this._x)*this._dragLog/(Math.pow(this._drag,100)-1)}x(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._x+this._v*t/this._dragLog-this._v/this._dragLog}dx(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._v*t}done(){return Math.abs(this.dx())<3}reconfigure(e){const t=this.x(),n=this.dx();this._drag=e,this._dragLog=Math.log(e),this.set(t,n)}configuration(){const e=this;return[{label:"Friction",read:function(){return e._drag},write:function(t){e.reconfigure(t)},min:.001,max:.1,step:.001}]}}function bf(e,t,n){return e>t-n&&e<t+n}function wf(e,t){return bf(e,0,t)}class xf{constructor(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}_solve(e,t){const n=this._c,r=this._m,s=this._k,o=n*n-4*r*s;if(0===o){const s=-n/(2*r),o=e,i=t/(s*e);return{x:function(e){return(o+i*e)*Math.pow(Math.E,s*e)},dx:function(e){const t=Math.pow(Math.E,s*e);return s*(o+i*e)*t+i*t}}}if(o>0){const s=(-n-Math.sqrt(o))/(2*r),i=(-n+Math.sqrt(o))/(2*r),a=(t-s*e)/(i-s),l=e-a;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,s*e)),n||(n=this._powER2T=Math.pow(Math.E,i*e)),l*t+a*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,s*e)),n||(n=this._powER2T=Math.pow(Math.E,i*e)),l*s*t+a*i*n}}}const i=Math.sqrt(4*r*s-n*n)/(2*r),a=-n/2*r,l=e,c=(t-a*e)/i;return{x:function(e){return Math.pow(Math.E,a*e)*(l*Math.cos(i*e)+c*Math.sin(i*e))},dx:function(e){const t=Math.pow(Math.E,a*e),n=Math.cos(i*e),r=Math.sin(i*e);return t*(c*i*n-l*i*r)+a*t*(c*r+l*n)}}}x(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0}dx(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0}setEnd(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!wf(t,.4)){t=t||0;let r=this._endPosition;this._solution&&(wf(t,.4)&&(t=this._solution.dx((n-this._startTime)/1e3)),r=this._solution.x((n-this._startTime)/1e3),wf(t,.4)&&(t=0),wf(r,.4)&&(r=0),r+=this._endPosition),this._solution&&wf(r-e,.4)&&wf(t,.4)||(this._endPosition=e,this._solution=this._solve(r-this._endPosition,t),this._startTime=n)}}snap(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}}done(e){return e||(e=(new Date).getTime()),bf(this.x(),this._endPosition,.4)&&wf(this.dx(),.4)}reconfigure(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())}springConstant(){return this._k}damping(){return this._c}configuration(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]}}class Sf{constructor(e,t,n){this._extent=e,this._friction=t||new _f(.01),this._spring=n||new xf(1,90,20),this._startTime=0,this._springing=!1,this._springOffset=0}snap(e,t){this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(t)}set(e,t){this._friction.set(e,t),e>0&&t>=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(0)):e<-this._extent&&t<=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(-this._extent)):this._springing=!1,this._startTime=(new Date).getTime()}x(e){if(!this._startTime)return 0;if(e||(e=((new Date).getTime()-this._startTime)/1e3),this._springing)return this._spring.x()+this._springOffset;let t=this._friction.x(e),n=this.dx(e);return(t>0&&n>=0||t<-this._extent&&n<=0)&&(this._springing=!0,this._spring.setEnd(0,n),t<-this._extent?this._springOffset=-this._extent:this._springOffset=0,t=this._spring.x()+this._springOffset),t}dx(e){let t;return t=this._lastTime===e?this._lastDx:this._springing?this._spring.dx(e):this._friction.dx(e),this._lastTime=e,this._lastDx=t,t}done(){return this._springing?this._spring.done():this._friction.done()}setVelocityByEnd(e){this._friction.setVelocityByEnd(e)}configuration(){const e=this._friction.configuration();return e.push.apply(e,this._spring.configuration()),e}}class kf{constructor(e,t){t=t||{},this._element=e,this._options=t,this._enableSnap=t.enableSnap||!1,this._itemSize=t.itemSize||0,this._enableX=t.enableX||!1,this._enableY=t.enableY||!1,this._shouldDispatchScrollEvent=!!t.onScroll,this._enableX?(this._extent=(t.scrollWidth||this._element.offsetWidth)-this._element.parentElement.offsetWidth,this._scrollWidth=t.scrollWidth):(this._extent=(t.scrollHeight||this._element.offsetHeight)-this._element.parentElement.offsetHeight,this._scrollHeight=t.scrollHeight),this._position=0,this._scroll=new Sf(this._extent,t.friction,t.spring),this._onTransitionEnd=this.onTransitionEnd.bind(this),this.updatePosition()}onTouchStart(){this._startPosition=this._position,this._lastChangePos=this._startPosition,this._startPosition>0?this._startPosition/=.5:this._startPosition<-this._extent&&(this._startPosition=(this._startPosition+this._extent)/.5-this._extent),this._animation&&(this._animation.cancel(),this._scrolling=!1),this.updatePosition()}onTouchMove(e,t){let n=this._startPosition;this._enableX?n+=e:this._enableY&&(n+=t),n>0?n*=.5:n<-this._extent&&(n=.5*(n+this._extent)-this._extent),this._position=n,this.updatePosition(),this.dispatchScroll()}onTouchEnd(e,t,n){if(this._enableSnap&&this._position>-this._extent&&this._position<0){if(this._enableY&&(Math.abs(t)<this._itemSize&&Math.abs(n.y)<300||Math.abs(n.y)<150))return void this.snap();if(this._enableX&&(Math.abs(e)<this._itemSize&&Math.abs(n.x)<300||Math.abs(n.x)<150))return void this.snap()}let r;if(this._enableX?this._scroll.set(this._position,n.x):this._enableY&&this._scroll.set(this._position,n.y),this._enableSnap){const e=this._scroll._friction.x(100),t=e%this._itemSize;r=Math.abs(t)>this._itemSize/2?e-(this._itemSize-Math.abs(t)):e-t,r<=0&&r>=-this._extent&&this._scroll.setVelocityByEnd(r)}this._lastTime=Date.now(),this._lastDelay=0,this._scrolling=!0,this._lastChangePos=this._position,this._lastIdx=Math.floor(Math.abs(this._position/this._itemSize)),this._animation=function(e,t,n){const r={id:0,cancelled:!1};return function e(t,n,r,s){if(!t||!t.cancelled){r(n);const o=n.done();o||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,r,s))),o&&s&&s(n)}}(r,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,r),model:e}}(this._scroll,()=>{const e=Date.now(),t=(e-this._scroll._startTime)/1e3,n=this._scroll.x(t);this._position=n,this.updatePosition();const r=this._scroll.dx(t);this._shouldDispatchScrollEvent&&e-this._lastTime>this._lastDelay&&(this.dispatchScroll(),this._lastDelay=Math.abs(2e3/r),this._lastTime=e)},()=>{this._enableSnap&&(r<=0&&r>=-this._extent&&(this._position=r,this.updatePosition()),v(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._shouldDispatchScrollEvent&&this.dispatchScroll(),this._scrolling=!1})}onTransitionEnd(){this._element.style.webkitTransition="",this._element.style.transition="",this._element.removeEventListener("transitionend",this._onTransitionEnd),this._snapping&&(this._snapping=!1),this.dispatchScroll()}snap(){const e=this._itemSize,t=this._position%e,n=Math.abs(t)>this._itemSize/2?this._position-(e-Math.abs(t)):this._position-t;this._position!==n&&(this._snapping=!0,this.scrollTo(-n),v(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize)))}scrollTo(e,t){this._animation&&(this._animation.cancel(),this._scrolling=!1),"number"==typeof e&&(this._position=-e),this._position<-this._extent?this._position=-this._extent:this._position>0&&(this._position=0);const n="transform "+(t||.2)+"s ease-out";this._element.style.webkitTransition="-webkit-"+n,this._element.style.transition=n,this.updatePosition(),this._element.addEventListener("transitionend",this._onTransitionEnd)}dispatchScroll(){if(v(this._options.onScroll)&&Math.round(Number(this._lastPos))!==Math.round(this._position)){this._lastPos=this._position;const e={target:{scrollLeft:this._enableX?-this._position:0,scrollTop:this._enableY?-this._position:0,scrollHeight:this._scrollHeight||this._element.offsetHeight,scrollWidth:this._scrollWidth||this._element.offsetWidth,offsetHeight:this._element.parentElement.offsetHeight,offsetWidth:this._element.parentElement.offsetWidth}};this._options.onScroll(e)}}update(e,t,n){let r=0;const s=this._position;this._enableX?(r=this._element.childNodes.length?(t||this._element.offsetWidth)-this._element.parentElement.offsetWidth:0,this._scrollWidth=t):(r=this._element.childNodes.length?(t||this._element.offsetHeight)-this._element.parentElement.offsetHeight:0,this._scrollHeight=t),"number"==typeof e&&(this._position=-e),this._position<-r?this._position=-r:this._position>0&&(this._position=0),this._itemSize=n||this._itemSize,this.updatePosition(),s!==this._position&&(this.dispatchScroll(),v(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._extent=r,this._scroll._extent=r}updatePosition(){let e="";this._enableX?e="translateX("+this._position+"px) translateZ(0)":this._enableY&&(e="translateY("+this._position+"px) translateZ(0)"),this._element.style.webkitTransform=e,this._element.style.transform=e}isScrolling(){return this._scrolling||this._snapping}}const Tf=Ru({name:"Refresher",props:{refreshState:{type:String,default:""},refresherHeight:{type:Number,default:0},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"}},setup(e,{slots:t}){const n=$n(null),r=ei(()=>{const t={backgroundColor:e.refresherBackground};switch(e.refreshState){case"pulling":t.height=e.refresherHeight+"px";break;case"refreshing":t.height=e.refresherThreshold+"px",t.transition="height 0.3s";break;case"":case"refresherabort":case"restore":t.height="0px",t.transition="height 0.3s"}return t}),s=ei(()=>{const t=e.refresherHeight/e.refresherThreshold;return 360*(t>1?1:t)});return()=>{const{refreshState:o,refresherDefaultStyle:i,refresherThreshold:a}=e;return $o("div",{ref:n,style:r.value,class:"uni-scroll-view-refresher"},["none"!==i?$o("div",{class:"uni-scroll-view-refresh"},[$o("div",{class:"uni-scroll-view-refresh-inner"},["pulling"==o?$o("svg",{key:"refresh__icon",style:{transform:"rotate("+s.value+"deg)"},fill:"#2BD009",class:"uni-scroll-view-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},[$o("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null),$o("path",{d:"M0 0h24v24H0z",fill:"none"},null)],4):null,"refreshing"==o?$o("svg",{key:"refresh__spinner",class:"uni-scroll-view-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},[$o("circle",{cx:"50",cy:"50",r:"20",fill:"none",style:"color: #2bd009","stroke-width":"3"},null)]):null])]):null,"none"===i?$o("div",{class:"uni-scroll-view-refresher-container",style:{height:`${a}px`}},[t.default&&t.default()]):null],4)}}}),Ef=ze(!0),Cf=Ru({name:"ScrollView",compatConfig:{MODE:3},props:{direction:{type:[String],default:"vertical"},scrollX:{type:[Boolean,String],default:!1},scrollY:{type:[Boolean,String],default:!1},showScrollbar:{type:[Boolean,String],default:!0},upperThreshold:{type:[Number,String],default:50},lowerThreshold:{type:[Number,String],default:50},scrollTop:{type:[Number,String],default:0},scrollLeft:{type:[Number,String],default:0},scrollIntoView:{type:String,default:""},scrollWithAnimation:{type:[Boolean,String],default:!1},enableBackToTop:{type:[Boolean,String],default:!1},refresherEnabled:{type:[Boolean,String],default:!1},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"},refresherTriggered:{type:[Boolean,String],default:!1}},emits:["scroll","scrolltoupper","scrolltolower","refresherrefresh","refresherrestore","refresherpulling","refresherabort","update:refresherTriggered"],setup(e,{emit:t,slots:n,expose:r}){const s=$n(null),o=$n(null),i=$n(null),a=$n(null),l=Du(s,t),{state:c,scrollTopNumber:u,scrollLeftNumber:h}=function(e){const t=ei(()=>Number(e.scrollTop)||0),n=ei(()=>Number(e.scrollLeft)||0),r=gn({lastScrollTop:t.value,lastScrollLeft:n.value,lastScrollToUpperTime:0,lastScrollToLowerTime:0,refresherHeight:0,refreshState:""});return{state:r,scrollTopNumber:t,scrollLeftNumber:n}}(e),{realScrollX:d,realScrollY:f}=function(e,t,n,r,s,o,i,a,l){let c=!1,u=0,h=!1,d=()=>{};const f=ei(()=>e.scrollX),p=ei(()=>e.scrollY),g=ei(()=>{let t=Number(e.upperThreshold);return isNaN(t)?50:t}),v=ei(()=>{let t=Number(e.lowerThreshold);return isNaN(t)?50:t});function m(e,t){const n=i.value;let r=0,s="";if(e<0?e=0:"x"===t&&e>n.scrollWidth-n.offsetWidth?e=n.scrollWidth-n.offsetWidth:"y"===t&&e>n.scrollHeight-n.offsetHeight&&(e=n.scrollHeight-n.offsetHeight),"x"===t?r=n.scrollLeft-e:"y"===t&&(r=n.scrollTop-e),0===r)return;let o=a.value;o.style.transition="transform .3s ease-out",o.style.webkitTransition="-webkit-transform .3s ease-out","x"===t?s="translateX("+r+"px) translateZ(0)":"y"===t&&(s="translateY("+r+"px) translateZ(0)"),o.removeEventListener("transitionend",d),o.removeEventListener("webkitTransitionEnd",d),d=()=>x(e,t),o.addEventListener("transitionend",d),o.addEventListener("webkitTransitionEnd",d),"x"===t?n.style.overflowX="hidden":"y"===t&&(n.style.overflowY="hidden"),o.style.transform=s,o.style.webkitTransform=s}function y(e){const n=e.target;s("scroll",e,{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop,scrollHeight:n.scrollHeight,scrollWidth:n.scrollWidth,deltaX:t.lastScrollLeft-n.scrollLeft,deltaY:t.lastScrollTop-n.scrollTop}),p.value&&(n.scrollTop<=g.value&&t.lastScrollTop-n.scrollTop>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(s("scrolltoupper",e,{direction:"top"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollTop+n.offsetHeight+v.value>=n.scrollHeight&&t.lastScrollTop-n.scrollTop<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(s("scrolltolower",e,{direction:"bottom"}),t.lastScrollToLowerTime=e.timeStamp)),f.value&&(n.scrollLeft<=g.value&&t.lastScrollLeft-n.scrollLeft>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(s("scrolltoupper",e,{direction:"left"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollLeft+n.offsetWidth+v.value>=n.scrollWidth&&t.lastScrollLeft-n.scrollLeft<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(s("scrolltolower",e,{direction:"right"}),t.lastScrollToLowerTime=e.timeStamp)),t.lastScrollTop=n.scrollTop,t.lastScrollLeft=n.scrollLeft}function _(t){p.value&&(e.scrollWithAnimation?m(t,"y"):i.value.scrollTop=t)}function b(t){f.value&&(e.scrollWithAnimation?m(t,"x"):i.value.scrollLeft=t)}function w(t){if(t){if(!/^[_a-zA-Z][-_a-zA-Z0-9:]*$/.test(t))return void console.error(`id error: scroll-into-view=${t}`);let n=o.value.querySelector("#"+t);if(n){let t=i.value.getBoundingClientRect(),r=n.getBoundingClientRect();if(f.value){let n=r.left-t.left,s=i.value.scrollLeft+n;e.scrollWithAnimation?m(s,"x"):i.value.scrollLeft=s}if(p.value){let n=r.top-t.top,s=i.value.scrollTop+n;e.scrollWithAnimation?m(s,"y"):i.value.scrollTop=s}}}}function x(e,t){a.value.style.transition="",a.value.style.webkitTransition="",a.value.style.transform="",a.value.style.webkitTransform="";let n=i.value;"x"===t?(n.style.overflowX=f.value?"auto":"hidden",n.scrollLeft=e):"y"===t&&(n.style.overflowY=p.value?"auto":"hidden",n.scrollTop=e),a.value.removeEventListener("transitionend",d),a.value.removeEventListener("webkitTransitionEnd",d)}function S(n){if(e.refresherEnabled){switch(n){case"refreshing":t.refresherHeight=e.refresherThreshold,c||(c=!0,s("refresherpulling",{},{deltaY:t.refresherHeight,dy:t.refresherHeight}),s("refresherrefresh",{},{dy:T.y-k.y}),l("update:refresherTriggered",!0));break;case"restore":case"refresherabort":c=!1,t.refresherHeight=u=0,"restore"===n&&(h=!1,s("refresherrestore",{},{dy:T.y-k.y})),"refresherabort"===n&&h&&(h=!1,s("refresherabort",{},{dy:T.y-k.y}))}t.refreshState=n}}let k={x:0,y:0},T={y:e.refresherThreshold};return ds(()=>{Zn(()=>{_(n.value),b(r.value)}),w(e.scrollIntoView);let o=function(e){e.preventDefault(),e.stopPropagation(),y(e)},a=null,l=function(n){if(null===k)return;let r=n.touches[0].pageX,o=n.touches[0].pageY,l=i.value;if(Math.abs(r-k.x)>Math.abs(o-k.y))if(f.value){if(0===l.scrollLeft&&r>k.x)return void(a=!1);if(l.scrollWidth===l.offsetWidth+l.scrollLeft&&r<k.x)return void(a=!1);a=!0}else a=!1;else if(p.value)if(0===l.scrollTop&&o>k.y)a=!1,e.refresherEnabled&&!1!==n.cancelable&&n.preventDefault();else{if(l.scrollHeight===l.offsetHeight+l.scrollTop&&o<k.y)return void(a=!1);a=!0}else a=!1;if(a&&n.stopPropagation(),0===l.scrollTop&&1===n.touches.length&&S("pulling"),e.refresherEnabled&&"pulling"===t.refreshState){const r=o-k.y;0===u&&(u=o),c?(t.refresherHeight=r+e.refresherThreshold,h=!1):(t.refresherHeight=o-u,t.refresherHeight>0&&(h=!0,s("refresherpulling",n,{deltaY:r,dy:r})))}},d=function(e){1===e.touches.length&&(k={x:e.touches[0].pageX,y:e.touches[0].pageY})},g=function(n){T={x:n.changedTouches[0].pageX,y:n.changedTouches[0].pageY},t.refresherHeight>=e.refresherThreshold?S("refreshing"):S("refresherabort"),k={x:0,y:0},T={x:0,y:e.refresherThreshold}};i.value.addEventListener("touchstart",d,Ef),i.value.addEventListener("touchmove",l,ze(!1)),i.value.addEventListener("scroll",o,ze(!1)),i.value.addEventListener("touchend",g,Ef),gs(()=>{i.value.removeEventListener("touchstart",d),i.value.removeEventListener("touchmove",l),i.value.removeEventListener("scroll",o),i.value.removeEventListener("touchend",g)})}),ts(()=>{p.value&&(i.value.scrollTop=t.lastScrollTop),f.value&&(i.value.scrollLeft=t.lastScrollLeft)}),Or(n,e=>{_(e)}),Or(r,e=>{b(e)}),Or(()=>e.scrollIntoView,e=>{w(e)}),Or(()=>e.refresherTriggered,e=>{!0===e?S("refreshing"):!1===e&&S("restore")}),{realScrollX:f,realScrollY:p,_scrollTopChanged:_,_scrollLeftChanged:b}}(e,c,u,h,l,s,o,a,t),p=ei(()=>{let e="";return d.value?e+="overflow-x:auto;":e+="overflow-x:hidden;",f.value?e+="overflow-y:auto;":e+="overflow-y:hidden;",e}),g=ei(()=>{let t="uni-scroll-view";return!1===e.showScrollbar&&(t+=" uni-scroll-view-scrollbar-hidden"),t});return r({$getMain:()=>o.value}),()=>{const{refresherEnabled:t,refresherBackground:r,refresherDefaultStyle:l,refresherThreshold:u}=e,{refresherHeight:h,refreshState:d}=c;return $o("uni-scroll-view",{ref:s},[$o("div",{ref:i,class:"uni-scroll-view"},[$o("div",{ref:o,style:p.value,class:g.value},[t?$o(Tf,{refreshState:d,refresherHeight:h,refresherThreshold:u,refresherDefaultStyle:l,refresherBackground:r},{default:()=>["none"==l?n.refresher&&n.refresher():null]},8,["refreshState","refresherHeight","refresherThreshold","refresherDefaultStyle","refresherBackground"]):null,$o("div",{ref:a,class:"uni-scroll-view-content"},[n.default&&n.default()],512)],6)],512)],512)}}});function Of(e,t,n,r,s,o){function i(){c&&(clearTimeout(c),c=null)}let a,l,c=null,u=!0,h=0,d=1,f=null,p=!1,g=0,v="";const m=ei(()=>n.value.length>t.displayMultipleItems),y=ei(()=>e.circular&&m.value);function _(s){Math.floor(2*h)===Math.floor(2*s)&&Math.ceil(2*h)===Math.ceil(2*s)||y.value&&function(r){if(!u)for(let s=n.value,o=s.length,i=r+t.displayMultipleItems,a=0;a<o;a++){const t=s[a],n=Math.floor(r/o)*o+a,l=n+o,c=n-o,u=Math.max(r-(n+1),n-i,0),h=Math.max(r-(l+1),l-i,0),d=Math.max(r-(c+1),c-i,0),f=Math.min(u,h,d),p=[n,l,c][[u,h,d].indexOf(f)];t.updatePosition(p,e.vertical)}}(s);const i="translate("+(e.vertical?"0":100*-s*d+"%")+", "+(e.vertical?100*-s*d+"%":"0")+") translateZ(0)",l=r.value;if(l&&(l.style.webkitTransform=i,l.style.transform=i),h=s,!a){if(s%1==0)return;a=s}s-=Math.floor(a);const c=n.value;s<=-(c.length-1)?s+=c.length:s>=c.length&&(s-=c.length),s=a%1>.5||a<0?s-1:s,o("transition",{},{dx:e.vertical?0:s*l.offsetWidth,dy:e.vertical?s*l.offsetHeight:0})}function b(e){const r=n.value.length;if(!r)return-1;const s=(Math.round(e)%r+r)%r;if(y.value){if(r<=t.displayMultipleItems)return 0}else if(s>r-t.displayMultipleItems)return r-t.displayMultipleItems;return s}function w(){f=null}function x(){if(!f)return void(p=!1);const e=f,r=e.toPos,s=e.acc,i=e.endTime,c=e.source,u=i-Date.now();if(u<=0){_(r),f=null,p=!1,a=null;const e=n.value[t.current];if(e){const n=e.getItemId();o("animationfinish",{},{current:t.current,currentItemId:n,source:c})}return}_(r+s*u*u/2),l=requestAnimationFrame(x)}function S(e,r,s){w();const o=t.duration,i=n.value.length;let a=h;if(y.value)if(s<0){for(;a<e;)a+=i;for(;a-i>e;)a-=i}else if(s>0){for(;a>e;)a-=i;for(;a+i<e;)a+=i;a+i-e<e-a&&(a+=i)}else{for(;a+i<e;)a+=i;for(;a-i>e;)a-=i;a+i-e<e-a&&(a+=i)}else"click"===r&&(e=e+t.displayMultipleItems-1<i?e:0);f={toPos:e,acc:2*(a-e)/(o*o),endTime:Date.now()+o,source:r},p||(p=!0,l=requestAnimationFrame(x))}function k(){i();const e=n.value,r=function(){c=null,v="autoplay",y.value?t.current=b(t.current+1):t.current=t.current+t.displayMultipleItems<e.length?t.current+1:0,S(t.current,"autoplay",y.value?1:0),c=setTimeout(r,t.interval)};u||e.length<=t.displayMultipleItems||(c=setTimeout(r,t.interval))}function T(e){e?k():i()}return Or([()=>e.current,()=>e.currentItemId,()=>[...n.value]],()=>{let r=-1;if(e.currentItemId)for(let t=0,s=n.value;t<s.length;t++){if(s[t].getItemId()===e.currentItemId){r=t;break}}r<0&&(r=Math.round(e.current)||0),r=r<0?0:r,t.current!==r&&(v="",t.current=r)}),Or([()=>e.vertical,()=>y.value,()=>t.displayMultipleItems,()=>[...n.value]],function(){i(),f&&(_(f.toPos),f=null);const s=n.value;for(let t=0;t<s.length;t++)s[t].updatePosition(t,e.vertical);d=1;const o=r.value;if(1===t.displayMultipleItems&&s.length){const e=s[0].getBoundingClientRect(),t=o.getBoundingClientRect();d=e.width/t.width,d>0&&d<1||(d=1)}const a=h;h=-2;const l=t.current;l>=0?(u=!1,t.userTracking?(_(a+l-g),g=l):(_(l),e.autoplay&&k())):(u=!0,_(-t.displayMultipleItems-1))}),Or(()=>t.interval,()=>{c&&(i(),k())}),Or(()=>t.current,(e,r)=>{!function(e,r){const s=v;v="";const i=n.value;if(!s){const t=i.length;S(e,"",y.value&&r+(t-e)%t>t/2?1:0)}const a=i[e];if(a){const e=t.currentItemId=a.getItemId();o("change",{},{current:t.current,currentItemId:e,source:s})}}(e,r),s("update:current",e)}),Or(()=>t.currentItemId,e=>{s("update:currentItemId",e)}),Or(()=>e.autoplay&&!t.userTracking,T),T(e.autoplay&&!t.userTracking),ds(()=>{let s=!1,o=0,a=0;function l(e){t.userTracking=!1;const n=o/Math.abs(o);let r=0;!e&&Math.abs(o)>.2&&(r=.5*n);const s=b(h+r);e?_(g):(v="touch",t.current=s,S(s,"touch",0!==r?r:0===s&&y.value&&h>=1?1:0))}rf(r.value,c=>{if(!e.disableTouch&&!u){if("start"===c.detail.state)return t.userTracking=!0,s=!1,i(),g=h,o=0,a=Date.now(),void w();if("end"===c.detail.state)return l(!1);if("cancel"===c.detail.state)return l(!0);if(t.userTracking){if(!s){s=!0;const n=Math.abs(c.detail.dx),r=Math.abs(c.detail.dy);if((n>=r&&e.vertical||n<=r&&!e.vertical)&&(t.userTracking=!1),!t.userTracking)return void(e.autoplay&&k())}return function(s){const i=a;a=Date.now();const l=n.value.length-t.displayMultipleItems;function c(e){return.5-.25/(e+.5)}function u(e,t){let n=g+e;o=.6*o+.4*t,y.value||(n<0||n>l)&&(n<0?n=-c(-n):n>l&&(n=l+c(n-l)),o=0),_(n)}const h=a-i||1,d=r.value;e.vertical?u(-s.dy/d.offsetHeight,-s.ddy/h):u(-s.dx/d.offsetWidth,-s.ddx/h)}(c.detail),!1}}})}),vs(()=>{i(),cancelAnimationFrame(l)}),{onSwiperDotClick:function(e){S(t.current=e,v="click",y.value?1:0)},circularEnabled:y,swiperEnabled:m}}const Pf=Ru({name:"Swiper",props:{indicatorDots:{type:[Boolean,String],default:!1},vertical:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},circular:{type:[Boolean,String],default:!1},interval:{type:[Number,String],default:5e3},duration:{type:[Number,String],default:500},current:{type:[Number,String],default:0},indicatorColor:{type:String,default:""},indicatorActiveColor:{type:String,default:""},previousMargin:{type:String,default:""},nextMargin:{type:String,default:""},currentItemId:{type:String,default:""},skipHiddenItemLayout:{type:[Boolean,String],default:!1},displayMultipleItems:{type:[Number,String],default:1},disableTouch:{type:[Boolean,String],default:!1},navigation:{type:[Boolean,String],default:!1},navigationColor:{type:String,default:"#fff"},navigationActiveColor:{type:String,default:"rgba(53, 53, 53, 0.6)"}},emits:["change","transition","animationfinish","update:current","update:currentItemId"],setup(e,{slots:t,emit:n}){const r=$n(null),s=Du(r,n),o=$n(null),i=$n(null),a=function(e){return gn({interval:ei(()=>{const t=Number(e.interval);return isNaN(t)?5e3:t}),duration:ei(()=>{const t=Number(e.duration);return isNaN(t)?500:t}),displayMultipleItems:ei(()=>{const t=Math.round(e.displayMultipleItems);return isNaN(t)?1:t}),current:Math.round(e.current)||0,currentItemId:e.currentItemId,userTracking:!1})}(e),l=ei(()=>{let t={};return(e.nextMargin||e.previousMargin)&&(t=e.vertical?{left:0,right:0,top:qc(e.previousMargin,!0),bottom:qc(e.nextMargin,!0)}:{top:0,bottom:0,left:qc(e.previousMargin,!0),right:qc(e.nextMargin,!0)}),t}),c=ei(()=>{const t=Math.abs(100/a.displayMultipleItems)+"%";return{width:e.vertical?"100%":t,height:e.vertical?t:"100%"}});let u=[];const h=[],d=$n([]);function f(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n instanceof Element||(n=n.el);const r=h.find(e=>n===e.rootRef.value);r&&e.push(kn(r))}d.value=e}qs("addSwiperContext",function(e){h.push(e),f()});qs("removeSwiperContext",function(e){const t=h.indexOf(e);t>=0&&(h.splice(t,1),f())});const{onSwiperDotClick:p,circularEnabled:g,swiperEnabled:v}=Of(e,a,d,i,n,s);let m=()=>null;return m=Af(r,e,a,p,d,g,v),()=>{const n=t.default&&t.default();return u=Gd(n),$o("uni-swiper",{ref:r},[$o("div",{ref:o,class:"uni-swiper-wrapper"},[$o("div",{class:"uni-swiper-slides",style:l.value},[$o("div",{ref:i,class:"uni-swiper-slide-frame",style:c.value},[n],4)],4),e.indicatorDots&&$o("div",{class:["uni-swiper-dots",e.vertical?"uni-swiper-dots-vertical":"uni-swiper-dots-horizontal"]},[d.value.map((t,n,r)=>$o("div",{onClick:()=>p(n),class:{"uni-swiper-dot":!0,"uni-swiper-dot-active":n<a.current+a.displayMultipleItems&&n>=a.current||n<a.current+a.displayMultipleItems-r.length},style:{background:n===a.current?e.indicatorActiveColor:e.indicatorColor}},null,14,["onClick"]))],2),m()],512)],512)}}}),Af=(e,t,n,r,s,o,i)=>{let a=!1,l=!1,u=!1,h=$n(!1);function d(e,n){const r=e.currentTarget;r&&(r.style.backgroundColor="over"===n?t.navigationActiveColor:"")}Er(()=>{a="auto"===t.navigation,h.value=!0!==t.navigation||a,_()}),Er(()=>{const e=s.value.length,t=!o.value;l=0===n.current&&t,u=n.current===e-1&&t||t&&n.current+n.displayMultipleItems>=e,i.value||(l=!0,u=!0,a&&(h.value=!0))});const f={onMouseover:e=>d(e,"over"),onMouseout:e=>d(e,"out")};function p(e,t,i){if(e.stopPropagation(),i)return;const a=s.value.length;let l=n.current;switch(t){case"prev":l--,l<0&&o.value&&(l=a-1);break;case"next":l++,l>=a&&o.value&&(l=0)}r(l)}const g=()=>Yc(Kc,t.navigationColor,26);let v;const m=n=>{clearTimeout(v);const{clientX:r,clientY:s}=n,{left:o,right:i,top:a,bottom:l,width:c,height:u}=e.value.getBoundingClientRect();let d=!1;if(d=t.vertical?!(s-a<u/3||l-s<u/3):!(r-o<c/3||i-r<c/3),d)return v=setTimeout(()=>{h.value=d},300);h.value=d},y=()=>{h.value=!0};function _(){e.value&&(e.value.removeEventListener("mousemove",m),e.value.removeEventListener("mouseleave",y),a&&(e.value.addEventListener("mousemove",m),e.value.addEventListener("mouseleave",y)))}return ds(_),function(){const e={"uni-swiper-navigation-hide":h.value,"uni-swiper-navigation-vertical":t.vertical};return t.navigation?$o(fo,null,[$o("div",Do({class:["uni-swiper-navigation uni-swiper-navigation-prev",c({"uni-swiper-navigation-disabled":l},e)],onClick:e=>p(e,"prev",l)},f),[g()],16,["onClick"]),$o("div",Do({class:["uni-swiper-navigation uni-swiper-navigation-next",c({"uni-swiper-navigation-disabled":u},e)],onClick:e=>p(e,"next",u)},f),[g()],16,["onClick"])]):null}},$f=Ru({name:"SwiperItem",props:{itemId:{type:String,default:""}},setup(e,{slots:t}){const n=$n(null),r={rootRef:n,getItemId:()=>e.itemId,getBoundingClientRect:()=>n.value.getBoundingClientRect(),updatePosition(e,t){const r=t?"0":100*e+"%",s=t?100*e+"%":"0",o=n.value,i=`translate(${r},${s}) translateZ(0)`;o&&(o.style.webkitTransform=i,o.style.transform=i)}};return ds(()=>{const e=Vs("addSwiperContext");e&&e(r)}),vs(()=>{const e=Vs("removeSwiperContext");e&&e(r)}),()=>$o("uni-swiper-item",{ref:n,style:{position:"absolute",width:"100%",height:"100%"}},[t.default&&t.default()],512)}}),jf={ensp:" ",emsp:" ",nbsp:" "};function Bf(e,t){return function(e,{space:t,decode:n}){let r="",s=!1;for(let o of e)t&&jf[t]&&" "===o&&(o=jf[t]),s?(r+="n"===o?ee:"\\"===o?"\\":"\\"+o,s=!1):"\\"===o?s=!0:r+=o;return n?r.replace(/&nbsp;/g,jf.nbsp).replace(/&ensp;/g,jf.ensp).replace(/&emsp;/g,jf.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'"):r}(e,t).split(ee)}const If=Ru({name:"Text",props:{selectable:{type:[Boolean,String],default:!1},space:{type:String,default:""},decode:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=$n(null);return()=>{const r=[];return t.default&&t.default().forEach(t=>{if(8&t.shapeFlag&&t.type!==go){const n=Bf(t.children,{space:e.space,decode:e.decode}),s=n.length-1;n.forEach((e,t)=>{(0!==t||e)&&r.push(Bo(e)),t!==s&&r.push($o("br"))})}else r.push(t)}),$o("uni-text",{ref:n,selectable:!!e.selectable||null},[$o("span",null,r)],8,["selectable"])}}}),Rf=c({},Ud,{placeholderClass:{type:String,default:"input-placeholder"},autoHeight:{type:[Boolean,String],default:!1},confirmType:{type:String,default:"return",validator:e=>Mf.concat("return").includes(e)}});let Lf=!1;const Mf=["done","go","next","search","send"];const Df=Ru({name:"Textarea",props:Rf,emits:["confirm","linechange",...zd],setup(e,{emit:t,expose:n}){const r=$n(null),s=$n(null),{fieldRef:o,state:i,scopedAttrsState:a,fixDisabledColor:l,trigger:c}=qd(e,r,t),u=ei(()=>i.value.split(ee)),h=ei(()=>Mf.includes(e.confirmType)),d=$n(0),f=$n(null);function p({height:e}){d.value=e}function g(e){"Enter"===e.key&&h.value&&e.preventDefault()}function v(t){if("Enter"===t.key&&h.value){!function(e){c("confirm",e,{value:i.value})}(t);const n=t.target;!e.confirmHold&&n.blur()}}return Or(()=>d.value,t=>{const n=r.value,o=f.value,i=s.value;let a=parseFloat(getComputedStyle(n).lineHeight);isNaN(a)&&(a=o.offsetHeight);var l=Math.round(t/a);c("linechange",{},{height:t,heightRpx:750/window.innerWidth*t,lineCount:l}),e.autoHeight&&(n.style.height="auto",i.style.height=t+"px")}),function(){const e="(prefers-color-scheme: dark)";Lf=0===String(navigator.platform).indexOf("iP")&&0===String(navigator.vendor).indexOf("Apple")&&window.matchMedia(e).media!==e}(),n({$triggerInput:e=>{t("update:modelValue",e.value),t("update:value",e.value),i.value=e.value}}),()=>{let t=e.disabled&&l?$o("textarea",{key:"disabled-textarea",ref:o,value:i.value,tabindex:"-1",readonly:!!e.disabled,maxlength:i.maxlength,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":Lf},style:{overflowY:e.autoHeight?"hidden":"auto",...e.cursorColor&&{caretColor:e.cursorColor}},onFocus:e=>e.target.blur()},null,46,["value","readonly","maxlength","onFocus"]):$o("textarea",{key:"textarea",ref:o,value:i.value,disabled:!!e.disabled,maxlength:i.maxlength,enterkeyhint:e.confirmType,inputmode:e.inputmode,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":Lf},style:{overflowY:e.autoHeight?"hidden":"auto",...e.cursorColor&&{caretColor:e.cursorColor}},onKeydown:g,onKeyup:v},null,46,["value","disabled","maxlength","enterkeyhint","inputmode","onKeydown","onKeyup"]);return $o("uni-textarea",{ref:r},[$o("div",{ref:s,class:"uni-textarea-wrapper"},[Br($o("div",Do(a.attrs,{style:e.placeholderStyle,class:["uni-textarea-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Si,!i.value.length]]),$o("div",{ref:f,class:"uni-textarea-line"},[" "],512),$o("div",{class:"uni-textarea-compute"},[u.value.map(e=>$o("div",null,[e.trim()?e:"."])),$o(wd,{initial:!0,onResize:p},null,8,["initial","onResize"])]),"search"===e.confirmType?$o("form",{action:"",onSubmit:()=>!1,class:"uni-input-form"},[t],40,["onSubmit"]):t],512)],512)}}}),Nf=Ru({name:"View",props:c({},Nu),setup(e,{slots:t}){const n=$n(null),{hovering:r,binding:s}=Fu(e);return()=>{const o=e.hoverClass;return o&&"none"!==o?$o("uni-view",Do({class:r.value?o:"",ref:n},s),[t.default&&t.default()],16):$o("uni-view",{ref:n},[t.default&&t.default()],512)}}});function Ff(e,t,n,r){v(t)&&cs(e,t.bind(n),r)}function Uf(e,t,n){var r;const s=e.mpType||n.$mpType;if(s&&"component"!==s&&(Object.keys(e).forEach(r=>{if(function(e,t,n=!0){return!(n&&!v(t))&&(Ge.indexOf(e)>-1||0===e.indexOf("on"))}(r,e[r],!1)){const s=e[r];f(s)?s.forEach(e=>Ff(r,e,n,t)):Ff(r,s,n,t)}}),"page"===s)){t.__isVisible=!0;try{let e=t.attrs.__pageQuery;0,ru(n,he,e),delete t.attrs.__pageQuery,"preloadPage"!==(null==(r=n.$page)?void 0:r.openType)&&ru(n,se)}catch(o){console.error(o.message+ee+o.stack)}}}function zf(e,t,n){Uf(e,t,n)}function Hf(e,t,n){return e[t]=n}function Wf(e,...t){const n=this[e];return n?n(...t):(console.error(`method ${e} not found`),null)}function qf(e){return function(t,n,r){if(!n)throw t;const s=e._instance;if(!s||!s.proxy)throw t;ru(s.proxy,ae,t)}}function Vf(e,t){return e?[...new Set([].concat(e,t))]:t}function Xf(e){const t=e._context.config;var n;t.errorHandler=Qe(e,qf),n=t.optionMergeStrategies,Ge.forEach(e=>{n[e]=Vf});const r=t.globalProperties;r.$set=Hf,r.$applyOptions=zf,r.$callMethod=Wf,function(e){Ze.forEach(t=>t(e))}(e)}const Kf=Hc("upm");function Yf(){return Vs(Kf)}function Jf(e){const t=function(e){return gn(function(e){{const{navigationBar:t}=e,{titleSize:n,titleColor:r,backgroundColor:s}=t;t.titleText=t.titleText||"",t.type=t.type||"default",t.titleSize=n||"16px",t.titleColor=r||"#000000",t.backgroundColor=s||"#F8F8F8"}if(history.state){const t=history.state.__type__;"redirectTo"!==t&&"reLaunch"!==t||0!==Tp().length||(e.isEntry=!0,e.isQuit=!0)}return e}(JSON.parse(JSON.stringify(nu(Rl().meta,e)))))}(e);return qs(Kf,t),t}function Gf(){return Rl()}function Zf(){return history.state&&history.state.__id__||1}function Qf(){const e=eu();if(!e)return;const t=kp(),n=t.keys();for(const r of n){const e=t.get(r);e.$.__isTabBar?e.$.__isActive=!1:Ep(r)}e.$.__isTabBar&&(e.$.__isVisible=!1,ru(e,oe))}function ep(e,t){return e===t.fullPath||"/"===e&&t.meta.isEntry}function tp(e){const t=kp().values();for(const n of t){const t=n.$page;if(ep(e,t))return n.$.__isActive=!0,t.id}}const np=Bh(ed,({url:e,tabBarText:t,isAutomatedTesting:n},{resolve:r,reject:s})=>{if(_p.handledBeforeEntryPageRoutes)return Qf(),ap({type:ed,url:e,tabBarText:t,isAutomatedTesting:n},tp(e)).then(r).catch(s);wp.push({args:{type:ed,url:e,tabBarText:t,isAutomatedTesting:n},resolve:r,reject:s})},0,od);function rp(){const e=Gc();if(!e)return;const t=e.$page;Ep(Ap(t.path,t.id))}const sp=Bh(Zh,({url:e,isAutomatedTesting:t},{resolve:n,reject:r})=>{if(_p.handledBeforeEntryPageRoutes)return rp(),ap({type:Zh,url:e,isAutomatedTesting:t}).then(n).catch(r);xp.push({args:{type:Zh,url:e,isAutomatedTesting:t},resolve:n,reject:r})},0,rd);function op(){const e=kp().keys();for(const t of e)Ep(t)}const ip=Bh(Qh,({url:e,isAutomatedTesting:t},{resolve:n,reject:r})=>{if(_p.handledBeforeEntryPageRoutes)return op(),ap({type:Qh,url:e,isAutomatedTesting:t}).then(n).catch(r);Sp.push({args:{type:Qh,url:e,isAutomatedTesting:t},resolve:n,reject:r})},0,sd);function ap({type:e,url:t,tabBarText:n,events:r,isAutomatedTesting:s},o){const i=Gp().$router,{path:a,query:l}=function(e){const[t,n]=e.split("?",2);return{path:t,query:Xe(n||"")}}(t);return new Promise((t,c)=>{const u=function(e,t){return{__id__:t||++Cp,__type__:e}}(e,o);i["navigateTo"===e?"push":"replace"]({path:a,query:l,state:u,force:!0}).then(o=>{if(Qa(o))return c(o.message);if("switchTab"===e&&(i.currentRoute.value.meta.tabBarText=n),"navigateTo"===e){const e=i.currentRoute.value.meta;return e.eventChannel?r&&(Object.keys(r).forEach(t=>{e.eventChannel._addListener(t,"on",r[t])}),e.eventChannel._clearCache()):e.eventChannel=new Ye(u.__id__,r),t(s?{__id__:u.__id__}:{eventChannel:e.eventChannel})}return s?t({__id__:u.__id__}):t()})})}let lp;function cp(){var e;return lp||(lp=__uniConfig.tabBar&&gn((e=__uniConfig.tabBar,Gl()&&e.list&&e.list.forEach(e=>{tc(e,["text"])}),e))),lp}const up=window.CSS&&window.CSS.supports;function hp(e){return up&&(up(e)||up.apply(window.CSS,e.split(":")))}const dp=hp("top:env(a)"),fp=hp("top:constant(a)"),pp=hp("backdrop-filter:blur(10px)"),gp=(()=>dp?"env":fp?"constant":"")();function vp(e){return gp?`calc(${e}px + ${gp}(safe-area-inset-bottom))`:`${e}px`}const mp="$$",yp=new Map,_p={handledBeforeEntryPageRoutes:!1},bp=[],wp=[],xp=[],Sp=[];function kp(){return yp}function Tp(){const e=[],t=yp.values();for(const n of t)n.$.__isTabBar?n.$.__isActive&&e.push(n):e.push(n);return e}function Ep(e,t=!0){const n=yp.get(e);n.$.__isUnload=!0,ru(n,de),yp.delete(e),t&&function(e){const t=$p.get(e);t&&($p.delete(e),jp.pruneCacheEntry(t))}(e)}let Cp=Zf();function Op(e){const t=Yf();let n=e.fullPath;return e.meta.isEntry&&-1===n.indexOf(e.meta.route)&&(n="/"+e.meta.route+n.replace("/","")),function(e,t,n,r,s,o){const{id:i,route:a}=r,l=st(r.navigationBar,__uniConfig.themeConfig,o).titleColor;return{id:i,path:Le(a),route:a,fullPath:t,options:n,meta:r,openType:e,eventChannel:s,statusBarStyle:"#ffffff"===l?"light":"dark"}}("navigateTo",n,{},t)}function Pp(e){const t=Op(e.$route);!function(e,t){e.route=t.route,e.$vm=e,e.$page=t,e.$mpType="page",e.$fontFamilySet=new Set,t.meta.isTabBar&&(e.$.__isTabBar=!0,e.$.__isActive=!0)}(e,t),yp.set(Ap(t.path,t.id),e),1===yp.size&&setTimeout(()=>{!function(){if(_p.handledBeforeEntryPageRoutes)return;_p.handledBeforeEntryPageRoutes=!0;const e=[...bp];bp.length=0,e.forEach(({args:e,resolve:t,reject:n})=>ap(e).then(t).catch(n));const t=[...wp];wp.length=0,t.forEach(({args:e,resolve:t,reject:n})=>(Qf(),ap(e,tp(e.url)).then(t).catch(n)));const n=[...xp];xp.length=0,n.forEach(({args:e,resolve:t,reject:n})=>(rp(),ap(e).then(t).catch(n)));const r=[...Sp];Sp.length=0,r.forEach(({args:e,resolve:t,reject:n})=>(op(),ap(e).then(t).catch(n)))}()},0)}function Ap(e,t){return e+mp+t}const $p=new Map,jp={get:e=>$p.get(e),set(e,t){!function(e){const t=parseInt(e.split(mp)[1]);if(!t)return;jp.forEach((e,n)=>{const r=parseInt(n.split(mp)[1]);if(r&&r>t){if(function(e){return"tabBar"===e.props.type}(e))return;jp.delete(n),jp.pruneCacheEntry(e),Zn(()=>{yp.forEach((e,t)=>{e.$.isUnmounted&&yp.delete(t)})})}})}(e),$p.set(e,t)},delete(e){$p.get(e)&&$p.delete(e)},forEach(e){$p.forEach(e)}};function Bp(e,t){!function(e){const t=Rp(e),{body:n}=document;Lp&&n.removeAttribute(Lp),t&&n.setAttribute(t,""),Lp=t}(e),function(e){let t=0,n=0;if("custom"!==e.navigationBar.style&&["default","float"].indexOf(e.navigationBar.type)>-1&&(t=44),e.isTabBar){const e=cp();e.shown&&(n=parseInt(e.height))}var r;zc({"--window-top":(r=t,gp?`calc(${r}px + ${gp}(safe-area-inset-top))`:`${r}px`),"--window-bottom":vp(n)})}(t),function(e){{const t="nvue-dir-"+__uniConfig.nvue["flex-direction"];e.isNVue?(document.body.setAttribute("nvue",""),document.body.setAttribute(t,"")):(document.body.removeAttribute("nvue"),document.body.removeAttribute(t))}}(t),Dp(e,t)}function Ip(e){const t=Rp(e);t&&function(e){const t=document.querySelector("uni-page-body");t&&t.setAttribute(e,"")}(t)}function Rp(e){return e.type.__scopeId}let Lp,Mp;function Dp(e,t){if(document.removeEventListener("touchmove",su),Mp&&document.removeEventListener("scroll",Mp),t.disableScroll)return document.addEventListener("touchmove",su);const{onPageScroll:n,onReachBottom:r}=e,s="transparent"===t.navigationBar.type;if(!(null==n?void 0:n.length)&&!(null==r?void 0:r.length)&&!s)return;const o={},i=e.proxy.$page.id;(n||s)&&(o.onPageScroll=function(e,t,n){return r=>{t&&jv.publishHandler(me,{scrollTop:r},e),n&&jv.emit(e+"."+me,{scrollTop:r})}}(i,n,s)),(null==r?void 0:r.length)&&(o.onReachBottomDistance=t.onReachBottomDistance||50,o.onReachBottom=()=>jv.publishHandler(_e,{},i)),Mp=au(o),requestAnimationFrame(()=>document.addEventListener("scroll",Mp))}function Np(e){const t=Il({history:zp(),strict:!!__uniConfig.router.strict,routes:__uniRoutes,scrollBehavior:Up});t.beforeEach((e,t)=>{var n;e&&t&&e.meta.isTabBar&&t.meta.isTabBar&&(n=t.meta.tabBarIndex,"undefined"!=typeof window&&(Fp[n]={left:window.pageXOffset,top:window.pageYOffset}))}),e.router=t,e.use(t)}let Fp=Object.create(null);const Up=(e,t,n)=>{if(n)return n;if(e&&t&&e.meta.isTabBar&&t.meta.isTabBar){const t=(r=e.meta.tabBarIndex,Fp[r]);if(t)return t}return{left:0,top:0};var r};function zp(){let{routerBase:e}=__uniConfig.router;"/"===e&&(e="");const t=(n=e,(n=location.host?n||location.pathname+location.search:"").includes("#")||(n+="#"),Xa(n));var n;return t.listen((e,t,n)=>{"back"===n.direction&&function(e=1){const t=Tp(),n=t.length-1,r=n-e;for(let s=n;s>r;s--){const e=t[s].$page;Ep(Ap(e.path,e.id),!1)}}(Math.abs(n.delta))}),t}const Hp={install(e){Xf(e),_u(e),Au(e),e.config.warnHandler||(e.config.warnHandler=Wp),Np(e)}};function Wp(e,t,n){if(t){if("PageMetaHead"===t.$.type.name)return;const e=t.$.parent;if(e&&"PageMeta"===e.type.name)return}const r=[`[Vue warn]: ${e}`];n.length&&r.push("\n",n),console.warn(...r)}const qp={class:"uni-async-loading"},Vp=$o("i",{class:"uni-loading"},null,-1),Xp=Lu({name:"AsyncLoading",render:()=>(_o(),ko("div",qp,[Vp]))});function Kp(){window.location.reload()}const Yp=Lu({name:"AsyncError",setup(){sc();const{t:e}=nc();return()=>$o("div",{class:"uni-async-error",onClick:Kp},[e("uni.async.error")],8,["onClick"])}});let Jp;function Gp(){return Jp}function Zp(e){Jp=e,Object.defineProperty(Jp.$.ctx,"$children",{get:()=>Tp().map(e=>e.$vm)});const t=Jp.$.appContext.app;t.component(Xp.name)||t.component(Xp.name,Xp),t.component(Yp.name)||t.component(Yp.name,Yp),function(e){e.$vm=e,e.$mpType="app";const t=$n(nc().getLocale());Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(Jp),function(e,t){const n=e.$options||{};n.globalData=c(n.globalData||{},t),Object.defineProperty(e,"globalData",{get:()=>n.globalData,set(e){n.globalData=e}})}(Jp),Ou(),Ec()}function Qp(e,{clone:t,init:n,setup:r,before:s}){t&&(e=c({},e)),s&&s(e);const o=e.setup;return e.setup=(e,t)=>{const s=Ho();n(s.proxy);const i=r(s);if(o)return o(i||e,t)},e}function eg(e,t){return e&&(e.__esModule||"Module"===e[Symbol.toStringTag])?Qp(e.default,t):Qp(e,t)}function tg(e){return eg(e,{clone:!0,init:Pp,setup(e){e.$pageInstance=e;const t=Gf(),n=qe(t.query);e.attrs.__pageQuery=n,e.proxy.$page.options=n,e.proxy.options=n;const r=Yf();var s,o;return e.onReachBottom=gn([]),e.onPageScroll=gn([]),Or([e.onReachBottom,e.onPageScroll],()=>{e.proxy===Gc()&&Dp(e,r)},{once:!0}),hs(()=>{Bp(e,r)}),ds(()=>{Ip(e);const{onReady:n}=e;n&&I(n),og(t)}),rs(()=>{if(!e.__isVisible){Bp(e,r),e.__isVisible=!0;const{onShow:n}=e;n&&I(n),Zn(()=>{og(t)})}},"ba",s),function(e,t){rs(e,"bda",t)}(()=>{if(e.__isVisible&&!e.__isUnload){e.__isVisible=!1;const{onHide:t}=e;t&&I(t)}}),o=r.id,jv.subscribe(pc(o,uc),gc),gs(()=>{!function(e){jv.unsubscribe(pc(e,uc)),Object.keys(fc).forEach(t=>{0===t.indexOf(e+".")&&delete fc[t]})}(r.id)}),n}})}function ng(){const{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:r}=yg(),s=90===Math.abs(Number(window.orientation))?"landscape":"portrait";Bv.emit(ge,{deviceOrientation:s,size:{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:r}})}function rg(e){S(e.data)&&"WEB_INVOKE_APPSERVICE"===e.data.type&&Bv.emit("onWebInvokeAppService",e.data.data,e.data.pageId)}function sg(){const{emit:e}=Bv;"visible"===document.visibilityState?e(Pe,c({},bd)):e(Ae)}function og(e){const{tabBarText:t,tabBarIndex:n,route:r}=e.meta;t&&ru("onTabItemTap",{index:n,text:t,pagePath:r})}const ig="__DC_STAT_UUID",ag=navigator.cookieEnabled&&(window.localStorage||window.sessionStorage)||{};let lg;function cg(){if(lg=lg||ag[ig],!lg){lg=Date.now()+""+Math.floor(1e7*Math.random());try{ag[ig]=lg}catch(e){}}return lg}function ug(){if(!0!==__uniConfig.darkmode)return m(__uniConfig.darkmode)?__uniConfig.darkmode:"light";try{return window.matchMedia("(prefers-color-scheme: light)").matches?"light":"dark"}catch(e){return"light"}}function hg(){let e,t="0",n="",r="phone";const s=navigator.language;if(Gu){e="iOS";const r=Yu.match(/OS\s([\w_]+)\slike/);r&&(t=r[1].replace(/_/g,"."));const s=Yu.match(/\(([a-zA-Z]+);/);s&&(n=s[1])}else if(Ju){e="Android";const r=Yu.match(/Android[\s/]([\w\.]+)[;\s]/);r&&(t=r[1]);const s=Yu.match(/\((.+?)\)/),o=s?s[1].split(";"):Yu.split(" "),i=[/\bAndroid\b/i,/\bLinux\b/i,/\bU\b/i,/^\s?[a-z][a-z]$/i,/^\s?[a-z][a-z]-[a-z][a-z]$/i,/\bwv\b/i,/\/[\d\.,]+$/,/^\s?[\d\.,]+$/,/\bBrowser\b/i,/\bMobile\b/i];for(let e=0;e<o.length;e++){const t=o[e];if(t.indexOf("Build")>0){n=t.split("Build")[0].trim();break}let r;for(let e=0;e<i.length;e++)if(i[e].test(t)){r=!0;break}if(!r){n=t.trim();break}}}else if(th)n="iPad",e="iOS",r="pad",t=v(window.BigInt)?"14.0":"13.0";else if(Zu||Qu||eh){n="PC",e="PC",r="pc",t="0";let s=Yu.match(/\((.+?)\)/)[1];if(Zu){switch(e="Windows",Zu[1]){case"5.1":t="XP";break;case"6.0":t="Vista";break;case"6.1":t="7";break;case"6.2":t="8";break;case"6.3":t="8.1";break;case"10.0":t="10"}const n=s&&s.match(/[Win|WOW]([\d]+)/);n&&(t+=` x${n[1]}`)}else if(Qu){e="macOS";const n=s&&s.match(/Mac OS X (.+)/)||"";t&&(t=n[1].replace(/_/g,"."),-1!==t.indexOf(";")&&(t=t.split(";")[0]))}else if(eh){e="Linux";const n=s&&s.match(/Linux (.*)/)||"";n&&(t=n[1],-1!==t.indexOf(";")&&(t=t.split(";")[0]))}}else e="Other",t="0",r="unknown";const o=`${e} ${t}`,i=e.toLocaleLowerCase();let a="",l=String(function(){const e=navigator.userAgent,t=e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1,n=e.indexOf("Edge")>-1&&!t,r=e.indexOf("Trident")>-1&&e.indexOf("rv:11.0")>-1;if(t){new RegExp("MSIE (\\d+\\.\\d+);").test(e);const t=parseFloat(RegExp.$1);return t>6?t:6}return n?-1:r?11:-1}());if("-1"!==l)a="IE";else{const e=["Version","Firefox","Chrome","Edge{0,1}"],t=["Safari","Firefox","Chrome","Edge"];for(let n=0;n<e.length;n++){const r=e[n],s=new RegExp(`(${r})/(\\S*)\\b`);s.test(Yu)&&(a=t[n],l=Yu.match(s)[2])}}let c="portrait";const u=void 0===window.screen.orientation?window.orientation:window.screen.orientation.angle;return c=90===Math.abs(u)?"landscape":"portrait",{deviceBrand:void 0,brand:void 0,deviceModel:n,deviceOrientation:c,model:n,system:o,platform:i,browserName:a.toLocaleLowerCase(),browserVersion:l,language:s,deviceType:r,ua:Yu,osname:e,osversion:t,theme:ug()}}const dg=jh(0,()=>{const e=window.devicePixelRatio,t=nh(),n=rh(t),r=sh(t,n),s=function(e,t){return e?Math[t?"min":"max"](screen.height,screen.width):screen.height}(t,n),o=oh(r);let i=window.innerHeight;const a=Mc.top,l={left:Mc.left,right:o-Mc.right,top:Mc.top,bottom:i-Mc.bottom,width:o-Mc.left-Mc.right,height:i-Mc.top-Mc.bottom},{top:c,bottom:u}=function(){const e=document.documentElement.style,t=Fc(),n=Nc(e,"--window-bottom"),r=Nc(e,"--window-left"),s=Nc(e,"--window-right"),o=Nc(e,"--top-window-height");return{top:t,bottom:n?n+Mc.bottom:0,left:r?r+Mc.left:0,right:s?s+Mc.right:0,topWindowHeight:o||0}}();return i-=c,i-=u,{windowTop:c,windowBottom:u,windowWidth:o,windowHeight:i,pixelRatio:e,screenWidth:r,screenHeight:s,statusBarHeight:a,safeArea:l,safeAreaInsets:{top:Mc.top,right:Mc.right,bottom:Mc.bottom,left:Mc.left},screenTop:s-i}});let fg,pg=!0;function gg(){pg&&(fg=hg())}const vg=jh(0,()=>{gg();const{deviceBrand:e,deviceModel:t,brand:n,model:r,platform:s,system:o,deviceOrientation:i,deviceType:a,osname:l,osversion:u}=fg;return c({brand:n,deviceBrand:e,deviceModel:t,devicePixelRatio:window.devicePixelRatio,deviceId:cg(),deviceOrientation:i,deviceType:a,model:r,platform:s,system:o},{})}),mg=jh(0,()=>{gg();const{theme:e,language:t,browserName:n,browserVersion:r}=fg;return c({appId:__uniConfig.appId,appName:__uniConfig.appName,appVersion:__uniConfig.appVersion,appVersionCode:__uniConfig.appVersionCode,appLanguage:Vh?Vh():t,enableDebug:!1,hostSDKVersion:void 0,hostPackageName:void 0,hostFontSizeSetting:void 0,hostName:n,hostVersion:r,hostTheme:e,hostLanguage:t,language:t,SDKVersion:"",theme:e,version:""},{})}),yg=jh(0,()=>{pg=!0,gg(),pg=!1;const e=dg(),t=vg(),n=mg();pg=!0;const{ua:r,browserName:s,browserVersion:o,osname:i,osversion:a}=fg,l=c(e,t,n,{ua:r,browserName:s,browserVersion:o,uniPlatform:"web",uniCompileVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion,fontSizeSetting:void 0,osName:i.toLocaleLowerCase(),osVersion:a,osLanguage:void 0,osTheme:void 0});return delete l.screenTop,delete l.enableDebug,__uniConfig.darkmode||delete l.theme,function(e){let t={};return S(e)&&Object.keys(e).sort().forEach(n=>{const r=n;t[r]=e[r]}),Object.keys(t)?t:e}(l)});const _g=jh(0,(e,t)=>{const n=typeof t,r="string"===n?t:JSON.stringify({type:n,data:t});localStorage.setItem(e,r)});function bg(e){const t=localStorage&&localStorage.getItem(e);if(!m(t))throw new Error("data not found");let n=t;try{const e=function(e){const t=["object","string","number","boolean","undefined"];try{const n=m(e)?JSON.parse(e):e,r=n.type;if(t.indexOf(r)>=0){const e=Object.keys(n);if(2===e.length&&"data"in n){if(typeof n.data===r)return n.data;if("object"===r&&/^\d{4}-\d{2}-\d{2}T\d{2}\:\d{2}\:\d{2}\.\d{3}Z$/.test(n.data))return new Date(n.data)}else if(1===e.length)return""}}catch(n){}}(JSON.parse(t));void 0!==e&&(n=e)}catch(r){}return n}const wg=jh(0,e=>{try{return bg(e)}catch(t){return""}}),xg=jh(0,e=>{localStorage&&localStorage.removeItem(e)}),Sg={image:{jpg:"jpeg",jpe:"jpeg",pbm:"x-portable-bitmap",pgm:"x-portable-graymap",pnm:"x-portable-anymap",ppm:"x-portable-pixmap",psd:"vnd.adobe.photoshop",pic:"x-pict",rgb:"x-rgb",svg:"svg+xml",svgz:"svg+xml",tif:"tiff",xif:"vnd.xiff",wbmp:"vnd.wap.wbmp",wdp:"vnd.ms-photo",xbm:"x-xbitmap",ico:"x-icon"},video:{"3g2":"3gpp2","3gp":"3gpp",avi:"x-msvideo",f4v:"x-f4v",flv:"x-flv",jpgm:"jpm",jpgv:"jpeg",m1v:"mpeg",m2v:"mpeg",mpe:"mpeg",mpg:"mpeg",mpg4:"mpeg",m4v:"x-m4v",mkv:"x-matroska",mov:"quicktime",qt:"quicktime",movie:"x-sgi-movie",mp4v:"mp4",ogv:"ogg",smv:"x-smv",wm:"x-ms-wm",wmv:"x-ms-wmv",wmx:"x-ms-wmx",wvx:"x-ms-wvx"}};let kg=null;const Tg=Bh("chooseImage",({count:e,sourceType:t,extension:n},{resolve:r,reject:s})=>{lc();const{t:o}=nc();kg&&(document.body.removeChild(kg),kg=null),kg=function({count:e,sourceType:t,type:n,extension:r}){Rd();const s=document.createElement("input");return s.type="file",function(e,t){for(const n in t)e.style[n]=t[n]}(s,{position:"absolute",visibility:"hidden",zIndex:"-999",width:"0",height:"0",top:"0",left:"0"}),s.accept=r.map(e=>{{const t=e.replace(".","");return`${n}/${Sg[n][t]||t}`}}).join(","),e&&e>1&&(s.multiple=!0),t instanceof Array&&1===t.length&&"camera"===t[0]&&s.setAttribute("capture","camera"),s}({count:e,sourceType:t,extension:n,type:"image"}),document.body.appendChild(kg),kg.addEventListener("change",function(t){const n=t.target,s=[];if(n&&n.files){const t=n.files.length;for(let r=0;r<t;r++){const t=n.files[r];let o;Object.defineProperty(t,"path",{get:()=>(o=o||yd(t),o)}),r<e&&s.push(t)}}r({get tempFilePaths(){return s.map(({path:e})=>e)},tempFiles:s})}),kg.click(),jd||console.warn(o("uni.chooseFile.notUserActivation"))},0,Kh),Eg={esc:["Esc","Escape"],enter:["Enter"]},Cg=Object.keys(Eg);function Og(){const e=$n(""),t=$n(!1),n=n=>{if(t.value)return;const r=Cg.find(e=>-1!==Eg[e].indexOf(n.key));r&&(e.value=r),Zn(()=>e.value="")};return ds(()=>{document.addEventListener("keyup",n)}),gs(()=>{document.removeEventListener("keyup",n)}),{key:e,disable:t}}function Pg(e,t,n){return t.onClose=(...e)=>(t.visible=!1,n.apply(null,e)),Qi(Vr({setup:()=>()=>(_o(),ko(e,t,null,16))}))}function Ag(e){let t=document.getElementById(e);return t||(t=document.createElement("div"),t.id=e,document.body.append(t)),t}let $g=0,jg="";function Bg(e){let t=$g;$g+=e?1:-1,$g=Math.max(0,$g),$g>0?0===t&&(jg=document.body.style.overflow,document.body.style.overflow="hidden"):(document.body.style.overflow=jg,jg="")}const Ig=Lu({name:"ImageView",props:{src:{type:String,default:""}},setup(e){const t=gn({direction:"none"});let n=1,r=0,s=0,o=0,i=0;function a({detail:e}){n=e.scale}function l(e){const t=e.target.getBoundingClientRect();r=t.width,s=t.height}function c(e){const t=e.target.getBoundingClientRect();o=t.width,i=t.height,h(e)}function u(e){const a=n*r>o,l=n*s>i;t.direction=a&&l?"all":a?"horizontal":l?"vertical":"none",h(e)}function h(e){"all"!==t.direction&&"horizontal"!==t.direction||e.stopPropagation()}return()=>{const n={position:"absolute",left:"0",top:"0",width:"100%",height:"100%"};return $o(Zd,{style:n,onTouchstart:Mu(c),onTouchmove:Mu(h),onTouchend:Mu(u)},{default:()=>[$o(df,{style:n,direction:t.direction,inertia:!0,scale:!0,"scale-min":"1","scale-max":"4",onScale:a},{default:()=>[$o("img",{src:e.src,style:{position:"absolute",left:"50%",top:"50%",transform:"translate(-50%, -50%)",maxHeight:"100%",maxWidth:"100%"},onLoad:l},null,40,["src","onLoad"])]},8,["style","direction","inertia","scale","onScale"])]},8,["style","onTouchstart","onTouchmove","onTouchend"])}}});function Rg(e){let t="number"==typeof e.current?e.current:e.urls.indexOf(e.current);return t=t<0?0:t,t}const Lg=Lu({name:"ImagePreview",props:{urls:{type:Array,default:()=>[]},current:{type:[Number,String],default:0}},emits:["close"],setup(e,{emit:t}){ds(()=>Bg(!0)),vs(()=>Bg(!1));const n=$n(null),r=$n(Rg(e));let s;function o(){s||Zn(()=>{t("close")})}function i(e){r.value=e.detail.current}Or(()=>e.current,()=>r.value=Rg(e)),ds(()=>{const e=n.value;let t=0,r=0;e.addEventListener("mousedown",e=>{s=!1,t=e.clientX,r=e.clientY}),e.addEventListener("mouseup",e=>{(Math.abs(e.clientX-t)>20||Math.abs(e.clientY-r)>20)&&(s=!0)})});const a={position:"absolute","box-sizing":"border-box",top:"0",right:"0",width:"60px",height:"44px",padding:"6px","line-height":"32px","font-size":"26px",color:"white","text-align":"center",cursor:"pointer"};return()=>{let t;return $o("div",{ref:n,style:{display:"block",position:"fixed",left:"0",top:"0",width:"100%",height:"100%",zIndex:999,background:"rgba(0,0,0,0.8)"},onClick:o},[$o(Pf,{navigation:"auto",current:r.value,onChange:i,"indicator-dots":!1,autoplay:!1,style:{position:"absolute",left:"0",top:"0",width:"100%",height:"100%"}},(s=t=e.urls.map(e=>$o($f,null,{default:()=>[$o(Ig,{src:e},null,8,["src"])]})),"function"==typeof s||"[object Object]"===Object.prototype.toString.call(s)&&!To(s)?t:{default:()=>[t],_:1}),8,["current","onChange"]),$o("div",{style:a},[Yc("M17.25 16.156l7.375-7.313q0.281-0.281 0.281-0.641t-0.281-0.641q-0.25-0.25-0.625-0.25t-0.625 0.25l-7.375 7.344-7.313-7.344q-0.25-0.25-0.625-0.25t-0.625 0.25q-0.281 0.25-0.281 0.625t0.281 0.625l7.313 7.344-7.375 7.344q-0.281 0.25-0.281 0.625t0.281 0.625q0.125 0.125 0.281 0.188t0.344 0.063q0.156 0 0.328-0.063t0.297-0.188l7.375-7.344 7.375 7.406q0.125 0.156 0.297 0.219t0.328 0.063q0.188 0 0.344-0.078t0.281-0.203q0.281-0.25 0.281-0.609t-0.281-0.641l-7.375-7.406z","#ffffff",26)],4)],8,["onClick"]);var s}}});let Mg,Dg=null;const Ng=()=>{Dg=null,Zn(()=>{null==Mg||Mg.unmount(),Mg=null})},Fg=Bh("previewImage",(e,{resolve:t})=>{Dg?c(Dg,e):(Dg=gn(e),Zn(()=>{Mg=Pg(Lg,Dg,Ng),Mg.mount(Ag("u-a-p"))})),t()},0,Yh),Ug=Bh("navigateBack",(e,{resolve:t,reject:n})=>{let r=!0;return!0===ru(ve,{from:e.from||"navigateBack"})&&(r=!1),r?(Gp().$router.go(-e.delta),t()):n(ve)},0,id),zg=Bh(Gh,({url:e,events:t,isAutomatedTesting:n},{resolve:r,reject:s})=>{if(_p.handledBeforeEntryPageRoutes)return ap({type:Gh,url:e,events:t,isAutomatedTesting:n}).then(r).catch(s);bp.push({args:{type:Gh,url:e,events:t,isAutomatedTesting:n},resolve:r,reject:s})},0,nd);function Hg(e){__uniConfig.darkmode&&Bv.on(le,e)}function Wg(e){Bv.off(le,e)}function qg(e){let t={};return __uniConfig.darkmode&&(t=st(e,__uniConfig.themeConfig,ug())),__uniConfig.darkmode?t:e}function Vg(e,t){const n=_n(e),r=n?gn(qg(e)):qg(e);return __uniConfig.darkmode&&n&&Or(e,e=>{const t=qg(e);for(const n in t)r[n]=t[n]}),t&&Hg(t),r}const Xg={title:{type:String,default:""},icon:{default:"success",validator:e=>-1!==pd.indexOf(e)},image:{type:String,default:""},duration:{type:Number,default:1500},mask:{type:Boolean,default:!1},visible:{type:Boolean}},Kg="uni-toast__icon",Yg={light:"#fff",dark:"rgba(255,255,255,0.9)"},Jg=e=>Yg[e],Gg=Vr({name:"Toast",props:Xg,setup(e){ic(),ac();const{Icon:t}=function(e){const t=$n(Jg(ug())),n=({theme:e})=>t.value=Jg(e);Er(()=>{e.visible?Hg(n):Wg(n)});const r=ei(()=>{switch(e.icon){case"success":return $o(Yc(Vc,t.value,38),{class:Kg});case"error":return $o(Yc(Xc,t.value,38),{class:Kg});case"loading":return $o("i",{class:[Kg,"uni-loading"]},null,2);default:return null}});return{Icon:r}}(e),n=function(e,{onEsc:t,onEnter:n}){const r=$n(e.visible),{key:s,disable:o}=Og();return Or(()=>e.visible,e=>r.value=e),Or(()=>r.value,e=>o.value=!e),Er(()=>{const{value:e}=s;"esc"===e?t&&t():"enter"===e&&n&&n()}),r}(e,{});return()=>{const{mask:r,duration:s,title:o,image:i}=e;return $o(ci,{name:"uni-fade"},{default:()=>[Br($o("uni-toast",{"data-duration":s},[r?$o("div",{class:"uni-mask",style:"background: transparent;",onTouchmove:Dc},null,40,["onTouchmove"]):"",i||t.value?$o("div",{class:"uni-toast"},[i?$o("img",{src:i,class:Kg},null,10,["src"]):t.value,$o("p",{class:"uni-toast__content"},[o])]):$o("div",{class:"uni-sample-toast"},[$o("p",{class:"uni-simple-toast__text"},[o])])],8,["data-duration"]),[[Si,n.value]])]})}}});let Zg,Qg,ev="";const tv=lt();function nv(e){Zg?c(Zg,e):(Zg=gn(c(e,{visible:!1})),Zn(()=>{tv.run(()=>{Or([()=>Zg.visible,()=>Zg.duration],([e,t])=>{if(e){if(Qg&&clearTimeout(Qg),"onShowLoading"===ev)return;Qg=setTimeout(()=>{av("onHideToast")},t)}else Qg&&clearTimeout(Qg)})}),Bv.on("onHidePopup",()=>av("onHidePopup")),Pg(Gg,Zg,()=>{}).mount(Ag("u-a-t"))})),setTimeout(()=>{Zg.visible=!0},10)}const rv=Bh("showToast",(e,{resolve:t,reject:n})=>{nv(e),ev="onShowToast",t()},0,gd),sv={icon:"loading",duration:1e8,image:""},ov=Bh("showLoading",(e,{resolve:t,reject:n})=>{c(e,sv),nv(e),ev="onShowLoading",t()},0,fd),iv=Bh("hideLoading",(e,{resolve:t,reject:n})=>{av("onHideLoading"),t()});function av(e){const{t:t}=nc();if(!ev)return;let n="";if("onHideToast"===e&&"onShowToast"!==ev?n=t("uni.showToast.unpaired"):"onHideLoading"===e&&"onShowLoading"!==ev&&(n=t("uni.showLoading.unpaired")),n)return console.warn(n);ev="",setTimeout(()=>{Zg.visible=!1},10)}const lv={light:{listItemColor:"#000000",cancelItemColor:"#000000"},dark:{listItemColor:"rgba(255, 255, 255, 0.8)",cancelItemColor:"rgba(255, 255, 255)"}};const cv=Vr({name:"ActionSheet",props:{title:{type:String,default:""},itemList:{type:Array,default:()=>[]},itemColor:{type:String,default:"#000000"},popover:{type:Object,default:null},visible:{type:Boolean,default:!1}},emits:["close"],setup(e,{emit:t}){oc();const n=$n(260),r=$n(0),s=$n(0),o=$n(0),i=$n(0),a=$n(null),l=$n(null),{t:u}=nc(),{_close:h}=function(e,t){function n(e){t("close",e)}const{key:r,disable:s}=Og();return Or(()=>e.visible,e=>s.value=!e),Er(()=>{const{value:e}=r;"esc"===e&&n&&n(-1)}),{_close:n}}(e,t),{popupStyle:d}=function(e){const t=$n(0),n=$n(0),r=ei(()=>t.value>=500&&n.value>=500),s=ei(()=>{const t={content:{transform:"",left:"",top:"",bottom:""},triangle:{left:"",top:"",bottom:"","border-width":"","border-color":""}},s=t.content,o=t.triangle,i=e.popover;function a(e){return Number(e)||0}if(r.value&&i){c(o,{position:"absolute",width:"0",height:"0","margin-left":"-6px","border-style":"solid"});const e=a(i.left),t=a(i.width),r=a(i.top),l=a(i.height),u=e+t/2;s.transform="none !important";const h=Math.max(0,u-150);s.left=`${h}px`;let d=Math.max(12,u-h);d=Math.min(288,d),o.left=`${d}px`;const f=n.value/2;r+l-f>f-r?(s.top="auto",s.bottom=n.value-r+6+"px",o.bottom="-6px",o["border-width"]="6px 6px 0 6px",o["border-color"]="#fcfcfd transparent transparent transparent"):(s.top=`${r+l+6}px`,o.top="-6px",o["border-width"]="0 6px 6px 6px",o["border-color"]="transparent transparent #fcfcfd transparent")}return t});return ds(()=>{const e=()=>{const{windowWidth:e,windowHeight:r,windowTop:s}=yg();t.value=e,n.value=r+(s||0)};window.addEventListener("resize",e),e(),vs(()=>{window.removeEventListener("resize",e)})}),{isDesktop:r,popupStyle:s}}(e);let f;function p(e){const t=o.value+e.deltaY;Math.abs(t)>10?(i.value+=t/3,i.value=i.value>=r.value?r.value:i.value<=0?0:i.value,f.scrollTo(i.value)):o.value=t,e.preventDefault()}ds(()=>{const{scroller:e,handleTouchStart:t,handleTouchMove:n,handleTouchEnd:r}=function(e,t){const n={trackingID:-1,maxDy:0,maxDx:0},r=new kf(e,t);function s(e){const t=e,r=e;return"move"===t.detail.state||"end"===t.detail.state?{x:t.detail.dx,y:t.detail.dy}:{x:r.screenX-n.x,y:r.screenY-n.y}}return{scroller:r,handleTouchStart:function(e){const t=e,s=e;"start"===t.detail.state?(n.trackingID="touch",n.x=t.detail.x,n.y=t.detail.y):(n.trackingID="mouse",n.x=s.screenX,n.y=s.screenY),n.maxDx=0,n.maxDy=0,n.historyX=[0],n.historyY=[0],n.historyTime=[t.detail.timeStamp||s.timeStamp],n.listener=r,r.onTouchStart&&r.onTouchStart(),("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault()},handleTouchMove:function(e){const t=e,r=e;if(-1!==n.trackingID){("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault();const o=s(e);if(o){for(n.maxDy=Math.max(n.maxDy,Math.abs(o.y)),n.maxDx=Math.max(n.maxDx,Math.abs(o.x)),n.historyX.push(o.x),n.historyY.push(o.y),n.historyTime.push(t.detail.timeStamp||r.timeStamp);n.historyTime.length>10;)n.historyTime.shift(),n.historyX.shift(),n.historyY.shift();n.listener&&n.listener.onTouchMove&&n.listener.onTouchMove(o.x,o.y)}}},handleTouchEnd:function(e){if(-1!==n.trackingID){e.preventDefault();const t=s(e);if(t){const e=n.listener;n.trackingID=-1,n.listener=null;const r={x:0,y:0};if(n.historyTime.length>2)for(let t=n.historyTime.length-1,s=n.historyTime[t],o=n.historyX[t],i=n.historyY[t];t>0;){t--;const e=s-n.historyTime[t];if(e>30&&e<50){r.x=(o-n.historyX[t])/(e/1e3),r.y=(i-n.historyY[t])/(e/1e3);break}}n.historyTime=[],n.historyX=[],n.historyY=[],e&&e.onTouchEnd&&e.onTouchEnd(t.x,t.y,r)}}}}}(a.value,{enableY:!0,friction:new _f(1e-4),spring:new xf(2,90,20),onScroll:e=>{i.value=e.target.scrollTop}});f=e,rf(a.value,s=>{if(e)switch(s.detail.state){case"start":t(s);break;case"move":n(s);break;case"end":case"cancel":r(s)}},!0)}),Or(()=>e.visible,()=>{Zn(()=>{e.title&&(s.value=document.querySelector(".uni-actionsheet__title").offsetHeight),f.update(),a.value&&(r.value=a.value.clientHeight-n.value),document.querySelectorAll(".uni-actionsheet__cell").forEach(e=>{!function(e){const t=20;let n=0,r=0;e.addEventListener("touchstart",e=>{const t=e.changedTouches[0];n=t.clientX,r=t.clientY}),e.addEventListener("touchend",e=>{const s=e.changedTouches[0];if(Math.abs(s.clientX-n)<t&&Math.abs(s.clientY-r)<t){const t=e.target,n=e.currentTarget,r=new CustomEvent("click",{bubbles:!0,cancelable:!0,target:t,currentTarget:n});["screenX","screenY","clientX","clientY","pageX","pageY"].forEach(e=>{r[e]=s[e]}),e.target.dispatchEvent(r)}})}(e)})})});const g=function(e){const t=gn({listItemColor:"#000",cancelItemColor:"#000"}),n=({theme:e})=>{!function(e,t){["listItemColor","cancelItemColor"].forEach(n=>{t[n]=lv[e][n]})}(e,t)};return Er(()=>{e.visible?(t.listItemColor=t.cancelItemColor=e.itemColor,"#000"===e.itemColor&&(n({theme:ug()}),Hg(n))):Wg(n)}),t}(e);return()=>$o("uni-actionsheet",{onTouchmove:Dc},[$o(ci,{name:"uni-fade"},{default:()=>[Br($o("div",{class:"uni-mask uni-actionsheet__mask",onClick:()=>h(-1)},null,8,["onClick"]),[[Si,e.visible]])]}),$o("div",{class:["uni-actionsheet",{"uni-actionsheet_toggle":e.visible}],style:d.value.content},[$o("div",{ref:l,class:"uni-actionsheet__menu",onWheel:p},[e.title?$o(fo,null,[$o("div",{class:"uni-actionsheet__cell",style:{height:`${s.value}px`}},null),$o("div",{class:"uni-actionsheet__title"},[e.title])]):"",$o("div",{style:{maxHeight:`${n.value}px`,overflow:"hidden"}},[$o("div",{ref:a},[e.itemList.map((e,t)=>$o("div",{key:t,style:{color:g.listItemColor},class:"uni-actionsheet__cell",onClick:()=>h(t)},[e],12,["onClick"]))],512)])],40,["onWheel"]),$o("div",{class:"uni-actionsheet__action"},[$o("div",{style:{color:g.cancelItemColor},class:"uni-actionsheet__cell",onClick:()=>h(-1)},[u("uni.showActionSheet.cancel")],12,["onClick"])]),$o("div",{style:d.value.triangle},null,4)],6)],40,["onTouchmove"])}});let uv,hv,dv;const fv=Me(()=>{Bv.on("onHidePopup",()=>dv.visible=!1)});function pv(e){-1===e?hv&&hv("cancel"):uv&&uv({tapIndex:e})}const gv=Bh("showActionSheet",(e,{resolve:t,reject:n})=>{fv(),uv=t,hv=n,dv?(c(dv,e),dv.visible=!0):(dv=gn(e),Zn(()=>(Pg(cv,dv,pv).mount(Ag("u-s-a-s")),Zn(()=>dv.visible=!0))))},0,dd),vv=Bh("loadFontFace",({family:e,source:t,desc:n},{resolve:r,reject:s})=>{(function(e,t,n){const r=document.fonts;if(r){const s=new FontFace(e,t,n);return s.load().then(()=>{r.add&&r.add(s)})}return new Promise(r=>{const s=document.createElement("style"),o=[];if(n){const{style:e,weight:t,stretch:r,unicodeRange:s,variant:i,featureSettings:a}=n;e&&o.push(`font-style:${e}`),t&&o.push(`font-weight:${t}`),r&&o.push(`font-stretch:${r}`),s&&o.push(`unicode-range:${s}`),i&&o.push(`font-variant:${i}`),a&&o.push(`font-feature-settings:${a}`)}s.innerText=`@font-face{font-family:"${e}";src:${t};${o.join(";")}}`,document.head.appendChild(s),r()})})(e,t=t.startsWith('url("')||t.startsWith("url('")?`url('${Ku(t.substring(5,t.length-2))}')`:t.startsWith("url(")?`url('${Ku(t.substring(4,t.length-1))}')`:Ku(t),n).then(()=>{r()}).catch(e=>{s(`loadFontFace:fail ${e}`)})});function mv(e){function t(){var t;t=e.navigationBar.titleText,document.title=t,Bv.emit("onNavigationBarChange",{titleText:t})}Er(t),ts(t)}const yv=Bh(vd,(e,{resolve:t})=>{Bv.invokeViewMethod(vd,{},Qc()),t()}),_v=Lu({name:"TabBar",setup(){const e=$n([]),t=cp(),n=Vg(t,()=>{const e=qg(t);n.backgroundColor=e.backgroundColor,n.borderStyle=e.borderStyle,n.color=e.color,n.selectedColor=e.selectedColor,n.blurEffect=e.blurEffect,n.midButton=e.midButton,e.list&&e.list.length&&e.list.forEach((e,t)=>{n.list[t].iconPath=e.iconPath,n.list[t].selectedIconPath=e.selectedIconPath})});!function(e,t){function n(){let n=[];n=e.list.filter(e=>!1!==e.visible),t.value=n}$n(c({type:"midButton"},e.midButton)),Er(n)}(n,e),function(e){Or(()=>e.shown,t=>{zc({"--window-bottom":vp(t?parseInt(e.height):0)})})}(n);const r=function(e,t,n){return Er(()=>{const r=e.meta;if(r.isTabBar){const e=r.route,s=n.value.findIndex(t=>t.pagePath===e);t.selectedIndex=s}}),(t,n)=>{const{type:r}=t;return()=>{const{pagePath:r,text:s}=t;let o=Le(r);o===__uniRoutes[0].alias&&(o="/"),e.path!==o?np({from:"tabBar",url:o,tabBarText:s}):ru("onTabItemTap",{index:n,text:s,pagePath:r})}}}(Rl(),n,e),{style:s,borderStyle:o,placeholderStyle:i}=function(e){const t=ei(()=>{let t=e.backgroundColor;const n=e.blurEffect;return t||pp&&n&&"none"!==n&&(t=Sv[n]),{backgroundColor:t||bv,backdropFilter:"none"!==n?"blur(10px)":n}}),n=ei(()=>{const{borderStyle:t,borderColor:n}=e;return n&&m(n)?{backgroundColor:n}:{backgroundColor:kv[t]||kv.black}}),r=ei(()=>({height:e.height}));return{style:t,borderStyle:n,placeholderStyle:r}}(n);return ds(()=>{n.iconfontSrc&&vv({family:"UniTabbarIconFont",source:`url("${n.iconfontSrc}")`})}),()=>{const t=function(e,t,n){const{selectedIndex:r,selectedColor:s,color:o}=e;return n.value.map((n,i)=>{const a=r===i;return function(e,t,n,r,s,o,i,a){return $o("div",{key:i,class:"uni-tabbar__item",onClick:a(s,i)},[Tv(e,t||"",n,r,s,o)],8,["onClick"])}(a?s:o,a&&n.selectedIconPath||n.iconPath||"",n.iconfont?a&&n.iconfont.selectedText||n.iconfont.text:void 0,n.iconfont?a&&n.iconfont.selectedColor||n.iconfont.color:void 0,n,e,i,t)})}(n,r,e);return $o("uni-tabbar",{class:"uni-tabbar-"+n.position},[$o("div",{class:"uni-tabbar",style:s.value},[$o("div",{class:"uni-tabbar-border",style:o.value},null,4),t],4),$o("div",{class:"uni-placeholder",style:i.value},null,4)],2)}}});const bv="#f7f7fa",wv="rgb(0, 0, 0, 0.8)",xv="rgb(250, 250, 250, 0.8)",Sv={dark:wv,light:xv,extralight:xv},kv={white:"rgba(255, 255, 255, 0.33)",black:"rgba(0, 0, 0, 0.33)"};function Tv(e,t,n,r,s,o){const{height:i}=o;return $o("div",{class:"uni-tabbar__bd",style:{height:i}},[n?Cv(n,r||wv,s,o):t&&Ev(t,s,o),s.text&&Ov(e,s,o),s.redDot&&Pv(s.badge)],4)}function Ev(e,t,n){const{type:r,text:s}=t,{iconWidth:o}=n;return $o("div",{class:"uni-tabbar__icon"+(s?" uni-tabbar__icon__diff":""),style:{width:o,height:o}},["midButton"!==r&&$o("img",{src:Ku(e)},null,8,["src"])],6)}function Cv(e,t,n,r){var s;const{type:o,text:i}=n,{iconWidth:a}=r,l="uni-tabbar__icon"+(i?" uni-tabbar__icon__diff":""),c={width:a,height:a},u={fontSize:(null==(s=n.iconfont)?void 0:s.fontSize)||a,color:t};return $o("div",{class:l,style:c},["midButton"!==o&&$o("div",{class:"uni-tabbar__iconfont",style:u},[e],4)],6)}function Ov(e,t,n){const{iconPath:r,text:s}=t,{fontSize:o,spacing:i}=n;return $o("div",{class:"uni-tabbar__label",style:{color:e,fontSize:o,lineHeight:r?"normal":1.8,marginTop:r?i:"inherit"}},[s],4)}function Pv(e){return $o("div",{class:"uni-tabbar__reddot"+(e?" uni-tabbar__badge":"")},[e],2)}const Av="0px",$v=Lu({name:"Layout",setup(e,{emit:t}){const n=$n(null);Uc({"--status-bar-height":Av,"--top-window-height":Av,"--window-left":Av,"--window-right":Av,"--window-margin":Av,"--tab-bar-height":Av});const r=function(){const e=Rl();return{routeKey:ei(()=>Ap("/"+e.meta.route,Zf())),isTabBar:ei(()=>e.meta.isTabBar),routeCache:jp}}(),{layoutState:s,windowState:o}=function(){Gf();{const e=gn({marginWidth:0,leftWindowWidth:0,rightWindowWidth:0});return Or(()=>e.marginWidth,e=>Uc({"--window-margin":e+"px"})),Or(()=>e.leftWindowWidth+e.marginWidth,e=>{Uc({"--window-left":e+"px"})}),Or(()=>e.rightWindowWidth+e.marginWidth,e=>{Uc({"--window-right":e+"px"})}),{layoutState:e,windowState:ei(()=>({}))}}}();!function(e,t){const n=Gf();function r(){const r=document.body.clientWidth,s=Tp();let o={};if(s.length>0){o=s[s.length-1].$page.meta}else{const e=cu(n.path,!0);e&&(o=e.meta)}const i=parseInt(String((d(o,"maxWidth")?o.maxWidth:__uniConfig.globalStyle.maxWidth)||Number.MAX_SAFE_INTEGER));let a=!1;a=r>i,a&&i?(e.marginWidth=(r-i)/2,Zn(()=>{const e=t.value;e&&e.setAttribute("style","max-width:"+i+"px;margin:0 auto;")})):(e.marginWidth=0,Zn(()=>{const e=t.value;e&&e.removeAttribute("style")}))}Or([()=>n.path],r),ds(()=>{r(),window.addEventListener("resize",r)})}(s,n);const i=function(){const e=Gf(),t=cp(),n=ei(()=>e.meta.isTabBar&&t.shown);return Uc({"--tab-bar-height":t.height}),n}(),a=function(e){const t=$n(!1);return ei(()=>({"uni-app--showtabbar":e&&e.value,"uni-app--maxwidth":t.value}))}(i);return()=>{const e=function(e){const t=function({routeKey:e,isTabBar:t,routeCache:n}){return $o(Bl,null,{default:fr(({Component:r})=>[(_o(),ko(Qr,{matchBy:"key",cache:n},[(_o(),ko(wr(r),{type:t.value?"tabBar":"",key:e.value}))],1032,["cache"]))]),_:1})}(e);return t}(r),t=function(e){return Br($o(_v,null,null,512),[[Si,e.value]])}(i);return $o("uni-app",{ref:n,class:a.value},[e,t],2)}}});const jv=c(vc,{publishHandler(e,t,n){Bv.subscribeHandler(e,t,n)}}),Bv=c(xu,{publishHandler(e,t,n){jv.subscribeHandler(e,t,n)}}),Iv=Lu({name:"PageHead",setup(){const e=$n(null),t=Yf(),n=Vg(t.navigationBar,()=>{const e=qg(t.navigationBar);n.backgroundColor=e.backgroundColor,n.titleColor=e.titleColor}),{clazz:r,style:s}=function(e){const t=ei(()=>{const{type:t,titlePenetrate:n,shadowColorType:r}=e,s={"uni-page-head":!0,"uni-page-head-transparent":"transparent"===t,"uni-page-head-titlePenetrate":"YES"===n,"uni-page-head-shadow":!!r};return r&&(s[`uni-page-head-shadow-${r}`]=!0),s}),n=ei(()=>({backgroundColor:e.backgroundColor,color:e.titleColor,transitionDuration:e.duration,transitionTimingFunction:e.timingFunc}));return{clazz:t,style:n}}(n);return()=>{const o=function(e,t){if(!t)return $o("div",{class:"uni-page-head-btn",onClick:Lv},[Yc(Kc,"transparent"===e.type?"#fff":e.titleColor,26)],8,["onClick"])}(n,t.isQuit),i=n.type||"default",a="transparent"!==i&&"float"!==i&&$o("div",{class:{"uni-placeholder":!0,"uni-placeholder-titlePenetrate":n.titlePenetrate}},null,2);return $o("uni-page-head",{"uni-page-head-type":i},[$o("div",{ref:e,class:r.value,style:s.value},[$o("div",{class:"uni-page-head-hd"},[o]),Rv(n),$o("div",{class:"uni-page-head-ft"},[])],6),a],8,["uni-page-head-type"])}}});function Rv(e,t){return function({type:e,loading:t,titleSize:n,titleText:r,titleImage:s}){return $o("div",{class:"uni-page-head-bd"},[$o("div",{style:{fontSize:n,opacity:"transparent"===e?0:1},class:"uni-page-head__title"},[t?$o("i",{class:"uni-loading"},null):s?$o("img",{src:s,class:"uni-page-head__title_image"},null,8,["src"]):r],4)])}(e)}function Lv(){1===Tp().length?ip({url:"/"}):Ug({from:"backbutton",success(){}})}const Mv=Lu({name:"PageBody",setup(e,t){const n=!1,r=$n(null);return Or(()=>n.enablePullDownRefresh,()=>{r.value=null},{immediate:!0}),()=>$o(fo,null,[!1,$o("uni-page-wrapper",r.value,[$o("uni-page-body",null,[xs(t.slots,"default")])],16)])}}),Dv=Lu({name:"Page",setup(e,t){const n=Jf(Zf()),r=n.navigationBar,s={};return mv(n),()=>$o("uni-page",{"data-page":n.route,style:s},"custom"!==r.style?[$o(Iv),Nv(t)]:[Nv(t)])}});function Nv(e){return _o(),ko(Mv,{key:0},{default:fr(()=>[xs(e.slots,"page")]),_:3})}const Fv={loading:"AsyncLoading",error:"AsyncError",delay:200,timeout:6e4,suspensible:!0};window.uni={},window.wx={},window.rpx2px=zh;const Uv=Object.assign({}),zv=Object.assign;window.__uniConfig=zv({easycom:{autoscan:!0,custom:{"^uni-(.*)":"@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"}},tabBar:{position:"bottom",color:"#999999",selectedColor:"#ff6b6b",borderStyle:"black",blurEffect:"none",fontSize:"16px",iconWidth:"24px",spacing:"3px",height:"50px",list:[{pagePath:"pages/index/index",text:"首页"},{pagePath:"pages/discover/discover",text:"发现"},{pagePath:"pages/message/message",text:"消息"},{pagePath:"pages/profile/profile",text:"我的"}],backgroundColor:"#ffffff",selectedIndex:0,shown:!0},globalStyle:{navigationBar:{backgroundColor:"#ffffff",titleText:"Biu",type:"default",titleColor:"#000000"},isNVue:!1},compilerVersion:"4.75"},{appId:"__UNI__4DA9899",appName:"Biu - 五线城市社交",appVersion:"1.0.0",appVersionCode:"100",async:Fv,debug:!1,networkTimeout:{request:6e4,connectSocket:6e4,uploadFile:6e4,downloadFile:6e4},sdkConfigs:{},qqMapKey:void 0,bMapKey:void 0,googleMapKey:void 0,aMapKey:void 0,aMapSecurityJsCode:void 0,aMapServiceHost:void 0,nvue:{"flex-direction":"column"},locale:"",fallbackLocale:"",locales:Object.keys(Uv).reduce((e,t)=>{const n=t.replace(/\.\/locale\/(uni-app.)?(.*).json/,"$2");return zv(e[n]||(e[n]={}),Uv[t].default),e},{}),router:{mode:"hash",base:"/",assets:"assets",routerBase:"/"},darkmode:!1,themeConfig:{}}),window.__uniLayout=window.__uniLayout||{};const Hv={delay:Fv.delay,timeout:Fv.timeout,suspensible:Fv.suspensible};Fv.loading&&(Hv.loadingComponent={name:"SystemAsyncLoading",render:()=>$o(_r(Fv.loading))}),Fv.error&&(Hv.errorComponent={name:"SystemAsyncError",render:()=>$o(_r(Fv.error))});const Wv=()=>t(()=>import("./pages-index-index.CuqwsmnY.js"),__vite__mapDeps([0,1,2,3,4,5,6,7])).then(e=>tg(e.default||e)),qv=Kr(zv({loader:Wv},Hv)),Vv=()=>t(()=>import("./pages-discover-discover.DQScCRVS.js"),__vite__mapDeps([8,6,9])).then(e=>tg(e.default||e)),Xv=Kr(zv({loader:Vv},Hv)),Kv=()=>t(()=>import("./pages-profile-profile.jszcMxNU.js"),__vite__mapDeps([10,4,2,6,11])).then(e=>tg(e.default||e)),Yv=Kr(zv({loader:Kv},Hv)),Jv=()=>t(()=>import("./pages-search-search.C0Cw1drz.js"),__vite__mapDeps([12,6,13])).then(e=>tg(e.default||e)),Gv=Kr(zv({loader:Jv},Hv)),Zv=()=>t(()=>import("./pages-login-login.w9Ntw37g.js"),__vite__mapDeps([14,6,15])).then(e=>tg(e.default||e)),Qv=Kr(zv({loader:Zv},Hv)),em=()=>t(()=>import("./pages-register-register.6BTAnS_k.js"),__vite__mapDeps([16,6,17])).then(e=>tg(e.default||e)),tm=Kr(zv({loader:em},Hv)),nm=()=>t(()=>import("./pages-register-register-test.BsndkOW0.js"),__vite__mapDeps([18,6,19])).then(e=>tg(e.default||e)),rm=Kr(zv({loader:nm},Hv)),sm=()=>t(()=>import("./pages-register-test-registration.DRcGnk2H.js"),__vite__mapDeps([20,6,21])).then(e=>tg(e.default||e)),om=Kr(zv({loader:sm},Hv)),im=()=>t(()=>import("./pages-create-post-create-post.B30wF2Qh.js"),__vite__mapDeps([22,1,2,6,23])).then(e=>tg(e.default||e)),am=Kr(zv({loader:im},Hv)),lm=()=>t(()=>import("./pages-message-message.BnmwPmuG.js"),__vite__mapDeps([24,6,25])).then(e=>tg(e.default||e)),cm=Kr(zv({loader:lm},Hv)),um=()=>t(()=>import("./pages-post-detail-post-detail.O9Nffhl2.js"),__vite__mapDeps([26,5,4,3,1,2,6,27])).then(e=>tg(e.default||e)),hm=Kr(zv({loader:um},Hv)),dm=()=>t(()=>import("./pages-avatar-test-avatar-test.D8ZBqktR.js"),__vite__mapDeps([28,2,6,29])).then(e=>tg(e.default||e)),fm=Kr(zv({loader:dm},Hv)),pm=()=>t(()=>import("./pages-debug-database-debug.kpuEfPJH.js"),__vite__mapDeps([30,1,2,3,6,31])).then(e=>tg(e.default||e)),gm=Kr(zv({loader:pm},Hv));function vm(e,t){return _o(),ko(Dv,null,{page:fr(()=>[$o(e,zv({},t,{ref:"page"}),null,512)]),_:1})}
/*!
  * pinia v2.0.36
  * (c) 2023 Eduardo San Martin Morote
  * @license MIT
  */
let mm;window.__uniRoutes=[{path:"/",alias:"/pages/index/index",component:{setup(){const e=Gp(),t=e&&e.$route&&e.$route.query||{};return()=>vm(qv,t)}},loader:Wv,meta:{isQuit:!0,isEntry:!0,isTabBar:!0,tabBarIndex:0,navigationBar:{titleText:"首页",type:"default"},isNVue:!1}},{path:"/pages/discover/discover",component:{setup(){const e=Gp(),t=e&&e.$route&&e.$route.query||{};return()=>vm(Xv,t)}},loader:Vv,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:1,navigationBar:{titleText:"发现",type:"default"},isNVue:!1}},{path:"/pages/profile/profile",component:{setup(){const e=Gp(),t=e&&e.$route&&e.$route.query||{};return()=>vm(Yv,t)}},loader:Kv,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:3,navigationBar:{titleText:"我的",type:"default"},isNVue:!1}},{path:"/pages/search/search",component:{setup(){const e=Gp(),t=e&&e.$route&&e.$route.query||{};return()=>vm(Gv,t)}},loader:Jv,meta:{navigationBar:{titleText:"搜索",type:"default"},isNVue:!1}},{path:"/pages/login/login",component:{setup(){const e=Gp(),t=e&&e.$route&&e.$route.query||{};return()=>vm(Qv,t)}},loader:Zv,meta:{navigationBar:{titleText:"登录",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/register/register",component:{setup(){const e=Gp(),t=e&&e.$route&&e.$route.query||{};return()=>vm(tm,t)}},loader:em,meta:{navigationBar:{titleText:"注册",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/register/register-test",component:{setup(){const e=Gp(),t=e&&e.$route&&e.$route.query||{};return()=>vm(rm,t)}},loader:nm,meta:{navigationBar:{titleText:"注册测试",type:"default"},isNVue:!1}},{path:"/pages/register/test-registration",component:{setup(){const e=Gp(),t=e&&e.$route&&e.$route.query||{};return()=>vm(om,t)}},loader:sm,meta:{navigationBar:{titleText:"注册流程测试",type:"default"},isNVue:!1}},{path:"/pages/create-post/create-post",component:{setup(){const e=Gp(),t=e&&e.$route&&e.$route.query||{};return()=>vm(am,t)}},loader:im,meta:{navigationBar:{titleText:"发布动态",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/message/message",component:{setup(){const e=Gp(),t=e&&e.$route&&e.$route.query||{};return()=>vm(cm,t)}},loader:lm,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:2,navigationBar:{titleText:"消息",type:"default"},isNVue:!1}},{path:"/pages/post-detail/post-detail",component:{setup(){const e=Gp(),t=e&&e.$route&&e.$route.query||{};return()=>vm(hm,t)}},loader:um,meta:{navigationBar:{titleText:"动态详情",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/avatar-test/avatar-test",component:{setup(){const e=Gp(),t=e&&e.$route&&e.$route.query||{};return()=>vm(fm,t)}},loader:dm,meta:{navigationBar:{titleText:"头像测试",type:"default"},isNVue:!1}},{path:"/pages/debug/database-debug",component:{setup(){const e=Gp(),t=e&&e.$route&&e.$route.query||{};return()=>vm(gm,t)}},loader:pm,meta:{navigationBar:{titleText:"数据库调试",type:"default"},isNVue:!1}}].map(e=>(e.meta.route=(e.alias||e.path).slice(1),e));const ym=e=>mm=e,_m=Symbol();function bm(e){return e&&"object"==typeof e&&"[object Object]"===Object.prototype.toString.call(e)&&"function"!=typeof e.toJSON}var wm,xm;(xm=wm||(wm={})).direct="direct",xm.patchObject="patch object",xm.patchFunction="patch function";const Sm=()=>{};function km(e,t,n,r=Sm){e.push(t);const s=()=>{const n=e.indexOf(t);n>-1&&(e.splice(n,1),r())};var o;return!n&&ct()&&(o=s,ot&&ot.cleanups.push(o)),s}function Tm(e,...t){e.slice().forEach(e=>{e(...t)})}function Em(e,t){e instanceof Map&&t instanceof Map&&t.forEach((t,n)=>e.set(n,t)),e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const r=t[n],s=e[n];bm(s)&&bm(r)&&e.hasOwnProperty(n)&&!An(r)&&!_n(r)?e[n]=Em(s,r):e[n]=r}return e}const Cm=Symbol();function Om(e){return!bm(e)||!e.hasOwnProperty(Cm)}const{assign:Pm}=Object;function Am(e){return!(!An(e)||!e.effect)}function $m(e,t,n,r){const{state:s,actions:o,getters:i}=t,a=n.state.value[e];let l;return l=jm(e,function(){a||(n.state.value[e]=s?s():{});const t=function(e){const t=f(e)?new Array(e.length):{};for(const n in e)t[n]=Nn(e,n);return t}(n.state.value[e]);return Pm(t,o,Object.keys(i||{}).reduce((t,r)=>(t[r]=kn(ei(()=>{ym(n);const t=n._s.get(e);return i[r].call(t,t)})),t),{}))},t,n,r,!0),l}function jm(e,t,n={},r,s,o){let i;const a=Pm({actions:{}},n),l={deep:!0};let c,u,h,d=kn([]),f=kn([]);const p=r.state.value[e];let g;function v(t){let n;c=u=!1,"function"==typeof t?(t(r.state.value[e]),n={type:wm.patchFunction,storeId:e,events:h}):(Em(r.state.value[e],t),n={type:wm.patchObject,payload:t,storeId:e,events:h});const s=g=Symbol();Zn().then(()=>{g===s&&(c=!0)}),u=!0,Tm(d,n,r.state.value[e])}o||p||(r.state.value[e]={}),$n({});const m=o?function(){const{state:e}=n,t=e?e():{};this.$patch(e=>{Pm(e,t)})}:Sm;function y(t,n){return function(){ym(r);const s=Array.from(arguments),o=[],i=[];let a;Tm(f,{args:s,name:t,store:_,after:function(e){o.push(e)},onError:function(e){i.push(e)}});try{a=n.apply(this&&this.$id===e?this:_,s)}catch(l){throw Tm(i,l),l}return a instanceof Promise?a.then(e=>(Tm(o,e),e)).catch(e=>(Tm(i,e),Promise.reject(e))):(Tm(o,a),a)}}const _=gn({_p:r,$id:e,$onAction:km.bind(null,f),$patch:v,$reset:m,$subscribe(t,n={}){const s=km(d,t,n.detached,()=>o()),o=i.run(()=>Or(()=>r.state.value[e],r=>{("sync"===n.flush?u:c)&&t({storeId:e,type:wm.direct,events:h},r)},Pm({},l,n)));return s},$dispose:function(){i.stop(),d=[],f=[],r._s.delete(e)}});r._s.set(e,_);const b=r._e.run(()=>(i=lt(),i.run(()=>t())));for(const w in b){const t=b[w];if(An(t)&&!Am(t)||_n(t))o||(p&&Om(t)&&(An(t)?t.value=p[w]:Em(t,p[w])),r.state.value[e][w]=t);else if("function"==typeof t){const e=y(w,t);b[w]=e,a.actions[w]=t}}return Pm(_,b),Pm(Sn(_),b),Object.defineProperty(_,"$state",{get:()=>r.state.value[e],set:e=>{v(t=>{Pm(t,e)})}}),r._p.forEach(e=>{Pm(_,i.run(()=>e({store:_,app:r._a,pinia:r,options:a})))}),p&&o&&n.hydrate&&n.hydrate(_.$state,p),c=!0,u=!0,_}class Bm extends Error{constructor(e,t="FunctionsError",n){super(e),this.name=t,this.context=n}}class Im extends Bm{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class Rm extends Bm{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class Lm extends Bm{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}var Mm,Dm;(Dm=Mm||(Mm={})).Any="any",Dm.ApNortheast1="ap-northeast-1",Dm.ApNortheast2="ap-northeast-2",Dm.ApSouth1="ap-south-1",Dm.ApSoutheast1="ap-southeast-1",Dm.ApSoutheast2="ap-southeast-2",Dm.CaCentral1="ca-central-1",Dm.EuCentral1="eu-central-1",Dm.EuWest1="eu-west-1",Dm.EuWest2="eu-west-2",Dm.EuWest3="eu-west-3",Dm.SaEast1="sa-east-1",Dm.UsEast1="us-east-1",Dm.UsWest1="us-west-1",Dm.UsWest2="us-west-2";var Nm=function(e,t,n,r){return new(n||(n=Promise))(function(s,o){function i(e){try{l(r.next(e))}catch(t){o(t)}}function a(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(i,a)}l((r=r.apply(e,t||[])).next())})};class Fm{constructor(e,{headers:n={},customFetch:r,region:s=Mm.Any}={}){this.url=e,this.headers=n,this.region=s,this.fetch=(e=>{let n;return n=e||("undefined"==typeof fetch?(...e)=>t(()=>Promise.resolve().then(()=>ny),void 0).then(({default:t})=>t(...e)):fetch),(...e)=>n(...e)})(r)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var n;return Nm(this,void 0,void 0,function*(){try{const{headers:r,method:s,body:o}=t;let i={},{region:a}=t;a||(a=this.region);const l=new URL(`${this.url}/${e}`);let c;a&&"any"!==a&&(i["x-region"]=a,l.searchParams.set("forceFunctionRegion",a)),o&&(r&&!Object.prototype.hasOwnProperty.call(r,"Content-Type")||!r)&&("undefined"!=typeof Blob&&o instanceof Blob||o instanceof ArrayBuffer?(i["Content-Type"]="application/octet-stream",c=o):"string"==typeof o?(i["Content-Type"]="text/plain",c=o):"undefined"!=typeof FormData&&o instanceof FormData?c=o:(i["Content-Type"]="application/json",c=JSON.stringify(o)));const u=yield this.fetch(l.toString(),{method:s||"POST",headers:Object.assign(Object.assign(Object.assign({},i),this.headers),r),body:c}).catch(e=>{throw new Im(e)}),h=u.headers.get("x-relay-error");if(h&&"true"===h)throw new Rm(u);if(!u.ok)throw new Lm(u);let d,f=(null!==(n=u.headers.get("Content-Type"))&&void 0!==n?n:"text/plain").split(";")[0].trim();return d="application/json"===f?yield u.json():"application/octet-stream"===f?yield u.blob():"text/event-stream"===f?u:"multipart/form-data"===f?yield u.formData():yield u.text(),{data:d,error:null,response:u}}catch(r){return{data:null,error:r,response:r instanceof Lm||r instanceof Rm?r.context:void 0}}})}}var Um="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function zm(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function Hm(e){if(e.__esModule)return e;var t=e.default;if("function"==typeof t){var n=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})}),n}var Wm={},qm={},Vm={},Xm={},Km={},Ym={},Jm=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}();const Gm=Jm.fetch,Zm=Jm.fetch.bind(Jm),Qm=Jm.Headers,ey=Jm.Request,ty=Jm.Response,ny=Object.freeze(Object.defineProperty({__proto__:null,Headers:Qm,Request:ey,Response:ty,default:Zm,fetch:Gm},Symbol.toStringTag,{value:"Module"})),ry=Hm(ny);var sy={};Object.defineProperty(sy,"__esModule",{value:!0});let oy=class extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}};sy.default=oy;var iy=Um&&Um.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ym,"__esModule",{value:!0});const ay=iy(ry),ly=iy(sy);Ym.default=class{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=ay.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let n=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,n,r;let s=null,o=null,i=null,a=e.status,l=e.statusText;if(e.ok){if("HEAD"!==this.method){const t=await e.text();""===t||(o="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}const r=null===(t=this.headers.Prefer)||void 0===t?void 0:t.match(/count=(exact|planned|estimated)/),c=null===(n=e.headers.get("content-range"))||void 0===n?void 0:n.split("/");r&&c&&c.length>1&&(i=parseInt(c[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(o)&&(o.length>1?(s={code:"PGRST116",details:`Results contain ${o.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},o=null,i=null,a=406,l="Not Acceptable"):o=1===o.length?o[0]:null)}else{const t=await e.text();try{s=JSON.parse(t),Array.isArray(s)&&404===e.status&&(o=[],s=null,a=200,l="OK")}catch(c){404===e.status&&""===t?(a=204,l="No Content"):s={message:t}}if(s&&this.isMaybeSingle&&(null===(r=null==s?void 0:s.details)||void 0===r?void 0:r.includes("0 rows"))&&(s=null,a=200,l="OK"),s&&this.shouldThrowOnError)throw new ly.default(s)}return{error:s,data:o,count:i,status:a,statusText:l}});return this.shouldThrowOnError||(n=n.catch(e=>{var t,n,r;return{error:{message:`${null!==(t=null==e?void 0:e.name)&&void 0!==t?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!==(n=null==e?void 0:e.stack)&&void 0!==n?n:""}`,hint:"",code:`${null!==(r=null==e?void 0:e.code)&&void 0!==r?r:""}`},data:null,count:null,status:0,statusText:""}})),n.then(e,t)}returns(){return this}overrideTypes(){return this}};var cy=Um&&Um.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Km,"__esModule",{value:!0});const uy=cy(Ym);let hy=class extends uy.default{select(e){let t=!1;const n=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",n),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:n,foreignTable:r,referencedTable:s=r}={}){const o=s?`${s}.order`:"order",i=this.url.searchParams.get(o);return this.url.searchParams.set(o,`${i?`${i},`:""}${e}.${t?"asc":"desc"}${void 0===n?"":n?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:n=t}={}){const r=void 0===n?"limit":`${n}.limit`;return this.url.searchParams.set(r,`${e}`),this}range(e,t,{foreignTable:n,referencedTable:r=n}={}){const s=void 0===r?"offset":`${r}.offset`,o=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(s,`${e}`),this.url.searchParams.set(o,""+(t-e+1)),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:n=!1,buffers:r=!1,wal:s=!1,format:o="text"}={}){var i;const a=[e?"analyze":null,t?"verbose":null,n?"settings":null,r?"buffers":null,s?"wal":null].filter(Boolean).join("|"),l=null!==(i=this.headers.Accept)&&void 0!==i?i:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${o}; for="${l}"; options=${a};`,this}rollback(){var e;return(null!==(e=this.headers.Prefer)&&void 0!==e?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};Km.default=hy;var dy=Um&&Um.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Xm,"__esModule",{value:!0});const fy=dy(Km);let py=class extends fy.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){const n=Array.from(new Set(t)).map(e=>"string"==typeof e&&new RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${n})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:n,type:r}={}){let s="";"plain"===r?s="pl":"phrase"===r?s="ph":"websearch"===r&&(s="w");const o=void 0===n?"":`(${n})`;return this.url.searchParams.append(e,`${s}fts${o}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,n){return this.url.searchParams.append(e,`not.${t}.${n}`),this}or(e,{foreignTable:t,referencedTable:n=t}={}){const r=n?`${n}.or`:"or";return this.url.searchParams.append(r,`(${e})`),this}filter(e,t,n){return this.url.searchParams.append(e,`${t}.${n}`),this}};Xm.default=py;var gy=Um&&Um.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Vm,"__esModule",{value:!0});const vy=gy(Xm);Vm.default=class{constructor(e,{headers:t={},schema:n,fetch:r}){this.url=e,this.headers=t,this.schema=n,this.fetch=r}select(e,{head:t=!1,count:n}={}){const r=t?"HEAD":"GET";let s=!1;const o=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!s?"":('"'===e&&(s=!s),e)).join("");return this.url.searchParams.set("select",o),n&&(this.headers.Prefer=`count=${n}`),new vy.default({method:r,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:n=!0}={}){const r=[];if(this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),n||r.push("missing=default"),this.headers.Prefer=r.join(","),Array.isArray(e)){const t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){const e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new vy.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:n=!1,count:r,defaultToNull:s=!0}={}){const o=[`resolution=${n?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&o.push(this.headers.Prefer),r&&o.push(`count=${r}`),s||o.push("missing=default"),this.headers.Prefer=o.join(","),Array.isArray(e)){const t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){const e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new vy.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){const n=[];return this.headers.Prefer&&n.push(this.headers.Prefer),t&&n.push(`count=${t}`),this.headers.Prefer=n.join(","),new vy.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){const t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new vy.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};var my={},yy={};Object.defineProperty(yy,"__esModule",{value:!0}),yy.version=void 0,yy.version="0.0.0-automated",Object.defineProperty(my,"__esModule",{value:!0}),my.DEFAULT_HEADERS=void 0;const _y=yy;my.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${_y.version}`};var by=Um&&Um.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(qm,"__esModule",{value:!0});const wy=by(Vm),xy=by(Xm),Sy=my;qm.default=class e{constructor(e,{headers:t={},schema:n,fetch:r}={}){this.url=e,this.headers=Object.assign(Object.assign({},Sy.DEFAULT_HEADERS),t),this.schemaName=n,this.fetch=r}from(e){const t=new URL(`${this.url}/${e}`);return new wy.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(t){return new e(this.url,{headers:this.headers,schema:t,fetch:this.fetch})}rpc(e,t={},{head:n=!1,get:r=!1,count:s}={}){let o;const i=new URL(`${this.url}/rpc/${e}`);let a;n||r?(o=n?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{i.searchParams.append(e,t)})):(o="POST",a=t);const l=Object.assign({},this.headers);return s&&(l.Prefer=`count=${s}`),new xy.default({method:o,url:i,headers:l,schema:this.schemaName,body:a,fetch:this.fetch,allowEmpty:!1})}};var ky=Um&&Um.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Wm,"__esModule",{value:!0}),Wm.PostgrestError=Wm.PostgrestBuilder=Wm.PostgrestTransformBuilder=Wm.PostgrestFilterBuilder=Wm.PostgrestQueryBuilder=Wm.PostgrestClient=void 0;const Ty=ky(qm);Wm.PostgrestClient=Ty.default;const Ey=ky(Vm);Wm.PostgrestQueryBuilder=Ey.default;const Cy=ky(Xm);Wm.PostgrestFilterBuilder=Cy.default;const Oy=ky(Km);Wm.PostgrestTransformBuilder=Oy.default;const Py=ky(Ym);Wm.PostgrestBuilder=Py.default;const Ay=ky(sy);Wm.PostgrestError=Ay.default;var $y=Wm.default={PostgrestClient:Ty.default,PostgrestQueryBuilder:Ey.default,PostgrestFilterBuilder:Cy.default,PostgrestTransformBuilder:Oy.default,PostgrestBuilder:Py.default,PostgrestError:Ay.default};const{PostgrestClient:jy,PostgrestQueryBuilder:By,PostgrestFilterBuilder:Iy,PostgrestTransformBuilder:Ry,PostgrestBuilder:Ly,PostgrestError:My}=$y;class Dy{static detectEnvironment(){var e;if("undefined"!=typeof WebSocket)return{type:"native",constructor:WebSocket};if("undefined"!=typeof globalThis&&void 0!==globalThis.WebSocket)return{type:"native",constructor:globalThis.WebSocket};if("undefined"!=typeof global&&void 0!==global.WebSocket)return{type:"native",constructor:global.WebSocket};if("undefined"!=typeof globalThis&&void 0!==globalThis.WebSocketPair&&void 0===globalThis.WebSocket)return{type:"cloudflare",error:"Cloudflare Workers detected. WebSocket clients are not supported in Cloudflare Workers.",workaround:"Use Cloudflare Workers WebSocket API for server-side WebSocket handling, or deploy to a different runtime."};if("undefined"!=typeof globalThis&&globalThis.EdgeRuntime||"undefined"!=typeof navigator&&(null===(e=navigator.userAgent)||void 0===e?void 0:e.includes("Vercel-Edge")))return{type:"unsupported",error:"Edge runtime detected (Vercel Edge/Netlify Edge). WebSockets are not supported in edge functions.",workaround:"Use serverless functions or a different deployment target for WebSocket functionality."};if("undefined"!=typeof process&&process.versions&&process.versions.node){const e=parseInt(process.versions.node.split(".")[0]);return e>=22?void 0!==globalThis.WebSocket?{type:"native",constructor:globalThis.WebSocket}:{type:"unsupported",error:`Node.js ${e} detected but native WebSocket not found.`,workaround:"Provide a WebSocket implementation via the transport option."}:{type:"unsupported",error:`Node.js ${e} detected without native WebSocket support.`,workaround:'For Node.js < 22, install "ws" package and provide it via the transport option:\nimport ws from "ws"\nnew RealtimeClient(url, { transport: ws })'}}return{type:"unsupported",error:"Unknown JavaScript runtime without WebSocket support.",workaround:"Ensure you're running in a supported environment (browser, Node.js, Deno) or provide a custom WebSocket implementation."}}static getWebSocketConstructor(){const e=this.detectEnvironment();if(e.constructor)return e.constructor;let t=e.error||"WebSocket not supported in this environment.";throw e.workaround&&(t+=`\n\nSuggested solution: ${e.workaround}`),new Error(t)}static createWebSocket(e,t){return new(this.getWebSocketConstructor())(e,t)}static isWebSocketSupported(){try{const e=this.detectEnvironment();return"native"===e.type||"ws"===e.type}catch(e){return!1}}}const Ny=1e4;var Fy,Uy,zy,Hy,Wy,qy,Vy,Xy,Ky,Yy,Jy;(Uy=Fy||(Fy={}))[Uy.connecting=0]="connecting",Uy[Uy.open=1]="open",Uy[Uy.closing=2]="closing",Uy[Uy.closed=3]="closed",(Hy=zy||(zy={})).closed="closed",Hy.errored="errored",Hy.joined="joined",Hy.joining="joining",Hy.leaving="leaving",(qy=Wy||(Wy={})).close="phx_close",qy.error="phx_error",qy.join="phx_join",qy.reply="phx_reply",qy.leave="phx_leave",qy.access_token="access_token",(Vy||(Vy={})).websocket="websocket",(Ky=Xy||(Xy={})).Connecting="connecting",Ky.Open="open",Ky.Closing="closing",Ky.Closed="closed";class Gy{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t("string"==typeof e?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),n=new TextDecoder;return this._decodeBroadcast(e,t,n)}_decodeBroadcast(e,t,n){const r=t.getUint8(1),s=t.getUint8(2);let o=this.HEADER_LENGTH+2;const i=n.decode(e.slice(o,o+r));o+=r;const a=n.decode(e.slice(o,o+s));o+=s;return{ref:null,topic:i,event:a,payload:JSON.parse(n.decode(e.slice(o,e.byteLength)))}}}class Zy{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer),this.timer=void 0}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}(Jy=Yy||(Yy={})).abstime="abstime",Jy.bool="bool",Jy.date="date",Jy.daterange="daterange",Jy.float4="float4",Jy.float8="float8",Jy.int2="int2",Jy.int4="int4",Jy.int4range="int4range",Jy.int8="int8",Jy.int8range="int8range",Jy.json="json",Jy.jsonb="jsonb",Jy.money="money",Jy.numeric="numeric",Jy.oid="oid",Jy.reltime="reltime",Jy.text="text",Jy.time="time",Jy.timestamp="timestamp",Jy.timestamptz="timestamptz",Jy.timetz="timetz",Jy.tsrange="tsrange",Jy.tstzrange="tstzrange";const Qy=(e,t,n={})=>{var r;const s=null!==(r=n.skipTypes)&&void 0!==r?r:[];return Object.keys(t).reduce((n,r)=>(n[r]=e_(r,e,t,s),n),{})},e_=(e,t,n,r)=>{const s=t.find(t=>t.name===e),o=null==s?void 0:s.type,i=n[e];return o&&!r.includes(o)?t_(o,i):n_(i)},t_=(e,t)=>{if("_"===e.charAt(0)){const n=e.slice(1,e.length);return i_(t,n)}switch(e){case Yy.bool:return r_(t);case Yy.float4:case Yy.float8:case Yy.int2:case Yy.int4:case Yy.int8:case Yy.numeric:case Yy.oid:return s_(t);case Yy.json:case Yy.jsonb:return o_(t);case Yy.timestamp:return a_(t);case Yy.abstime:case Yy.date:case Yy.daterange:case Yy.int4range:case Yy.int8range:case Yy.money:case Yy.reltime:case Yy.text:case Yy.time:case Yy.timestamptz:case Yy.timetz:case Yy.tsrange:case Yy.tstzrange:default:return n_(t)}},n_=e=>e,r_=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},s_=e=>{if("string"==typeof e){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},o_=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(t){return console.log(`JSON parse error: ${t}`),e}return e},i_=(e,t)=>{if("string"!=typeof e)return e;const n=e.length-1,r=e[n];if("{"===e[0]&&"}"===r){let r;const o=e.slice(1,n);try{r=JSON.parse("["+o+"]")}catch(s){r=o?o.split(","):[]}return r.map(e=>t_(t,e))}return e},a_=e=>"string"==typeof e?e.replace(" ","T"):e,l_=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")+"/api/broadcast"};class c_{constructor(e,t,n={},r=1e4){this.channel=e,this.event=t,this.payload=n,this.timeout=r,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var n;return this._hasReceived(e)&&t(null===(n=this.receivedResp)||void 0===n?void 0:n.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}var u_,h_,d_,f_,p_,g_,v_,m_;(h_=u_||(u_={})).SYNC="sync",h_.JOIN="join",h_.LEAVE="leave";class y_{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.enabled=!1,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const n=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(n.state,{},e=>{const{onJoin:t,onLeave:n,onSync:r}=this.caller;this.joinRef=this.channel._joinRef(),this.state=y_.syncState(this.state,e,t,n),this.pendingDiffs.forEach(e=>{this.state=y_.syncDiff(this.state,e,t,n)}),this.pendingDiffs=[],r()}),this.channel._on(n.diff,{},e=>{const{onJoin:t,onLeave:n,onSync:r}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=y_.syncDiff(this.state,e,t,n),r())}),this.onJoin((e,t,n)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:n})}),this.onLeave((e,t,n)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:n})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,n,r){const s=this.cloneDeep(e),o=this.transformState(t),i={},a={};return this.map(s,(e,t)=>{o[e]||(a[e]=t)}),this.map(o,(e,t)=>{const n=s[e];if(n){const r=t.map(e=>e.presence_ref),s=n.map(e=>e.presence_ref),o=t.filter(e=>s.indexOf(e.presence_ref)<0),l=n.filter(e=>r.indexOf(e.presence_ref)<0);o.length>0&&(i[e]=o),l.length>0&&(a[e]=l)}else i[e]=t}),this.syncDiff(s,{joins:i,leaves:a},n,r)}static syncDiff(e,t,n,r){const{joins:s,leaves:o}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return n||(n=()=>{}),r||(r=()=>{}),this.map(s,(t,r)=>{var s;const o=null!==(s=e[t])&&void 0!==s?s:[];if(e[t]=this.cloneDeep(r),o.length>0){const n=e[t].map(e=>e.presence_ref),r=o.filter(e=>n.indexOf(e.presence_ref)<0);e[t].unshift(...r)}n(t,o,r)}),this.map(o,(t,n)=>{let s=e[t];if(!s)return;const o=n.map(e=>e.presence_ref);s=s.filter(e=>o.indexOf(e.presence_ref)<0),e[t]=s,r(t,s,n),0===s.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(n=>t(n,e[n]))}static transformState(e){return e=this.cloneDeep(e),Object.getOwnPropertyNames(e).reduce((t,n)=>{const r=e[n];return t[n]="metas"in r?r.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):r,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}(f_=d_||(d_={})).ALL="*",f_.INSERT="INSERT",f_.UPDATE="UPDATE",f_.DELETE="DELETE",(g_=p_||(p_={})).BROADCAST="broadcast",g_.PRESENCE="presence",g_.POSTGRES_CHANGES="postgres_changes",g_.SYSTEM="system",(m_=v_||(v_={})).SUBSCRIBED="SUBSCRIBED",m_.TIMED_OUT="TIMED_OUT",m_.CLOSED="CLOSED",m_.CHANNEL_ERROR="CHANNEL_ERROR";class __{constructor(e,t={config:{}},n){this.topic=e,this.params=t,this.socket=n,this.bindings={},this.state=zy.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:"",enabled:!1},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new c_(this,Wy.join,this.params,this.timeout),this.rejoinTimer=new Zy(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=zy.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=zy.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=zy.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=zy.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("error",e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=zy.errored,this.rejoinTimer.scheduleTimeout())}),this._on(Wy.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new y_(this),this.broadcastEndpointURL=l_(this.socket.endPoint),this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var n,r;if(this.socket.isConnected()||this.socket.connect(),this.state==zy.closed){const{config:{broadcast:s,presence:o,private:i}}=this.params,a=null!==(r=null===(n=this.bindings.postgres_changes)||void 0===n?void 0:n.map(e=>e.filter))&&void 0!==r?r:[],l=!!this.bindings[p_.PRESENCE]&&this.bindings[p_.PRESENCE].length>0,c={},u={broadcast:s,presence:Object.assign(Object.assign({},o),{enabled:l}),postgres_changes:a,private:i};this.socket.accessTokenValue&&(c.access_token=this.socket.accessTokenValue),this._onError(t=>null==e?void 0:e(v_.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(v_.CLOSED)),this.updateJoinPayload(Object.assign({config:u},c)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var n;if(this.socket.setAuth(),void 0!==t){const r=this.bindings.postgres_changes,s=null!==(n=null==r?void 0:r.length)&&void 0!==n?n:0,o=[];for(let n=0;n<s;n++){const s=r[n],{filter:{event:i,schema:a,table:l,filter:c}}=s,u=t&&t[n];if(!u||u.event!==i||u.schema!==a||u.table!==l||u.filter!==c)return this.unsubscribe(),this.state=zy.errored,void(null==e||e(v_.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes")));o.push(Object.assign(Object.assign({},s),{id:u.id}))}return this.bindings.postgres_changes=o,void(e&&e(v_.SUBSCRIBED))}null==e||e(v_.SUBSCRIBED)}).receive("error",t=>{this.state=zy.errored,null==e||e(v_.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(v_.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,n){return this.state===zy.joined&&e===p_.PRESENCE&&(this.socket.log("channel",`resubscribe to ${this.topic} due to change in presence callbacks on joined channel`),this.unsubscribe().then(()=>this.subscribe())),this._on(e,t,n)}async send(e,t={}){var n,r;if(this._canPush()||"broadcast"!==e.type)return new Promise(n=>{var r,s,o;const i=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null===(o=null===(s=null===(r=this.params)||void 0===r?void 0:r.config)||void 0===s?void 0:s.broadcast)||void 0===o?void 0:o.ack)||n("ok"),i.receive("ok",()=>n("ok")),i.receive("error",()=>n("error")),i.receive("timeout",()=>n("timed out"))});{const{event:o,payload:i}=e,a={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:o,payload:i,private:this.private}]})};try{const e=await this._fetchWithTimeout(this.broadcastEndpointURL,a,null!==(n=t.timeout)&&void 0!==n?n:this.timeout);return await(null===(r=e.body)||void 0===r?void 0:r.cancel()),e.ok?"ok":"error"}catch(s){return"AbortError"===s.name?"timed out":"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=zy.leaving;const t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(Wy.close,"leave",this._joinRef())};this.joinPush.destroy();let n=null;return new Promise(r=>{n=new c_(this,Wy.leave,{},e),n.receive("ok",()=>{t(),r("ok")}).receive("timeout",()=>{t(),r("timed out")}).receive("error",()=>{r("error")}),n.send(),this._canPush()||n.trigger("ok",{})}).finally(()=>{null==n||n.destroy()})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.pushBuffer=[],this.rejoinTimer.reset(),this.joinPush.destroy(),this.state=zy.closed,this.bindings={}}async _fetchWithTimeout(e,t,n){const r=new AbortController,s=setTimeout(()=>r.abort(),n),o=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:r.signal}));return clearTimeout(s),o}_push(e,t,n=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let r=new c_(this,e,t,n);return this._canPush()?r.send():this._addToPushBuffer(r),r}_addToPushBuffer(e){if(e.startTimeout(),this.pushBuffer.push(e),this.pushBuffer.length>100){const e=this.pushBuffer.shift();e&&(e.destroy(),this.socket.log("channel",`discarded push due to buffer overflow: ${e.event}`,e.payload))}}_onMessage(e,t,n){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,n){var r,s;const o=e.toLocaleLowerCase(),{close:i,error:a,leave:l,join:c}=Wy;if(n&&[i,a,l,c].indexOf(o)>=0&&n!==this._joinRef())return;let u=this._onMessage(o,t,n);if(t&&!u)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(o)?null===(r=this.bindings.postgres_changes)||void 0===r||r.filter(e=>{var t,n,r;return"*"===(null===(t=e.filter)||void 0===t?void 0:t.event)||(null===(r=null===(n=e.filter)||void 0===n?void 0:n.event)||void 0===r?void 0:r.toLocaleLowerCase())===o}).map(e=>e.callback(u,n)):null===(s=this.bindings[o])||void 0===s||s.filter(e=>{var n,r,s,i,a,l;if(["broadcast","presence","postgres_changes"].includes(o)){if("id"in e){const o=e.id,i=null===(n=e.filter)||void 0===n?void 0:n.event;return o&&(null===(r=t.ids)||void 0===r?void 0:r.includes(o))&&("*"===i||(null==i?void 0:i.toLocaleLowerCase())===(null===(s=t.data)||void 0===s?void 0:s.type.toLocaleLowerCase()))}{const n=null===(a=null===(i=null==e?void 0:e.filter)||void 0===i?void 0:i.event)||void 0===a?void 0:a.toLocaleLowerCase();return"*"===n||n===(null===(l=null==t?void 0:t.event)||void 0===l?void 0:l.toLocaleLowerCase())}}return e.type.toLocaleLowerCase()===o}).map(e=>{if("object"==typeof u&&"ids"in u){const e=u.data,{schema:t,table:n,commit_timestamp:r,type:s,errors:o}=e,i={schema:t,table:n,commit_timestamp:r,eventType:s,new:{},old:{},errors:o};u=Object.assign(Object.assign({},i),this._getPayloadRecords(e))}e.callback(u,n)})}_isClosed(){return this.state===zy.closed}_isJoined(){return this.state===zy.joined}_isJoining(){return this.state===zy.joining}_isLeaving(){return this.state===zy.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,n){const r=e.toLocaleLowerCase(),s={type:r,filter:t,callback:n};return this.bindings[r]?this.bindings[r].push(s):this.bindings[r]=[s],this}_off(e,t){const n=e.toLocaleLowerCase();return this.bindings[n]&&(this.bindings[n]=this.bindings[n].filter(e=>{var r;return!((null===(r=e.type)||void 0===r?void 0:r.toLocaleLowerCase())===n&&__.isEqual(e.filter,t))})),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(e[n]!==t[n])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(Wy.close,{},e)}_onError(e){this._on(Wy.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=zy.joining,this.joinPush.resend(e))}_getPayloadRecords(e){const t={new:{},old:{}};return"INSERT"!==e.type&&"UPDATE"!==e.type||(t.new=Qy(e.columns,e.record)),"UPDATE"!==e.type&&"DELETE"!==e.type||(t.old=Qy(e.columns,e.old_record)),t}}const b_=()=>{},w_=25e3,x_=10,S_=100,k_=[1e3,2e3,5e3,1e4];class T_{constructor(e,n){var r;if(this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=Ny,this.transport=null,this.heartbeatIntervalMs=w_,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=b_,this.ref=0,this.reconnectTimer=null,this.logger=b_,this.conn=null,this.sendBuffer=[],this.serializer=new Gy,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._connectionState="disconnected",this._wasManualDisconnect=!1,this._authPromise=null,this._resolveFetch=e=>{let n;return n=e||("undefined"==typeof fetch?(...e)=>t(()=>Promise.resolve().then(()=>ny),void 0).then(({default:t})=>t(...e)).catch(e=>{throw new Error(`Failed to load @supabase/node-fetch: ${e.message}. This is required for HTTP requests in Node.js environments without native fetch.`)}):fetch),(...e)=>n(...e)},!(null===(r=null==n?void 0:n.params)||void 0===r?void 0:r.apikey))throw new Error("API key is required to connect to Realtime");this.apiKey=n.params.apikey,this.endPoint=`${e}/${Vy.websocket}`,this.httpEndpoint=l_(e),this._initializeOptions(n),this._setupReconnectionTimer(),this.fetch=this._resolveFetch(null==n?void 0:n.fetch)}connect(){if(!(this.isConnecting()||this.isDisconnecting()||null!==this.conn&&this.isConnected())){if(this._setConnectionState("connecting"),this._setAuthSafely("connect"),this.transport)this.conn=new this.transport(this.endpointURL());else try{this.conn=Dy.createWebSocket(this.endpointURL())}catch(e){this._setConnectionState("disconnected");const t=e.message;if(t.includes("Node.js"))throw new Error(`${t}\n\nTo use Realtime in Node.js, you need to provide a WebSocket implementation:\n\nOption 1: Use Node.js 22+ which has native WebSocket support\nOption 2: Install and provide the "ws" package:\n\n  npm install ws\n\n  import ws from "ws"\n  const client = new RealtimeClient(url, {\n    ...options,\n    transport: ws\n  })`);throw new Error(`WebSocket not available: ${t}`)}this._setupConnectionHandlers()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){if(!this.isDisconnecting())if(this._setConnectionState("disconnecting",!0),this.conn){const n=setTimeout(()=>{this._setConnectionState("disconnected")},100);this.conn.onclose=()=>{clearTimeout(n),this._setConnectionState("disconnected")},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this._teardownConnection()}else this._setConnectionState("disconnected")}getChannels(){return this.channels}async removeChannel(e){const t=await e.unsubscribe();return 0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){const e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,t,n){this.logger(e,t,n)}connectionState(){switch(this.conn&&this.conn.readyState){case Fy.connecting:return Xy.Connecting;case Fy.open:return Xy.Open;case Fy.closing:return Xy.Closing;default:return Xy.Closed}}isConnected(){return this.connectionState()===Xy.Open}isConnecting(){return"connecting"===this._connectionState}isDisconnecting(){return"disconnecting"===this._connectionState}channel(e,t={config:{}}){const n=`realtime:${e}`,r=this.getChannels().find(e=>e.topic===n);if(r)return r;{const n=new __(`realtime:${e}`,t,this);return this.channels.push(n),n}}push(e){const{topic:t,event:n,payload:r,ref:s}=e,o=()=>{this.encode(e,e=>{var t;null===(t=this.conn)||void 0===t||t.send(e)})};this.log("push",`${t} ${n} (${s})`,r),this.isConnected()?o():this.sendBuffer.push(o)}async setAuth(e=null){this._authPromise=this._performAuth(e);try{await this._authPromise}finally{this._authPromise=null}}async sendHeartbeat(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef)return this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),this._wasManualDisconnect=!1,null===(e=this.conn)||void 0===e||e.close(1e3,"heartbeat timeout"),void setTimeout(()=>{var e;this.isConnected()||null===(e=this.reconnectTimer)||void 0===e||e.scheduleTimeout()},S_);this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),this._setAuthSafely("heartbeat")}else this.heartbeatCallback("disconnected")}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}_onConnMessage(e){this.decode(e.data,e=>{"phoenix"===e.topic&&"phx_reply"===e.event&&this.heartbeatCallback("ok"===e.payload.status?"ok":"error"),e.ref&&e.ref===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null);const{topic:t,event:n,payload:r,ref:s}=e,o=s?`(${s})`:"",i=r.status||"";this.log("receive",`${i} ${t} ${n} ${o}`.trim(),r),this.channels.filter(e=>e._isMember(t)).forEach(e=>e._trigger(n,r,s)),this._triggerStateCallbacks("message",e)})}_clearTimer(e){var t;"heartbeat"===e&&this.heartbeatTimer?(clearInterval(this.heartbeatTimer),this.heartbeatTimer=void 0):"reconnect"===e&&(null===(t=this.reconnectTimer)||void 0===t||t.reset())}_clearAllTimers(){this._clearTimer("heartbeat"),this._clearTimer("reconnect")}_setupConnectionHandlers(){this.conn&&("binaryType"in this.conn&&(this.conn.binaryType="arraybuffer"),this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_teardownConnection(){this.conn&&(this.conn.onopen=null,this.conn.onerror=null,this.conn.onmessage=null,this.conn.onclose=null,this.conn=null),this._clearAllTimers(),this.channels.forEach(e=>e.teardown())}_onConnOpen(){this._setConnectionState("connected"),this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this._clearTimer("reconnect"),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this._triggerStateCallbacks("open")}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(e){var t;this._setConnectionState("disconnected"),this.log("transport","close",e),this._triggerChanError(),this._clearTimer("heartbeat"),this._wasManualDisconnect||null===(t=this.reconnectTimer)||void 0===t||t.scheduleTimeout(),this._triggerStateCallbacks("close",e)}_onConnError(e){this._setConnectionState("disconnected"),this.log("transport",`${e}`),this._triggerChanError(),this._triggerStateCallbacks("error",e)}_triggerChanError(){this.channels.forEach(e=>e._trigger(Wy.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;const n=e.match(/\?/)?"&":"?";return`${e}${n}${new URLSearchParams(t)}`}_workerObjectUrl(e){let t;if(e)t=e;else{const e=new Blob(['\n  addEventListener("message", (e) => {\n    if (e.data.event === "start") {\n      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);\n    }\n  });'],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}_setConnectionState(e,t=!1){this._connectionState=e,"connecting"===e?this._wasManualDisconnect=!1:"disconnecting"===e&&(this._wasManualDisconnect=t)}async _performAuth(e=null){let t;t=e||(this.accessToken?await this.accessToken():this.accessTokenValue),this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach(e=>{const n={access_token:t,version:"realtime-js/2.15.1"};t&&e.updateJoinPayload(n),e.joinedOnce&&e._isJoined()&&e._push(Wy.access_token,{access_token:t})}))}async _waitForAuthIfNeeded(){this._authPromise&&await this._authPromise}_setAuthSafely(e="general"){this.setAuth().catch(t=>{this.log("error",`error setting auth in ${e}`,t)})}_triggerStateCallbacks(e,t){try{this.stateChangeCallbacks[e].forEach(n=>{try{n(t)}catch(r){this.log("error",`error in ${e} callback`,r)}})}catch(n){this.log("error",`error triggering ${e} callbacks`,n)}}_setupReconnectionTimer(){this.reconnectTimer=new Zy(async()=>{setTimeout(async()=>{await this._waitForAuthIfNeeded(),this.isConnected()||this.connect()},x_)},this.reconnectAfterMs)}_initializeOptions(e){var t,n,r,s,o,i,a,l;if(this.transport=null!==(t=null==e?void 0:e.transport)&&void 0!==t?t:null,this.timeout=null!==(n=null==e?void 0:e.timeout)&&void 0!==n?n:Ny,this.heartbeatIntervalMs=null!==(r=null==e?void 0:e.heartbeatIntervalMs)&&void 0!==r?r:w_,this.worker=null!==(s=null==e?void 0:e.worker)&&void 0!==s&&s,this.accessToken=null!==(o=null==e?void 0:e.accessToken)&&void 0!==o?o:null,(null==e?void 0:e.params)&&(this.params=e.params),(null==e?void 0:e.logger)&&(this.logger=e.logger),((null==e?void 0:e.logLevel)||(null==e?void 0:e.log_level))&&(this.logLevel=e.logLevel||e.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),this.reconnectAfterMs=null!==(i=null==e?void 0:e.reconnectAfterMs)&&void 0!==i?i:e=>k_[e-1]||1e4,this.encode=null!==(a=null==e?void 0:e.encode)&&void 0!==a?a:(e,t)=>t(JSON.stringify(e)),this.decode=null!==(l=null==e?void 0:e.decode)&&void 0!==l?l:this.serializer.decode.bind(this.serializer),this.worker){if("undefined"!=typeof window&&!window.Worker)throw new Error("Web Worker is not supported");this.workerUrl=null==e?void 0:e.workerUrl}}}class E_ extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function C_(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class O_ extends E_{constructor(e,t,n){super(e),this.name="StorageApiError",this.status=t,this.statusCode=n}toJSON(){return{name:this.name,message:this.message,status:this.status,statusCode:this.statusCode}}}class P_ extends E_{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}var A_=function(e,t,n,r){return new(n||(n=Promise))(function(s,o){function i(e){try{l(r.next(e))}catch(t){o(t)}}function a(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(i,a)}l((r=r.apply(e,t||[])).next())})};const $_=e=>{let n;return n=e||("undefined"==typeof fetch?(...e)=>t(()=>Promise.resolve().then(()=>ny),void 0).then(({default:t})=>t(...e)):fetch),(...e)=>n(...e)},j_=e=>{if(Array.isArray(e))return e.map(e=>j_(e));if("function"==typeof e||e!==Object(e))return e;const t={};return Object.entries(e).forEach(([e,n])=>{const r=e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""));t[r]=j_(n)}),t};var B_=function(e,t,n,r){return new(n||(n=Promise))(function(s,o){function i(e){try{l(r.next(e))}catch(t){o(t)}}function a(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(i,a)}l((r=r.apply(e,t||[])).next())})};const I_=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),R_=(e,n,r)=>B_(void 0,void 0,void 0,function*(){const s=yield A_(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield t(()=>Promise.resolve().then(()=>ny),void 0)).Response:Response});e instanceof s&&!(null==r?void 0:r.noResolveJson)?e.json().then(t=>{const r=e.status||500,s=(null==t?void 0:t.statusCode)||r+"";n(new O_(I_(t),r,s))}).catch(e=>{n(new P_(I_(e),e))}):n(new P_(I_(e),e))}),L_=(e,t,n,r)=>{const s={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"!==e&&r?((e=>{if("object"!=typeof e||null===e)return!1;const t=Object.getPrototypeOf(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||Symbol.toStringTag in e||Symbol.iterator in e)})(r)?(s.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),s.body=JSON.stringify(r)):s.body=r,(null==t?void 0:t.duplex)&&(s.duplex=t.duplex),Object.assign(Object.assign({},s),n)):s};function M_(e,t,n,r,s,o){return B_(this,void 0,void 0,function*(){return new Promise((i,a)=>{e(n,L_(t,r,s,o)).then(e=>{if(!e.ok)throw e;return(null==r?void 0:r.noResolveJson)?e:e.json()}).then(e=>i(e)).catch(e=>R_(e,a,r))})})}function D_(e,t,n,r){return B_(this,void 0,void 0,function*(){return M_(e,"GET",t,n,r)})}function N_(e,t,n,r,s){return B_(this,void 0,void 0,function*(){return M_(e,"POST",t,r,s,n)})}function F_(e,t,n,r,s){return B_(this,void 0,void 0,function*(){return M_(e,"PUT",t,r,s,n)})}function U_(e,t,n,r,s){return B_(this,void 0,void 0,function*(){return M_(e,"DELETE",t,r,s,n)})}var z_=function(e,t,n,r){return new(n||(n=Promise))(function(s,o){function i(e){try{l(r.next(e))}catch(t){o(t)}}function a(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(i,a)}l((r=r.apply(e,t||[])).next())})};const H_={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},W_={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class q_{constructor(e,t={},n,r){this.url=e,this.headers=t,this.bucketId=n,this.fetch=$_(r)}uploadOrUpdate(e,t,n,r){return z_(this,void 0,void 0,function*(){try{let s;const o=Object.assign(Object.assign({},W_),r);let i=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(o.upsert)});const a=o.metadata;"undefined"!=typeof Blob&&n instanceof Blob?(s=new FormData,s.append("cacheControl",o.cacheControl),a&&s.append("metadata",this.encodeMetadata(a)),s.append("",n)):"undefined"!=typeof FormData&&n instanceof FormData?(s=n,s.append("cacheControl",o.cacheControl),a&&s.append("metadata",this.encodeMetadata(a))):(s=n,i["cache-control"]=`max-age=${o.cacheControl}`,i["content-type"]=o.contentType,a&&(i["x-metadata"]=this.toBase64(this.encodeMetadata(a)))),(null==r?void 0:r.headers)&&(i=Object.assign(Object.assign({},i),r.headers));const l=this._removeEmptyFolders(t),c=this._getFinalPath(l),u=yield("PUT"==e?F_:N_)(this.fetch,`${this.url}/object/${c}`,s,Object.assign({headers:i},(null==o?void 0:o.duplex)?{duplex:o.duplex}:{}));return{data:{path:l,id:u.Id,fullPath:u.Key},error:null}}catch(s){if(C_(s))return{data:null,error:s};throw s}})}upload(e,t,n){return z_(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,n)})}uploadToSignedUrl(e,t,n,r){return z_(this,void 0,void 0,function*(){const s=this._removeEmptyFolders(e),o=this._getFinalPath(s),i=new URL(this.url+`/object/upload/sign/${o}`);i.searchParams.set("token",t);try{let e;const t=Object.assign({upsert:W_.upsert},r),o=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&n instanceof Blob?(e=new FormData,e.append("cacheControl",t.cacheControl),e.append("",n)):"undefined"!=typeof FormData&&n instanceof FormData?(e=n,e.append("cacheControl",t.cacheControl)):(e=n,o["cache-control"]=`max-age=${t.cacheControl}`,o["content-type"]=t.contentType);return{data:{path:s,fullPath:(yield F_(this.fetch,i.toString(),e,{headers:o})).Key},error:null}}catch(a){if(C_(a))return{data:null,error:a};throw a}})}createSignedUploadUrl(e,t){return z_(this,void 0,void 0,function*(){try{let n=this._getFinalPath(e);const r=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(r["x-upsert"]="true");const s=yield N_(this.fetch,`${this.url}/object/upload/sign/${n}`,{},{headers:r}),o=new URL(this.url+s.url),i=o.searchParams.get("token");if(!i)throw new E_("No token returned by API");return{data:{signedUrl:o.toString(),path:e,token:i},error:null}}catch(n){if(C_(n))return{data:null,error:n};throw n}})}update(e,t,n){return z_(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,n)})}move(e,t,n){return z_(this,void 0,void 0,function*(){try{return{data:yield N_(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==n?void 0:n.destinationBucket},{headers:this.headers}),error:null}}catch(r){if(C_(r))return{data:null,error:r};throw r}})}copy(e,t,n){return z_(this,void 0,void 0,function*(){try{return{data:{path:(yield N_(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==n?void 0:n.destinationBucket},{headers:this.headers})).Key},error:null}}catch(r){if(C_(r))return{data:null,error:r};throw r}})}createSignedUrl(e,t,n){return z_(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),s=yield N_(this.fetch,`${this.url}/object/sign/${r}`,Object.assign({expiresIn:t},(null==n?void 0:n.transform)?{transform:n.transform}:{}),{headers:this.headers});const o=(null==n?void 0:n.download)?`&download=${!0===n.download?"":n.download}`:"";return s={signedUrl:encodeURI(`${this.url}${s.signedURL}${o}`)},{data:s,error:null}}catch(r){if(C_(r))return{data:null,error:r};throw r}})}createSignedUrls(e,t,n){return z_(this,void 0,void 0,function*(){try{const r=yield N_(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),s=(null==n?void 0:n.download)?`&download=${!0===n.download?"":n.download}`:"";return{data:r.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${s}`):null})),error:null}}catch(r){if(C_(r))return{data:null,error:r};throw r}})}download(e,t){return z_(this,void 0,void 0,function*(){const n=void 0!==(null==t?void 0:t.transform)?"render/image/authenticated":"object",r=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),s=r?`?${r}`:"";try{const t=this._getFinalPath(e),r=yield D_(this.fetch,`${this.url}/${n}/${t}${s}`,{headers:this.headers,noResolveJson:!0});return{data:yield r.blob(),error:null}}catch(o){if(C_(o))return{data:null,error:o};throw o}})}info(e){return z_(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{const e=yield D_(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:j_(e),error:null}}catch(n){if(C_(n))return{data:null,error:n};throw n}})}exists(e){return z_(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{return yield function(e,t,n,r){return B_(this,void 0,void 0,function*(){return M_(e,"HEAD",t,Object.assign(Object.assign({},n),{noResolveJson:!0}),r)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(n){if(C_(n)&&n instanceof P_){const e=n.originalError;if([400,404].includes(null==e?void 0:e.status))return{data:!1,error:n}}throw n}})}getPublicUrl(e,t){const n=this._getFinalPath(e),r=[],s=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==s&&r.push(s);const o=void 0!==(null==t?void 0:t.transform)?"render/image":"object",i=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==i&&r.push(i);let a=r.join("&");return""!==a&&(a=`?${a}`),{data:{publicUrl:encodeURI(`${this.url}/${o}/public/${n}${a}`)}}}remove(e){return z_(this,void 0,void 0,function*(){try{return{data:yield U_(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(t){if(C_(t))return{data:null,error:t};throw t}})}list(e,t,n){return z_(this,void 0,void 0,function*(){try{const r=Object.assign(Object.assign(Object.assign({},H_),t),{prefix:e||""});return{data:yield N_(this.fetch,`${this.url}/object/list/${this.bucketId}`,r,{headers:this.headers},n),error:null}}catch(r){if(C_(r))return{data:null,error:r};throw r}})}listV2(e,t){return z_(this,void 0,void 0,function*(){try{const n=Object.assign({},e);return{data:yield N_(this.fetch,`${this.url}/object/list-v2/${this.bucketId}`,n,{headers:this.headers},t),error:null}}catch(n){if(C_(n))return{data:null,error:n};throw n}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return"undefined"!=typeof Buffer?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e.replace(/^\/+/,"")}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}const V_={"X-Client-Info":"storage-js/2.11.0"};var X_=function(e,t,n,r){return new(n||(n=Promise))(function(s,o){function i(e){try{l(r.next(e))}catch(t){o(t)}}function a(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(i,a)}l((r=r.apply(e,t||[])).next())})};class K_{constructor(e,t={},n,r){const s=new URL(e);if(null==r?void 0:r.useNewHostname){/supabase\.(co|in|red)$/.test(s.hostname)&&!s.hostname.includes("storage.supabase.")&&(s.hostname=s.hostname.replace("supabase.","storage.supabase."))}this.url=s.href,this.headers=Object.assign(Object.assign({},V_),t),this.fetch=$_(n)}listBuckets(){return X_(this,void 0,void 0,function*(){try{return{data:yield D_(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(C_(e))return{data:null,error:e};throw e}})}getBucket(e){return X_(this,void 0,void 0,function*(){try{return{data:yield D_(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(t){if(C_(t))return{data:null,error:t};throw t}})}createBucket(e,t={public:!1}){return X_(this,void 0,void 0,function*(){try{return{data:yield N_(this.fetch,`${this.url}/bucket`,{id:e,name:e,type:t.type,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(n){if(C_(n))return{data:null,error:n};throw n}})}updateBucket(e,t){return X_(this,void 0,void 0,function*(){try{return{data:yield F_(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(n){if(C_(n))return{data:null,error:n};throw n}})}emptyBucket(e){return X_(this,void 0,void 0,function*(){try{return{data:yield N_(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(t){if(C_(t))return{data:null,error:t};throw t}})}deleteBucket(e){return X_(this,void 0,void 0,function*(){try{return{data:yield U_(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(t){if(C_(t))return{data:null,error:t};throw t}})}}class Y_ extends K_{constructor(e,t={},n,r){super(e,t,n,r)}from(e){return new q_(this.url,this.headers,e,this.fetch)}}let J_="";J_="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";const G_={headers:{"X-Client-Info":`supabase-js-${J_}/2.55.0`}},Z_={schema:"public"},Q_={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},eb={};var tb=function(e,t,n,r){return new(n||(n=Promise))(function(s,o){function i(e){try{l(r.next(e))}catch(t){o(t)}}function a(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(i,a)}l((r=r.apply(e,t||[])).next())})};const nb=(e,t,n)=>{const r=(e=>{let t;return t=e||("undefined"==typeof fetch?Zm:fetch),(...e)=>t(...e)})(n),s="undefined"==typeof Headers?Qm:Headers;return(n,o)=>tb(void 0,void 0,void 0,function*(){var i;const a=null!==(i=yield t())&&void 0!==i?i:e;let l=new s(null==o?void 0:o.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${a}`),r(n,Object.assign(Object.assign({},o),{headers:l}))})};var rb=function(e,t,n,r){return new(n||(n=Promise))(function(s,o){function i(e){try{l(r.next(e))}catch(t){o(t)}}function a(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(i,a)}l((r=r.apply(e,t||[])).next())})};const sb="2.71.1",ob=3e4,ib=9e4,ab={"X-Client-Info":`gotrue-js/${sb}`},lb="X-Supabase-Api-Version",cb={timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"},ub=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class hb extends Error{constructor(e,t,n){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=n}}function db(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class fb extends hb{constructor(e,t,n){super(e,t,n),this.name="AuthApiError",this.status=t,this.code=n}}class pb extends hb{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class gb extends hb{constructor(e,t,n,r){super(e,n,r),this.name=t,this.status=n}}class vb extends gb{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class mb extends gb{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class yb extends gb{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class _b extends gb{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class bb extends gb{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class wb extends gb{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function xb(e){return db(e)&&"AuthRetryableFetchError"===e.name}class Sb extends gb{constructor(e,t,n){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=n}}class kb extends gb{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}const Tb="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),Eb=" \t\n\r=".split(""),Cb=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<Eb.length;t+=1)e[Eb[t].charCodeAt(0)]=-2;for(let t=0;t<Tb.length;t+=1)e[Tb[t].charCodeAt(0)]=t;return e})();function Ob(e,t,n){if(null!==e)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;){const e=t.queue>>t.queuedBits-6&63;n(Tb[e]),t.queuedBits-=6}else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;){const e=t.queue>>t.queuedBits-6&63;n(Tb[e]),t.queuedBits-=6}}function Pb(e,t,n){const r=Cb[e];if(!(r>-1)){if(-2===r)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}for(t.queue=t.queue<<6|r,t.queuedBits+=6;t.queuedBits>=8;)n(t.queue>>t.queuedBits-8&255),t.queuedBits-=8}function Ab(e){const t=[],n=e=>{t.push(String.fromCodePoint(e))},r={utf8seq:0,codepoint:0},s={queue:0,queuedBits:0},o=e=>{!function(e,t,n){if(0===t.utf8seq){if(e<=127)return void n(e);for(let n=1;n<6;n+=1)if(!(e>>7-n&1)){t.utf8seq=n;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else{if(4!==t.utf8seq)throw new Error("Invalid UTF-8 sequence");t.codepoint=7&e}t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&n(t.codepoint)}}(e,r,n)};for(let i=0;i<e.length;i+=1)Pb(e.charCodeAt(i),s,o);return t.join("")}function $b(e,t){if(!(e<=127)){if(e<=2047)return t(192|e>>6),void t(128|63&e);if(e<=65535)return t(224|e>>12),t(128|e>>6&63),void t(128|63&e);if(e<=1114111)return t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),void t(128|63&e);throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}t(e)}function jb(e){const t=[],n={queue:0,queuedBits:0},r=e=>{t.push(e)};for(let s=0;s<e.length;s+=1)Pb(e.charCodeAt(s),n,r);return new Uint8Array(t)}function Bb(e){const t=[];return function(e,t){for(let n=0;n<e.length;n+=1){let r=e.charCodeAt(n);if(r>55295&&r<=56319){const t=1024*(r-55296)&65535;r=65536+(e.charCodeAt(n+1)-56320&65535|t),n+=1}$b(r,t)}}(e,e=>t.push(e)),new Uint8Array(t)}function Ib(e){const t=[],n={queue:0,queuedBits:0},r=e=>{t.push(e)};return e.forEach(e=>Ob(e,n,r)),Ob(null,n,r),t.join("")}const Rb=()=>"undefined"!=typeof window&&"undefined"!=typeof document,Lb={tested:!1,writable:!1},Mb=()=>{if(!Rb())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(t){return!1}if(Lb.tested)return Lb.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),Lb.tested=!0,Lb.writable=!0}catch(t){Lb.tested=!0,Lb.writable=!1}return Lb.writable};const Db=e=>{let n;return n=e||("undefined"==typeof fetch?(...e)=>t(()=>Promise.resolve().then(()=>ny),void 0).then(({default:t})=>t(...e)):fetch),(...e)=>n(...e)},Nb=async(e,t,n)=>{await e.setItem(t,JSON.stringify(n))},Fb=async(e,t)=>{const n=await e.getItem(t);if(!n)return null;try{return JSON.parse(n)}catch(r){return n}},Ub=async(e,t)=>{await e.removeItem(t)};class zb{constructor(){this.promise=new zb.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function Hb(e){const t=e.split(".");if(3!==t.length)throw new kb("Invalid JWT structure");for(let n=0;n<t.length;n++)if(!ub.test(t[n]))throw new kb("JWT not in base64url format");return{header:JSON.parse(Ab(t[0])),payload:JSON.parse(Ab(t[1])),signature:jb(t[2]),raw:{header:t[0],payload:t[1]}}}function Wb(e){return("0"+e.toString(16)).substr(-2)}async function qb(e){if(!("undefined"!=typeof crypto&&void 0!==crypto.subtle&&"undefined"!=typeof TextEncoder))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const t=await async function(e){const t=(new TextEncoder).encode(e),n=await crypto.subtle.digest("SHA-256",t),r=new Uint8Array(n);return Array.from(r).map(e=>String.fromCharCode(e)).join("")}(e);return btoa(t).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function Vb(e,t,n=!1){const r=function(){const e=new Uint32Array(56);if("undefined"==typeof crypto){const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length;let n="";for(let r=0;r<56;r++)n+=e.charAt(Math.floor(Math.random()*t));return n}return crypto.getRandomValues(e),Array.from(e,Wb).join("")}();let s=r;n&&(s+="/PASSWORD_RECOVERY"),await Nb(e,`${t}-code-verifier`,s);const o=await qb(r);return[o,r===o?"plain":"s256"]}zb.promiseConstructor=Promise;const Xb=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;const Kb=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function Yb(e){if(!Kb.test(e))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}function Jb(){return new Proxy({},{get:(e,t)=>{if("__isUserNotAvailableProxy"===t)return!0;if("symbol"==typeof t){const e=t.toString();if("Symbol(Symbol.toPrimitive)"===e||"Symbol(Symbol.toStringTag)"===e||"Symbol(util.inspect.custom)"===e)return}throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Accessing the "${t}" property of the session object is not supported. Please use getUser() instead.`)},set:(e,t)=>{throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Setting the "${t}" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`)},deleteProperty:(e,t)=>{throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Deleting the "${t}" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`)}})}function Gb(e){return JSON.parse(JSON.stringify(e))}const Zb=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Qb=[502,503,504];async function ew(e){var t,n;if(!("object"==typeof(n=e)&&null!==n&&"status"in n&&"ok"in n&&"json"in n&&"function"==typeof n.json))throw new wb(Zb(e),0);if(Qb.includes(e.status))throw new wb(Zb(e),e.status);let r,s;try{r=await e.json()}catch(i){throw new pb(Zb(i),i)}const o=function(e){const t=e.headers.get(lb);if(!t)return null;if(!t.match(Xb))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(i){return null}}(e);if(o&&o.getTime()>=cb.timestamp&&"object"==typeof r&&r&&"string"==typeof r.code?s=r.code:"object"==typeof r&&r&&"string"==typeof r.error_code&&(s=r.error_code),s){if("weak_password"===s)throw new Sb(Zb(r),e.status,(null===(t=r.weak_password)||void 0===t?void 0:t.reasons)||[]);if("session_not_found"===s)throw new vb}else if("object"==typeof r&&r&&"object"==typeof r.weak_password&&r.weak_password&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new Sb(Zb(r),e.status,r.weak_password.reasons);throw new fb(Zb(r),e.status||500,s)}async function tw(e,t,n,r){var s;const o=Object.assign({},null==r?void 0:r.headers);o[lb]||(o[lb]=cb.name),(null==r?void 0:r.jwt)&&(o.Authorization=`Bearer ${r.jwt}`);const i=null!==(s=null==r?void 0:r.query)&&void 0!==s?s:{};(null==r?void 0:r.redirectTo)&&(i.redirect_to=r.redirectTo);const a=Object.keys(i).length?"?"+new URLSearchParams(i).toString():"",l=await async function(e,t,n,r,s,o){const i=((e,t,n,r)=>{const s={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?s:(s.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),s.body=JSON.stringify(r),Object.assign(Object.assign({},s),n))})(t,r,s,o);let a;try{a=await e(n,Object.assign({},i))}catch(l){throw console.error(l),new wb(Zb(l),0)}a.ok||await ew(a);if(null==r?void 0:r.noResolveJson)return a;try{return await a.json()}catch(l){await ew(l)}}(e,t,n+a,{headers:o,noResolveJson:null==r?void 0:r.noResolveJson},{},null==r?void 0:r.body);return(null==r?void 0:r.xform)?null==r?void 0:r.xform(l):{data:Object.assign({},l),error:null}}function nw(e){var t;let n=null;var r;(function(e){return e.access_token&&e.refresh_token&&e.expires_in})(e)&&(n=Object.assign({},e),e.expires_at||(n.expires_at=(r=e.expires_in,Math.round(Date.now()/1e3)+r)));return{data:{session:n,user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function rw(e){const t=nw(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function sw(e){var t;return{data:{user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function ow(e){return{data:e,error:null}}function iw(e){const{action_link:t,email_otp:n,hashed_token:r,redirect_to:s,verification_type:o}=e,i=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var s=0;for(r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(n[r[s]]=e[r[s]])}return n}(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]);return{data:{properties:{action_link:t,email_otp:n,hashed_token:r,redirect_to:s,verification_type:o},user:Object.assign({},i)},error:null}}function aw(e){return e}const lw=["global","local","others"];class cw{constructor({url:e="",headers:t={},fetch:n}){this.url=e,this.headers=t,this.fetch=Db(n),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t=lw[0]){if(lw.indexOf(t)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${lw.join(", ")}`);try{return await tw(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(n){if(db(n))return{data:null,error:n};throw n}}async inviteUserByEmail(e,t={}){try{return await tw(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:sw})}catch(n){if(db(n))return{data:{user:null},error:n};throw n}}async generateLink(e){try{const{options:t}=e,n=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var s=0;for(r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(n[r[s]]=e[r[s]])}return n}(e,["options"]),r=Object.assign(Object.assign({},n),t);return"newEmail"in n&&(r.new_email=null==n?void 0:n.newEmail,delete r.newEmail),await tw(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:r,headers:this.headers,xform:iw,redirectTo:null==t?void 0:t.redirectTo})}catch(t){if(db(t))return{data:{properties:null,user:null},error:t};throw t}}async createUser(e){try{return await tw(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:sw})}catch(t){if(db(t))return{data:{user:null},error:t};throw t}}async listUsers(e){var t,n,r,s,o,i,a;try{const l={nextPage:null,lastPage:0,total:0},c=await tw(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!==(n=null===(t=null==e?void 0:e.page)||void 0===t?void 0:t.toString())&&void 0!==n?n:"",per_page:null!==(s=null===(r=null==e?void 0:e.perPage)||void 0===r?void 0:r.toString())&&void 0!==s?s:""},xform:aw});if(c.error)throw c.error;const u=await c.json(),h=null!==(o=c.headers.get("x-total-count"))&&void 0!==o?o:0,d=null!==(a=null===(i=c.headers.get("link"))||void 0===i?void 0:i.split(","))&&void 0!==a?a:[];return d.length>0&&(d.forEach(e=>{const t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),n=JSON.parse(e.split(";")[1].split("=")[1]);l[`${n}Page`]=t}),l.total=parseInt(h)),{data:Object.assign(Object.assign({},u),l),error:null}}catch(l){if(db(l))return{data:{users:[]},error:l};throw l}}async getUserById(e){Yb(e);try{return await tw(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:sw})}catch(t){if(db(t))return{data:{user:null},error:t};throw t}}async updateUserById(e,t){Yb(e);try{return await tw(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:sw})}catch(n){if(db(n))return{data:{user:null},error:n};throw n}}async deleteUser(e,t=!1){Yb(e);try{return await tw(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:sw})}catch(n){if(db(n))return{data:{user:null},error:n};throw n}}async _listFactors(e){Yb(e.userId);try{const{data:t,error:n}=await tw(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:n}}catch(t){if(db(t))return{data:null,error:t};throw t}}async _deleteFactor(e){Yb(e.userId),Yb(e.id);try{return{data:await tw(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(t){if(db(t))return{data:null,error:t};throw t}}}function uw(e={}){return{getItem:t=>e[t]||null,setItem:(t,n)=>{e[t]=n},removeItem:t=>{delete e[t]}}}const hw=!!(globalThis&&Mb()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"));class dw extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class fw extends dw{}async function pw(e,t,n){hw&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const r=new globalThis.AbortController;return t>0&&setTimeout(()=>{r.abort(),hw&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:r.signal},async r=>{if(!r){if(0===t)throw hw&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new fw(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(hw)try{const e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(s){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",s)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await n()}hw&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,r.name);try{return await n()}finally{hw&&console.log("@supabase/gotrue-js: navigatorLock: released",e,r.name)}}))}!function(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}}();const gw={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:ab,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function vw(e,t,n){return await n()}const mw={};class yw{constructor(e){var t,n;this.userStorage=null,this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=yw.nextInstanceID,yw.nextInstanceID+=1,this.instanceID>0&&Rb()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const r=Object.assign(Object.assign({},gw),e);if(this.logDebugMessages=!!r.debug,"function"==typeof r.debug&&(this.logger=r.debug),this.persistSession=r.persistSession,this.storageKey=r.storageKey,this.autoRefreshToken=r.autoRefreshToken,this.admin=new cw({url:r.url,headers:r.headers,fetch:r.fetch}),this.url=r.url,this.headers=r.headers,this.fetch=Db(r.fetch),this.lock=r.lock||vw,this.detectSessionInUrl=r.detectSessionInUrl,this.flowType=r.flowType,this.hasCustomAuthorizationHeader=r.hasCustomAuthorizationHeader,r.lock?this.lock=r.lock:Rb()&&(null===(t=null===globalThis||void 0===globalThis?void 0:globalThis.navigator)||void 0===t?void 0:t.locks)?this.lock=pw:this.lock=vw,this.jwks||(this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER),this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?(r.storage?this.storage=r.storage:Mb()?this.storage=globalThis.localStorage:(this.memoryStorage={},this.storage=uw(this.memoryStorage)),r.userStorage&&(this.userStorage=r.userStorage)):(this.memoryStorage={},this.storage=uw(this.memoryStorage)),Rb()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(s){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",s)}null===(n=this.broadcastChannel)||void 0===n||n.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}get jwks(){var e,t;return null!==(t=null===(e=mw[this.storageKey])||void 0===e?void 0:e.jwks)&&void 0!==t?t:{keys:[]}}set jwks(e){mw[this.storageKey]=Object.assign(Object.assign({},mw[this.storageKey]),{jwks:e})}get jwks_cached_at(){var e,t;return null!==(t=null===(e=mw[this.storageKey])||void 0===e?void 0:e.cachedAt)&&void 0!==t?t:Number.MIN_SAFE_INTEGER}set jwks_cached_at(e){mw[this.storageKey]=Object.assign(Object.assign({},mw[this.storageKey]),{cachedAt:e})}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${sb}) ${(new Date).toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{const t=function(e){const t={},n=new URL(e);if(n.hash&&"#"===n.hash[0])try{new URLSearchParams(n.hash.substring(1)).forEach((e,n)=>{t[n]=e})}catch(r){}return n.searchParams.forEach((e,n)=>{t[n]=e}),t}(window.location.href);let n="none";if(this._isImplicitGrantCallback(t)?n="implicit":await this._isPKCECallback(t)&&(n="pkce"),Rb()&&this.detectSessionInUrl&&"none"!==n){const{data:r,error:s}=await this._getSessionFromURL(t,n);if(s){if(this._debug("#_initialize()","error detecting session from URL",s),function(e){return db(e)&&"AuthImplicitGrantRedirectError"===e.name}(s)){const t=null===(e=s.details)||void 0===e?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:s}}return await this._removeSession(),{error:s}}const{session:o,redirectType:i}=r;return this._debug("#_initialize()","detected session in URL",o,"redirect type",i),await this._saveSession(o),setTimeout(async()=>{"recovery"===i?await this._notifyAllSubscribers("PASSWORD_RECOVERY",o):await this._notifyAllSubscribers("SIGNED_IN",o)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(t){return db(t)?{error:t}:{error:new pb("Unexpected error during initialization",t)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,n,r;try{const s=await tw(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!==(n=null===(t=null==e?void 0:e.options)||void 0===t?void 0:t.data)&&void 0!==n?n:{},gotrue_meta_security:{captcha_token:null===(r=null==e?void 0:e.options)||void 0===r?void 0:r.captchaToken}},xform:nw}),{data:o,error:i}=s;if(i||!o)return{data:{user:null,session:null},error:i};const a=o.session,l=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:l,session:a},error:null}}catch(s){if(db(s))return{data:{user:null,session:null},error:s};throw s}}async signUp(e){var t,n,r;try{let s;if("email"in e){const{email:n,password:r,options:o}=e;let i=null,a=null;"pkce"===this.flowType&&([i,a]=await Vb(this.storage,this.storageKey)),s=await tw(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==o?void 0:o.emailRedirectTo,body:{email:n,password:r,data:null!==(t=null==o?void 0:o.data)&&void 0!==t?t:{},gotrue_meta_security:{captcha_token:null==o?void 0:o.captchaToken},code_challenge:i,code_challenge_method:a},xform:nw})}else{if(!("phone"in e))throw new yb("You must provide either an email or phone number and a password");{const{phone:t,password:o,options:i}=e;s=await tw(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:o,data:null!==(n=null==i?void 0:i.data)&&void 0!==n?n:{},channel:null!==(r=null==i?void 0:i.channel)&&void 0!==r?r:"sms",gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},xform:nw})}}const{data:o,error:i}=s;if(i||!o)return{data:{user:null,session:null},error:i};const a=o.session,l=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:l,session:a},error:null}}catch(s){if(db(s))return{data:{user:null,session:null},error:s};throw s}}async signInWithPassword(e){try{let t;if("email"in e){const{email:n,password:r,options:s}=e;t=await tw(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:n,password:r,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}},xform:rw})}else{if(!("phone"in e))throw new yb("You must provide either an email or phone number and a password");{const{phone:n,password:r,options:s}=e;t=await tw(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:n,password:r,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}},xform:rw})}}const{data:n,error:r}=t;return r?{data:{user:null,session:null},error:r}:n&&n.session&&n.user?(n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",n.session)),{data:Object.assign({user:n.user,session:n.session},n.weak_password?{weakPassword:n.weak_password}:null),error:r}):{data:{user:null,session:null},error:new mb}}catch(t){if(db(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOAuth(e){var t,n,r,s;return await this._handleProviderSignIn(e.provider,{redirectTo:null===(t=e.options)||void 0===t?void 0:t.redirectTo,scopes:null===(n=e.options)||void 0===n?void 0:n.scopes,queryParams:null===(r=e.options)||void 0===r?void 0:r.queryParams,skipBrowserRedirect:null===(s=e.options)||void 0===s?void 0:s.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async signInWithWeb3(e){const{chain:t}=e;if("solana"===t)return await this.signInWithSolana(e);throw new Error(`@supabase/auth-js: Unsupported chain "${t}"`)}async signInWithSolana(e){var t,n,r,s,o,i,a,l,c,u,h,d;let f,p;if("message"in e)f=e.message,p=e.signature;else{const{chain:h,wallet:d,statement:g,options:v}=e;let m;if(Rb())if("object"==typeof d)m=d;else{const e=window;if(!("solana"in e)||"object"!=typeof e.solana||!("signIn"in e.solana&&"function"==typeof e.solana.signIn||"signMessage"in e.solana&&"function"==typeof e.solana.signMessage))throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.");m=e.solana}else{if("object"!=typeof d||!(null==v?void 0:v.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");m=d}const y=new URL(null!==(t=null==v?void 0:v.url)&&void 0!==t?t:window.location.href);if("signIn"in m&&m.signIn){const e=await m.signIn(Object.assign(Object.assign(Object.assign({issuedAt:(new Date).toISOString()},null==v?void 0:v.signInWithSolana),{version:"1",domain:y.host,uri:y.href}),g?{statement:g}:null));let t;if(Array.isArray(e)&&e[0]&&"object"==typeof e[0])t=e[0];else{if(!(e&&"object"==typeof e&&"signedMessage"in e&&"signature"in e))throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");t=e}if(!("signedMessage"in t&&"signature"in t&&("string"==typeof t.signedMessage||t.signedMessage instanceof Uint8Array)&&t.signature instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields");f="string"==typeof t.signedMessage?t.signedMessage:(new TextDecoder).decode(t.signedMessage),p=t.signature}else{if(!("signMessage"in m&&"function"==typeof m.signMessage&&"publicKey"in m&&"object"==typeof m&&m.publicKey&&"toBase58"in m.publicKey&&"function"==typeof m.publicKey.toBase58))throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");f=[`${y.host} wants you to sign in with your Solana account:`,m.publicKey.toBase58(),...g?["",g,""]:[""],"Version: 1",`URI: ${y.href}`,`Issued At: ${null!==(r=null===(n=null==v?void 0:v.signInWithSolana)||void 0===n?void 0:n.issuedAt)&&void 0!==r?r:(new Date).toISOString()}`,...(null===(s=null==v?void 0:v.signInWithSolana)||void 0===s?void 0:s.notBefore)?[`Not Before: ${v.signInWithSolana.notBefore}`]:[],...(null===(o=null==v?void 0:v.signInWithSolana)||void 0===o?void 0:o.expirationTime)?[`Expiration Time: ${v.signInWithSolana.expirationTime}`]:[],...(null===(i=null==v?void 0:v.signInWithSolana)||void 0===i?void 0:i.chainId)?[`Chain ID: ${v.signInWithSolana.chainId}`]:[],...(null===(a=null==v?void 0:v.signInWithSolana)||void 0===a?void 0:a.nonce)?[`Nonce: ${v.signInWithSolana.nonce}`]:[],...(null===(l=null==v?void 0:v.signInWithSolana)||void 0===l?void 0:l.requestId)?[`Request ID: ${v.signInWithSolana.requestId}`]:[],...(null===(u=null===(c=null==v?void 0:v.signInWithSolana)||void 0===c?void 0:c.resources)||void 0===u?void 0:u.length)?["Resources",...v.signInWithSolana.resources.map(e=>`- ${e}`)]:[]].join("\n");const e=await m.signMessage((new TextEncoder).encode(f),"utf8");if(!(e&&e instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");p=e}}try{const{data:t,error:n}=await tw(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:f,signature:Ib(p)},(null===(h=e.options)||void 0===h?void 0:h.captchaToken)?{gotrue_meta_security:{captcha_token:null===(d=e.options)||void 0===d?void 0:d.captchaToken}}:null),xform:nw});if(n)throw n;return t&&t.session&&t.user?(t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign({},t),error:n}):{data:{user:null,session:null},error:new mb}}catch(g){if(db(g))return{data:{user:null,session:null},error:g};throw g}}async _exchangeCodeForSession(e){const t=await Fb(this.storage,`${this.storageKey}-code-verifier`),[n,r]=(null!=t?t:"").split("/");try{const{data:t,error:s}=await tw(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:n},xform:nw});if(await Ub(this.storage,`${this.storageKey}-code-verifier`),s)throw s;return t&&t.session&&t.user?(t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=r?r:null}),error:s}):{data:{user:null,session:null,redirectType:null},error:new mb}}catch(s){if(db(s))return{data:{user:null,session:null,redirectType:null},error:s};throw s}}async signInWithIdToken(e){try{const{options:t,provider:n,token:r,access_token:s,nonce:o}=e,i=await tw(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:n,id_token:r,access_token:s,nonce:o,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:nw}),{data:a,error:l}=i;return l?{data:{user:null,session:null},error:l}:a&&a.session&&a.user?(a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",a.session)),{data:a,error:l}):{data:{user:null,session:null},error:new mb}}catch(t){if(db(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOtp(e){var t,n,r,s,o;try{if("email"in e){const{email:r,options:s}=e;let o=null,i=null;"pkce"===this.flowType&&([o,i]=await Vb(this.storage,this.storageKey));const{error:a}=await tw(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:r,data:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:{},create_user:null===(n=null==s?void 0:s.shouldCreateUser)||void 0===n||n,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken},code_challenge:o,code_challenge_method:i},redirectTo:null==s?void 0:s.emailRedirectTo});return{data:{user:null,session:null},error:a}}if("phone"in e){const{phone:t,options:n}=e,{data:i,error:a}=await tw(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!==(r=null==n?void 0:n.data)&&void 0!==r?r:{},create_user:null===(s=null==n?void 0:n.shouldCreateUser)||void 0===s||s,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken},channel:null!==(o=null==n?void 0:n.channel)&&void 0!==o?o:"sms"}});return{data:{user:null,session:null,messageId:null==i?void 0:i.message_id},error:a}}throw new yb("You must provide either an email or phone number.")}catch(i){if(db(i))return{data:{user:null,session:null},error:i};throw i}}async verifyOtp(e){var t,n;try{let r,s;"options"in e&&(r=null===(t=e.options)||void 0===t?void 0:t.redirectTo,s=null===(n=e.options)||void 0===n?void 0:n.captchaToken);const{data:o,error:i}=await tw(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:s}}),redirectTo:r,xform:nw});if(i)throw i;if(!o)throw new Error("An error occurred on token verification.");const a=o.session,l=o.user;return(null==a?void 0:a.access_token)&&(await this._saveSession(a),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",a)),{data:{user:l,session:a},error:null}}catch(r){if(db(r))return{data:{user:null,session:null},error:r};throw r}}async signInWithSSO(e){var t,n,r;try{let s=null,o=null;return"pkce"===this.flowType&&([s,o]=await Vb(this.storage,this.storageKey)),await tw(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!==(n=null===(t=e.options)||void 0===t?void 0:t.redirectTo)&&void 0!==n?n:void 0}),(null===(r=null==e?void 0:e.options)||void 0===r?void 0:r.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:s,code_challenge_method:o}),headers:this.headers,xform:ow})}catch(s){if(db(s))return{data:null,error:s};throw s}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{const{data:{session:t},error:n}=e;if(n)throw n;if(!t)throw new vb;const{error:r}=await tw(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:r}})}catch(e){if(db(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{const t=`${this.url}/resend`;if("email"in e){const{email:n,type:r,options:s}=e,{error:o}=await tw(this.fetch,"POST",t,{headers:this.headers,body:{email:n,type:r,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}},redirectTo:null==s?void 0:s.emailRedirectTo});return{data:{user:null,session:null},error:o}}if("phone"in e){const{phone:n,type:r,options:s}=e,{data:o,error:i}=await tw(this.fetch,"POST",t,{headers:this.headers,body:{phone:n,type:r,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}}});return{data:{user:null,session:null,messageId:null==o?void 0:o.message_id},error:i}}throw new yb("You must provide either an email or phone number and a type")}catch(t){if(db(t))return{data:{user:null,session:null},error:t};throw t}}async getSession(){await this.initializePromise;return await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){const e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),n=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await n}catch(e){}})()),n}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(t){}})()),await e;this.pendingInLock.length;){const e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{const t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",(new Error).stack);try{let e=null;const t=await Fb(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};const n=!!e.expires_at&&1e3*e.expires_at-Date.now()<ib;if(this._debug("#__loadSession()",`session has${n?"":" not"} expired`,"expires_at",e.expires_at),!n){if(this.userStorage){const t=await Fb(this.userStorage,this.storageKey+"-user");(null==t?void 0:t.user)?e.user=t.user:e.user=Jb()}if(this.storage.isServer&&e.user){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,n,r)=>(t||"user"!==n||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,n,r))})}return{data:{session:e},error:null}}const{session:r,error:s}=await this._callRefreshToken(e.refresh_token);return s?{data:{session:null},error:s}:{data:{session:r},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){if(e)return await this._getUser(e);await this.initializePromise;return await this._acquireLock(-1,async()=>await this._getUser())}async _getUser(e){try{return e?await tw(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:sw}):await this._useSession(async e=>{var t,n,r;const{data:s,error:o}=e;if(o)throw o;return(null===(t=s.session)||void 0===t?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await tw(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!==(r=null===(n=s.session)||void 0===n?void 0:n.access_token)&&void 0!==r?r:void 0,xform:sw}):{data:{user:null},error:new vb}})}catch(t){if(db(t))return function(e){return db(e)&&"AuthSessionMissingError"===e.name}(t)&&(await this._removeSession(),await Ub(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:t};throw t}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async n=>{const{data:r,error:s}=n;if(s)throw s;if(!r.session)throw new vb;const o=r.session;let i=null,a=null;"pkce"===this.flowType&&null!=e.email&&([i,a]=await Vb(this.storage,this.storageKey));const{data:l,error:c}=await tw(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:i,code_challenge_method:a}),jwt:o.access_token,xform:sw});if(c)throw c;return o.user=l.user,await this._saveSession(o),await this._notifyAllSubscribers("USER_UPDATED",o),{data:{user:o.user},error:null}})}catch(n){if(db(n))return{data:{user:null},error:n};throw n}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new vb;const t=Date.now()/1e3;let n=t,r=!0,s=null;const{payload:o}=Hb(e.access_token);if(o.exp&&(n=o.exp,r=n<=t),r){const{session:t,error:n}=await this._callRefreshToken(e.refresh_token);if(n)return{data:{user:null,session:null},error:n};if(!t)return{data:{user:null,session:null},error:null};s=t}else{const{data:r,error:o}=await this._getUser(e.access_token);if(o)throw o;s={access_token:e.access_token,refresh_token:e.refresh_token,user:r.user,token_type:"bearer",expires_in:n-t,expires_at:n},await this._saveSession(s),await this._notifyAllSubscribers("SIGNED_IN",s)}return{data:{user:s.user,session:s},error:null}}catch(t){if(db(t))return{data:{session:null,user:null},error:t};throw t}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var n;if(!e){const{data:r,error:s}=t;if(s)throw s;e=null!==(n=r.session)&&void 0!==n?n:void 0}if(!(null==e?void 0:e.refresh_token))throw new vb;const{session:r,error:s}=await this._callRefreshToken(e.refresh_token);return s?{data:{user:null,session:null},error:s}:r?{data:{user:r.user,session:r},error:null}:{data:{user:null,session:null},error:null}})}catch(t){if(db(t))return{data:{user:null,session:null},error:t};throw t}}async _getSessionFromURL(e,t){try{if(!Rb())throw new _b("No browser detected.");if(e.error||e.error_description||e.error_code)throw new _b(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new bb("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new _b("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new bb("No code detected.");const{data:t,error:n}=await this._exchangeCodeForSession(e.code);if(n)throw n;const r=new URL(window.location.href);return r.searchParams.delete("code"),window.history.replaceState(window.history.state,"",r.toString()),{data:{session:t.session,redirectType:null},error:null}}const{provider_token:n,provider_refresh_token:r,access_token:s,refresh_token:o,expires_in:i,expires_at:a,token_type:l}=e;if(!(s&&i&&o&&l))throw new _b("No session defined in URL");const c=Math.round(Date.now()/1e3),u=parseInt(i);let h=c+u;a&&(h=parseInt(a));const d=h-c;1e3*d<=ob&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${d}s, should have been closer to ${u}s`);const f=h-u;c-f>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",f,h,c):c-f<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",f,h,c);const{data:p,error:g}=await this._getUser(s);if(g)throw g;const v={provider_token:n,provider_refresh_token:r,access_token:s,expires_in:u,expires_at:h,refresh_token:o,token_type:l,user:p.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:v,redirectType:e.type},error:null}}catch(n){if(db(n))return{data:{session:null,redirectType:null},error:n};throw n}}_isImplicitGrantCallback(e){return Boolean(e.access_token||e.error_description)}async _isPKCECallback(e){const t=await Fb(this.storage,`${this.storageKey}-code-verifier`);return!(!e.code||!t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var n;const{data:r,error:s}=t;if(s)return{error:s};const o=null===(n=r.session)||void 0===n?void 0:n.access_token;if(o){const{error:t}=await this.admin.signOut(o,e);if(t&&(!function(e){return db(e)&&"AuthApiError"===e.name}(t)||404!==t.status&&401!==t.status&&403!==t.status))return{error:t}}return"others"!==e&&(await this._removeSession(),await Ub(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){const t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),n={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,n),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:n}}}async _emitInitialSession(e){return await this._useSession(async t=>{var n,r;try{const{data:{session:r},error:s}=t;if(s)throw s;await(null===(n=this.stateChangeEmitters.get(e))||void 0===n?void 0:n.callback("INITIAL_SESSION",r)),this._debug("INITIAL_SESSION","callback id",e,"session",r)}catch(s){await(null===(r=this.stateChangeEmitters.get(e))||void 0===r?void 0:r.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",s),console.error(s)}})}async resetPasswordForEmail(e,t={}){let n=null,r=null;"pkce"===this.flowType&&([n,r]=await Vb(this.storage,this.storageKey,!0));try{return await tw(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:n,code_challenge_method:r,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(s){if(db(s))return{data:null,error:s};throw s}}async getUserIdentities(){var e;try{const{data:t,error:n}=await this.getUser();if(n)throw n;return{data:{identities:null!==(e=t.user.identities)&&void 0!==e?e:[]},error:null}}catch(t){if(db(t))return{data:null,error:t};throw t}}async linkIdentity(e){var t;try{const{data:n,error:r}=await this._useSession(async t=>{var n,r,s,o,i;const{data:a,error:l}=t;if(l)throw l;const c=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null===(n=e.options)||void 0===n?void 0:n.redirectTo,scopes:null===(r=e.options)||void 0===r?void 0:r.scopes,queryParams:null===(s=e.options)||void 0===s?void 0:s.queryParams,skipBrowserRedirect:!0});return await tw(this.fetch,"GET",c,{headers:this.headers,jwt:null!==(i=null===(o=a.session)||void 0===o?void 0:o.access_token)&&void 0!==i?i:void 0})});if(r)throw r;return Rb()&&!(null===(t=e.options)||void 0===t?void 0:t.skipBrowserRedirect)&&window.location.assign(null==n?void 0:n.url),{data:{provider:e.provider,url:null==n?void 0:n.url},error:null}}catch(n){if(db(n))return{data:{provider:e.provider,url:null},error:n};throw n}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var n,r;const{data:s,error:o}=t;if(o)throw o;return await tw(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!==(r=null===(n=s.session)||void 0===n?void 0:n.access_token)&&void 0!==r?r:void 0})})}catch(t){if(db(t))return{data:null,error:t};throw t}}async _refreshAccessToken(e){const t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{const s=Date.now();return await(n=async n=>(n>0&&await async function(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}(200*Math.pow(2,n-1)),this._debug(t,"refreshing attempt",n),await tw(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:nw})),r=(e,t)=>{const n=200*Math.pow(2,e);return t&&xb(t)&&Date.now()+n-s<ob},new Promise((e,t)=>{(async()=>{for(let o=0;o<1/0;o++)try{const t=await n(o);if(!r(o,null,t))return void e(t)}catch(s){if(!r(o,s))return void t(s)}})()}))}catch(s){if(this._debug(t,"error",s),db(s))return{data:{session:null,user:null},error:s};throw s}finally{this._debug(t,"end")}var n,r}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){const n=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",n),Rb()&&!t.skipBrowserRedirect&&window.location.assign(n),{data:{provider:e,url:n},error:null}}async _recoverAndRefresh(){var e,t;const n="#_recoverAndRefresh()";this._debug(n,"begin");try{const s=await Fb(this.storage,this.storageKey);if(s&&this.userStorage){let t=await Fb(this.userStorage,this.storageKey+"-user");this.storage.isServer||!Object.is(this.storage,this.userStorage)||t||(t={user:s.user},await Nb(this.userStorage,this.storageKey+"-user",t)),s.user=null!==(e=null==t?void 0:t.user)&&void 0!==e?e:Jb()}else if(s&&!s.user&&!s.user){const e=await Fb(this.storage,this.storageKey+"-user");e&&(null==e?void 0:e.user)?(s.user=e.user,await Ub(this.storage,this.storageKey+"-user"),await Nb(this.storage,this.storageKey,s)):s.user=Jb()}if(this._debug(n,"session from storage",s),!this._isValidSession(s))return this._debug(n,"session is not valid"),void(null!==s&&await this._removeSession());const o=1e3*(null!==(t=s.expires_at)&&void 0!==t?t:1/0)-Date.now()<ib;if(this._debug(n,`session has${o?"":" not"} expired with margin of 90000s`),o){if(this.autoRefreshToken&&s.refresh_token){const{error:e}=await this._callRefreshToken(s.refresh_token);e&&(console.error(e),xb(e)||(this._debug(n,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else if(s.user&&!0===s.user.__isUserNotAvailableProxy)try{const{data:e,error:t}=await this._getUser(s.access_token);!t&&(null==e?void 0:e.user)?(s.user=e.user,await this._saveSession(s),await this._notifyAllSubscribers("SIGNED_IN",s)):this._debug(n,"could not get user data, skipping SIGNED_IN notification")}catch(r){console.error("Error getting user data:",r),this._debug(n,"error getting user data, skipping SIGNED_IN notification",r)}else await this._notifyAllSubscribers("SIGNED_IN",s)}catch(s){return this._debug(n,"error",s),void console.error(s)}finally{this._debug(n,"end")}}async _callRefreshToken(e){var t,n;if(!e)throw new vb;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const r=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(r,"begin");try{this.refreshingDeferred=new zb;const{data:t,error:n}=await this._refreshAccessToken(e);if(n)throw n;if(!t.session)throw new vb;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);const r={session:t.session,error:null};return this.refreshingDeferred.resolve(r),r}catch(s){if(this._debug(r,"error",s),db(s)){const e={session:null,error:s};return xb(s)||await this._removeSession(),null===(t=this.refreshingDeferred)||void 0===t||t.resolve(e),e}throw null===(n=this.refreshingDeferred)||void 0===n||n.reject(s),s}finally{this.refreshingDeferred=null,this._debug(r,"end")}}async _notifyAllSubscribers(e,t,n=!0){const r=`#_notifyAllSubscribers(${e})`;this._debug(r,"begin",t,`broadcast = ${n}`);try{this.broadcastChannel&&n&&this.broadcastChannel.postMessage({event:e,session:t});const r=[],s=Array.from(this.stateChangeEmitters.values()).map(async n=>{try{await n.callback(e,t)}catch(s){r.push(s)}});if(await Promise.all(s),r.length>0){for(let e=0;e<r.length;e+=1)console.error(r[e]);throw r[0]}}finally{this._debug(r,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0;const t=Object.assign({},e),n=t.user&&!0===t.user.__isUserNotAvailableProxy;if(this.userStorage){!n&&t.user&&await Nb(this.userStorage,this.storageKey+"-user",{user:t.user});const e=Object.assign({},t);delete e.user;const r=Gb(e);await Nb(this.storage,this.storageKey,r)}else{const e=Gb(t);await Nb(this.storage,this.storageKey,e)}}async _removeSession(){this._debug("#_removeSession()"),await Ub(this.storage,this.storageKey),await Ub(this.storage,this.storageKey+"-code-verifier"),await Ub(this.storage,this.storageKey+"-user"),this.userStorage&&await Ub(this.userStorage,this.storageKey+"-user"),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&Rb()&&(null===window||void 0===window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(t){console.error("removing visibilitychange callback failed",t)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const e=setInterval(()=>this._autoRefreshTokenTick(),ob);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const t=Date.now();try{return await this._useSession(async e=>{const{data:{session:n}}=e;if(!n||!n.refresh_token||!n.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");const r=Math.floor((1e3*n.expires_at-t)/ob);this._debug("#_autoRefreshTokenTick()",`access token expires in ${r} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),r<=3&&await this._callRefreshToken(n.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(!(e.isAcquireTimeout||e instanceof dw))throw e;this._debug("auto refresh token tick lock not available")}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!Rb()||!(null===window||void 0===window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null===window||void 0===window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){const t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{"visible"===document.visibilityState?await this._recoverAndRefresh():this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting")}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,n){const r=[`provider=${encodeURIComponent(t)}`];if((null==n?void 0:n.redirectTo)&&r.push(`redirect_to=${encodeURIComponent(n.redirectTo)}`),(null==n?void 0:n.scopes)&&r.push(`scopes=${encodeURIComponent(n.scopes)}`),"pkce"===this.flowType){const[e,t]=await Vb(this.storage,this.storageKey),n=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});r.push(n.toString())}if(null==n?void 0:n.queryParams){const e=new URLSearchParams(n.queryParams);r.push(e.toString())}return(null==n?void 0:n.skipBrowserRedirect)&&r.push(`skip_http_redirect=${n.skipBrowserRedirect}`),`${e}?${r.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var n;const{data:r,error:s}=t;return s?{data:null,error:s}:await tw(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null===(n=null==r?void 0:r.session)||void 0===n?void 0:n.access_token})})}catch(t){if(db(t))return{data:null,error:t};throw t}}async _enroll(e){try{return await this._useSession(async t=>{var n,r;const{data:s,error:o}=t;if(o)return{data:null,error:o};const i=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:a,error:l}=await tw(this.fetch,"POST",`${this.url}/factors`,{body:i,headers:this.headers,jwt:null===(n=null==s?void 0:s.session)||void 0===n?void 0:n.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null===(r=null==a?void 0:a.totp)||void 0===r?void 0:r.qr_code)&&(a.totp.qr_code=`data:image/svg+xml;utf-8,${a.totp.qr_code}`),{data:a,error:null})})}catch(t){if(db(t))return{data:null,error:t};throw t}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var n;const{data:r,error:s}=t;if(s)return{data:null,error:s};const{data:o,error:i}=await tw(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null===(n=null==r?void 0:r.session)||void 0===n?void 0:n.access_token});return i?{data:null,error:i}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+o.expires_in},o)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",o),{data:o,error:i})})}catch(t){if(db(t))return{data:null,error:t};throw t}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var n;const{data:r,error:s}=t;return s?{data:null,error:s}:await tw(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null===(n=null==r?void 0:r.session)||void 0===n?void 0:n.access_token})})}catch(t){if(db(t))return{data:null,error:t};throw t}})}async _challengeAndVerify(e){const{data:t,error:n}=await this._challenge({factorId:e.factorId});return n?{data:null,error:n}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){const{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};const n=(null==e?void 0:e.factors)||[],r=n.filter(e=>"totp"===e.factor_type&&"verified"===e.status),s=n.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:n,totp:r,phone:s},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,n;const{data:{session:r},error:s}=e;if(s)return{data:null,error:s};if(!r)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:o}=Hb(r.access_token);let i=null;o.aal&&(i=o.aal);let a=i;(null!==(n=null===(t=r.user.factors)||void 0===t?void 0:t.filter(e=>"verified"===e.status))&&void 0!==n?n:[]).length>0&&(a="aal2");return{data:{currentLevel:i,nextLevel:a,currentAuthenticationMethods:o.amr||[]},error:null}}))}async fetchJwk(e,t={keys:[]}){let n=t.keys.find(t=>t.kid===e);if(n)return n;const r=Date.now();if(n=this.jwks.keys.find(t=>t.kid===e),n&&this.jwks_cached_at+6e5>r)return n;const{data:s,error:o}=await tw(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(o)throw o;return s.keys&&0!==s.keys.length?(this.jwks=s,this.jwks_cached_at=r,n=s.keys.find(t=>t.kid===e),n||null):null}async getClaims(e,t={}){try{let n=e;if(!n){const{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};n=e.session.access_token}const{header:r,payload:s,signature:o,raw:{header:i,payload:a}}=Hb(n);(null==t?void 0:t.allowExpired)||function(e){if(!e)throw new Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw new Error("JWT has expired")}(s.exp);const l=r.alg&&!r.alg.startsWith("HS")&&r.kid&&"crypto"in globalThis&&"subtle"in globalThis.crypto?await this.fetchJwk(r.kid,(null==t?void 0:t.keys)?{keys:t.keys}:null==t?void 0:t.jwks):null;if(!l){const{error:e}=await this.getUser(n);if(e)throw e;return{data:{claims:s,header:r,signature:o},error:null}}const c=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}(r.alg),u=await crypto.subtle.importKey("jwk",l,c,!0,["verify"]);if(!(await crypto.subtle.verify(c,u,o,Bb(`${i}.${a}`))))throw new kb("Invalid JWT signature");return{data:{claims:s,header:r,signature:o},error:null}}catch(n){if(db(n))return{data:null,error:n};throw n}}}yw.nextInstanceID=0;const _w=yw;class bw extends _w{constructor(e){super(e)}}var ww=function(e,t,n,r){return new(n||(n=Promise))(function(s,o){function i(e){try{l(r.next(e))}catch(t){o(t)}}function a(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(i,a)}l((r=r.apply(e,t||[])).next())})};class xw{constructor(e,t,n){var r,s,o;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const i=(a=e).endsWith("/")?a:a+"/";var a;const l=new URL(i);this.realtimeUrl=new URL("realtime/v1",l),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",l),this.storageUrl=new URL("storage/v1",l),this.functionsUrl=new URL("functions/v1",l);const c=`sb-${l.hostname.split(".")[0]}-auth-token`,u=function(e,t){var n,r;const{db:s,auth:o,realtime:i,global:a}=e,{db:l,auth:c,realtime:u,global:h}=t,d={db:Object.assign(Object.assign({},l),s),auth:Object.assign(Object.assign({},c),o),realtime:Object.assign(Object.assign({},u),i),storage:{},global:Object.assign(Object.assign(Object.assign({},h),a),{headers:Object.assign(Object.assign({},null!==(n=null==h?void 0:h.headers)&&void 0!==n?n:{}),null!==(r=null==a?void 0:a.headers)&&void 0!==r?r:{})}),accessToken:()=>rb(this,void 0,void 0,function*(){return""})};return e.accessToken?d.accessToken=e.accessToken:delete d.accessToken,d}(null!=n?n:{},{db:Z_,realtime:eb,auth:Object.assign(Object.assign({},Q_),{storageKey:c}),global:G_});this.storageKey=null!==(r=u.auth.storageKey)&&void 0!==r?r:"",this.headers=null!==(s=u.global.headers)&&void 0!==s?s:{},u.accessToken?(this.accessToken=u.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!==(o=u.auth)&&void 0!==o?o:{},this.headers,u.global.fetch),this.fetch=nb(t,this._getAccessToken.bind(this),u.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},u.realtime)),this.rest=new jy(new URL("rest/v1",l).href,{headers:this.headers,schema:u.db.schema,fetch:this.fetch}),this.storage=new Y_(this.storageUrl.href,this.headers,this.fetch,null==n?void 0:n.storage),u.accessToken||this._listenForAuthEvents()}get functions(){return new Fm(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},n={}){return this.rest.rpc(e,t,n)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return ww(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:n}=yield this.auth.getSession();return null!==(t=null===(e=n.session)||void 0===e?void 0:e.access_token)&&void 0!==t?t:this.supabaseKey})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:n,storage:r,storageKey:s,flowType:o,lock:i,debug:a},l,c){const u={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new bw({url:this.authUrl.href,headers:Object.assign(Object.assign({},u),l),storageKey:s,autoRefreshToken:e,persistSession:t,detectSessionInUrl:n,storage:r,flowType:o,lock:i,debug:a,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new T_(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,n){"TOKEN_REFRESHED"!==e&&"SIGNED_IN"!==e||this.changedAccessToken===n?"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0):this.changedAccessToken=n}}const Sw=(e,t,n)=>new xw(e,t,n);(function(){if("undefined"!=typeof window)return!1;if("undefined"==typeof process)return!1;const e=process.version;if(null==e)return!1;const t=e.match(/^v(\d+)\./);return!!t&&parseInt(t[1],10)<=18})()&&console.warn("⚠️  Node.js 18 and below are deprecated and will no longer be supported in future versions of @supabase/supabase-js. Please upgrade to Node.js 20 or later. For more information, visit: https://github.com/orgs/supabase/discussions/37217");const kw="https://gcrsoruzrxompokkckii.supabase.co",Tw="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdjcnNvcnV6cnhvbXBva2tja2lpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDE5MzgzMDcsImV4cCI6MjA1NzUxNDMwN30.YZNPNofM03rbi7Gay-iE95CRzb9llrehuLt0O4JU9MI";let Ew;function Cw(){return Ew||(Ew=Sw(kw,Tw,{global:{headers:{Authorization:`Bearer ${Tw}`}},auth:{persistSession:!1,autoRefreshToken:!1,detectSessionInUrl:!1}})),Ew}function Ow(e){return e?Sw(kw,Tw,{global:{headers:{"x-auth-token":e,Authorization:`Bearer ${Tw}`}},auth:{persistSession:!1,autoRefreshToken:!1,detectSessionInUrl:!1}}):Cw()}var Pw={exports:{}};var Aw={exports:{}};const $w=Hm(Object.freeze(Object.defineProperty({__proto__:null,default:{}},Symbol.toStringTag,{value:"Module"})));var jw;function Bw(){return jw||(jw=1,Aw.exports=(e=e||function(e,t){var n;if("undefined"!=typeof window&&window.crypto&&(n=window.crypto),"undefined"!=typeof self&&self.crypto&&(n=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(n=globalThis.crypto),!n&&"undefined"!=typeof window&&window.msCrypto&&(n=window.msCrypto),!n&&void 0!==Um&&Um.crypto&&(n=Um.crypto),!n)try{n=$w}catch(g){}var r=function(){if(n){if("function"==typeof n.getRandomValues)try{return n.getRandomValues(new Uint32Array(1))[0]}catch(g){}if("function"==typeof n.randomBytes)try{return n.randomBytes(4).readInt32LE()}catch(g){}}throw new Error("Native crypto module could not be used to get secure random number.")},s=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),o={},i=o.lib={},a=i.Base=function(){return{extend:function(e){var t=s(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),l=i.WordArray=a.extend({init:function(e,n){e=this.words=e||[],this.sigBytes=n!=t?n:4*e.length},toString:function(e){return(e||u).stringify(this)},concat:function(e){var t=this.words,n=e.words,r=this.sigBytes,s=e.sigBytes;if(this.clamp(),r%4)for(var o=0;o<s;o++){var i=n[o>>>2]>>>24-o%4*8&255;t[r+o>>>2]|=i<<24-(r+o)%4*8}else for(var a=0;a<s;a+=4)t[r+a>>>2]=n[a>>>2];return this.sigBytes+=s,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=a.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],n=0;n<e;n+=4)t.push(r());return new l.init(t,e)}}),c=o.enc={},u=c.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],s=0;s<n;s++){var o=t[s>>>2]>>>24-s%4*8&255;r.push((o>>>4).toString(16)),r.push((15&o).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new l.init(n,t/2)}},h=c.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],s=0;s<n;s++){var o=t[s>>>2]>>>24-s%4*8&255;r.push(String.fromCharCode(o))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new l.init(n,t)}},d=c.Utf8={stringify:function(e){try{return decodeURIComponent(escape(h.stringify(e)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(e){return h.parse(unescape(encodeURIComponent(e)))}},f=i.BufferedBlockAlgorithm=a.extend({reset:function(){this._data=new l.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=d.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n,r=this._data,s=r.words,o=r.sigBytes,i=this.blockSize,a=o/(4*i),c=(a=t?e.ceil(a):e.max((0|a)-this._minBufferSize,0))*i,u=e.min(4*c,o);if(c){for(var h=0;h<c;h+=i)this._doProcessBlock(s,h);n=s.splice(0,c),r.sigBytes-=u}return new l.init(n,u)},clone:function(){var e=a.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});i.Hasher=f.extend({cfg:a.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){f.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new p.HMAC.init(e,n).finalize(t)}}});var p=o.algo={};return o}(Math),e)),Aw.exports;var e}var Iw,Rw={exports:{}};function Lw(){return Iw?Rw.exports:(Iw=1,Rw.exports=(i=Bw(),n=(t=i).lib,r=n.Base,s=n.WordArray,(o=t.x64={}).Word=r.extend({init:function(e,t){this.high=e,this.low=t}}),o.WordArray=r.extend({init:function(t,n){t=this.words=t||[],this.sigBytes=n!=e?n:8*t.length},toX32:function(){for(var e=this.words,t=e.length,n=[],r=0;r<t;r++){var o=e[r];n.push(o.high),n.push(o.low)}return s.create(n,this.sigBytes)},clone:function(){for(var e=r.clone.call(this),t=e.words=this.words.slice(0),n=t.length,s=0;s<n;s++)t[s]=t[s].clone();return e}}),i));var e,t,n,r,s,o,i}var Mw,Dw={exports:{}};function Nw(){return Mw||(Mw=1,Dw.exports=(e=Bw(),function(){if("function"==typeof ArrayBuffer){var t=e.lib.WordArray,n=t.init,r=t.init=function(e){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){for(var t=e.byteLength,r=[],s=0;s<t;s++)r[s>>>2]|=e[s]<<24-s%4*8;n.call(this,r,t)}else n.apply(this,arguments)};r.prototype=t}}(),e.lib.WordArray)),Dw.exports;var e}var Fw,Uw={exports:{}};function zw(){return Fw?Uw.exports:(Fw=1,Uw.exports=(e=Bw(),function(){var t=e,n=t.lib.WordArray,r=t.enc;function s(e){return e<<8&4278255360|e>>>8&16711935}r.Utf16=r.Utf16BE={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],s=0;s<n;s+=2){var o=t[s>>>2]>>>16-s%4*8&65535;r.push(String.fromCharCode(o))}return r.join("")},parse:function(e){for(var t=e.length,r=[],s=0;s<t;s++)r[s>>>1]|=e.charCodeAt(s)<<16-s%2*16;return n.create(r,2*t)}},r.Utf16LE={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],o=0;o<n;o+=2){var i=s(t[o>>>2]>>>16-o%4*8&65535);r.push(String.fromCharCode(i))}return r.join("")},parse:function(e){for(var t=e.length,r=[],o=0;o<t;o++)r[o>>>1]|=s(e.charCodeAt(o)<<16-o%2*16);return n.create(r,2*t)}}}(),e.enc.Utf16));var e}var Hw,Ww={exports:{}};function qw(){return Hw?Ww.exports:(Hw=1,Ww.exports=(e=Bw(),function(){var t=e,n=t.lib.WordArray;function r(e,t,r){for(var s=[],o=0,i=0;i<t;i++)if(i%4){var a=r[e.charCodeAt(i-1)]<<i%4*2|r[e.charCodeAt(i)]>>>6-i%4*2;s[o>>>2]|=a<<24-o%4*8,o++}return n.create(s,o)}t.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,r=this._map;e.clamp();for(var s=[],o=0;o<n;o+=3)for(var i=(t[o>>>2]>>>24-o%4*8&255)<<16|(t[o+1>>>2]>>>24-(o+1)%4*8&255)<<8|t[o+2>>>2]>>>24-(o+2)%4*8&255,a=0;a<4&&o+.75*a<n;a++)s.push(r.charAt(i>>>6*(3-a)&63));var l=r.charAt(64);if(l)for(;s.length%4;)s.push(l);return s.join("")},parse:function(e){var t=e.length,n=this._map,s=this._reverseMap;if(!s){s=this._reverseMap=[];for(var o=0;o<n.length;o++)s[n.charCodeAt(o)]=o}var i=n.charAt(64);if(i){var a=e.indexOf(i);-1!==a&&(t=a)}return r(e,t,s)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),e.enc.Base64));var e}var Vw,Xw={exports:{}};function Kw(){return Vw?Xw.exports:(Vw=1,Xw.exports=(e=Bw(),function(){var t=e,n=t.lib.WordArray;function r(e,t,r){for(var s=[],o=0,i=0;i<t;i++)if(i%4){var a=r[e.charCodeAt(i-1)]<<i%4*2|r[e.charCodeAt(i)]>>>6-i%4*2;s[o>>>2]|=a<<24-o%4*8,o++}return n.create(s,o)}t.enc.Base64url={stringify:function(e,t){void 0===t&&(t=!0);var n=e.words,r=e.sigBytes,s=t?this._safe_map:this._map;e.clamp();for(var o=[],i=0;i<r;i+=3)for(var a=(n[i>>>2]>>>24-i%4*8&255)<<16|(n[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|n[i+2>>>2]>>>24-(i+2)%4*8&255,l=0;l<4&&i+.75*l<r;l++)o.push(s.charAt(a>>>6*(3-l)&63));var c=s.charAt(64);if(c)for(;o.length%4;)o.push(c);return o.join("")},parse:function(e,t){void 0===t&&(t=!0);var n=e.length,s=t?this._safe_map:this._map,o=this._reverseMap;if(!o){o=this._reverseMap=[];for(var i=0;i<s.length;i++)o[s.charCodeAt(i)]=i}var a=s.charAt(64);if(a){var l=e.indexOf(a);-1!==l&&(n=l)}return r(e,n,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"}}(),e.enc.Base64url));var e}var Yw,Jw={exports:{}};function Gw(){return Yw?Jw.exports:(Yw=1,Jw.exports=(e=Bw(),function(t){var n=e,r=n.lib,s=r.WordArray,o=r.Hasher,i=n.algo,a=[];!function(){for(var e=0;e<64;e++)a[e]=4294967296*t.abs(t.sin(e+1))|0}();var l=i.MD5=o.extend({_doReset:function(){this._hash=new s.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var r=t+n,s=e[r];e[r]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8)}var o=this._hash.words,i=e[t+0],l=e[t+1],f=e[t+2],p=e[t+3],g=e[t+4],v=e[t+5],m=e[t+6],y=e[t+7],_=e[t+8],b=e[t+9],w=e[t+10],x=e[t+11],S=e[t+12],k=e[t+13],T=e[t+14],E=e[t+15],C=o[0],O=o[1],P=o[2],A=o[3];C=c(C,O,P,A,i,7,a[0]),A=c(A,C,O,P,l,12,a[1]),P=c(P,A,C,O,f,17,a[2]),O=c(O,P,A,C,p,22,a[3]),C=c(C,O,P,A,g,7,a[4]),A=c(A,C,O,P,v,12,a[5]),P=c(P,A,C,O,m,17,a[6]),O=c(O,P,A,C,y,22,a[7]),C=c(C,O,P,A,_,7,a[8]),A=c(A,C,O,P,b,12,a[9]),P=c(P,A,C,O,w,17,a[10]),O=c(O,P,A,C,x,22,a[11]),C=c(C,O,P,A,S,7,a[12]),A=c(A,C,O,P,k,12,a[13]),P=c(P,A,C,O,T,17,a[14]),C=u(C,O=c(O,P,A,C,E,22,a[15]),P,A,l,5,a[16]),A=u(A,C,O,P,m,9,a[17]),P=u(P,A,C,O,x,14,a[18]),O=u(O,P,A,C,i,20,a[19]),C=u(C,O,P,A,v,5,a[20]),A=u(A,C,O,P,w,9,a[21]),P=u(P,A,C,O,E,14,a[22]),O=u(O,P,A,C,g,20,a[23]),C=u(C,O,P,A,b,5,a[24]),A=u(A,C,O,P,T,9,a[25]),P=u(P,A,C,O,p,14,a[26]),O=u(O,P,A,C,_,20,a[27]),C=u(C,O,P,A,k,5,a[28]),A=u(A,C,O,P,f,9,a[29]),P=u(P,A,C,O,y,14,a[30]),C=h(C,O=u(O,P,A,C,S,20,a[31]),P,A,v,4,a[32]),A=h(A,C,O,P,_,11,a[33]),P=h(P,A,C,O,x,16,a[34]),O=h(O,P,A,C,T,23,a[35]),C=h(C,O,P,A,l,4,a[36]),A=h(A,C,O,P,g,11,a[37]),P=h(P,A,C,O,y,16,a[38]),O=h(O,P,A,C,w,23,a[39]),C=h(C,O,P,A,k,4,a[40]),A=h(A,C,O,P,i,11,a[41]),P=h(P,A,C,O,p,16,a[42]),O=h(O,P,A,C,m,23,a[43]),C=h(C,O,P,A,b,4,a[44]),A=h(A,C,O,P,S,11,a[45]),P=h(P,A,C,O,E,16,a[46]),C=d(C,O=h(O,P,A,C,f,23,a[47]),P,A,i,6,a[48]),A=d(A,C,O,P,y,10,a[49]),P=d(P,A,C,O,T,15,a[50]),O=d(O,P,A,C,v,21,a[51]),C=d(C,O,P,A,S,6,a[52]),A=d(A,C,O,P,p,10,a[53]),P=d(P,A,C,O,w,15,a[54]),O=d(O,P,A,C,l,21,a[55]),C=d(C,O,P,A,_,6,a[56]),A=d(A,C,O,P,E,10,a[57]),P=d(P,A,C,O,m,15,a[58]),O=d(O,P,A,C,k,21,a[59]),C=d(C,O,P,A,g,6,a[60]),A=d(A,C,O,P,x,10,a[61]),P=d(P,A,C,O,f,15,a[62]),O=d(O,P,A,C,b,21,a[63]),o[0]=o[0]+C|0,o[1]=o[1]+O|0,o[2]=o[2]+P|0,o[3]=o[3]+A|0},_doFinalize:function(){var e=this._data,n=e.words,r=8*this._nDataBytes,s=8*e.sigBytes;n[s>>>5]|=128<<24-s%32;var o=t.floor(r/4294967296),i=r;n[15+(s+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),n[14+(s+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),e.sigBytes=4*(n.length+1),this._process();for(var a=this._hash,l=a.words,c=0;c<4;c++){var u=l[c];l[c]=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8)}return a},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});function c(e,t,n,r,s,o,i){var a=e+(t&n|~t&r)+s+i;return(a<<o|a>>>32-o)+t}function u(e,t,n,r,s,o,i){var a=e+(t&r|n&~r)+s+i;return(a<<o|a>>>32-o)+t}function h(e,t,n,r,s,o,i){var a=e+(t^n^r)+s+i;return(a<<o|a>>>32-o)+t}function d(e,t,n,r,s,o,i){var a=e+(n^(t|~r))+s+i;return(a<<o|a>>>32-o)+t}n.MD5=o._createHelper(l),n.HmacMD5=o._createHmacHelper(l)}(Math),e.MD5));var e}var Zw,Qw={exports:{}};function ex(){return Zw?Qw.exports:(Zw=1,Qw.exports=(a=Bw(),t=(e=a).lib,n=t.WordArray,r=t.Hasher,s=e.algo,o=[],i=s.SHA1=r.extend({_doReset:function(){this._hash=new n.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],s=n[1],i=n[2],a=n[3],l=n[4],c=0;c<80;c++){if(c<16)o[c]=0|e[t+c];else{var u=o[c-3]^o[c-8]^o[c-14]^o[c-16];o[c]=u<<1|u>>>31}var h=(r<<5|r>>>27)+l+o[c];h+=c<20?1518500249+(s&i|~s&a):c<40?1859775393+(s^i^a):c<60?(s&i|s&a|i&a)-1894007588:(s^i^a)-899497514,l=a,a=i,i=s<<30|s>>>2,s=r,r=h}n[0]=n[0]+r|0,n[1]=n[1]+s|0,n[2]=n[2]+i|0,n[3]=n[3]+a|0,n[4]=n[4]+l|0},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[14+(r+64>>>9<<4)]=Math.floor(n/4294967296),t[15+(r+64>>>9<<4)]=n,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e}}),e.SHA1=r._createHelper(i),e.HmacSHA1=r._createHmacHelper(i),a.SHA1));var e,t,n,r,s,o,i,a}var tx,nx={exports:{}};function rx(){return tx?nx.exports:(tx=1,nx.exports=(e=Bw(),function(t){var n=e,r=n.lib,s=r.WordArray,o=r.Hasher,i=n.algo,a=[],l=[];!function(){function e(e){for(var n=t.sqrt(e),r=2;r<=n;r++)if(!(e%r))return!1;return!0}function n(e){return 4294967296*(e-(0|e))|0}for(var r=2,s=0;s<64;)e(r)&&(s<8&&(a[s]=n(t.pow(r,.5))),l[s]=n(t.pow(r,1/3)),s++),r++}();var c=[],u=i.SHA256=o.extend({_doReset:function(){this._hash=new s.init(a.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],s=n[1],o=n[2],i=n[3],a=n[4],u=n[5],h=n[6],d=n[7],f=0;f<64;f++){if(f<16)c[f]=0|e[t+f];else{var p=c[f-15],g=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,v=c[f-2],m=(v<<15|v>>>17)^(v<<13|v>>>19)^v>>>10;c[f]=g+c[f-7]+m+c[f-16]}var y=r&s^r&o^s&o,_=(r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22),b=d+((a<<26|a>>>6)^(a<<21|a>>>11)^(a<<7|a>>>25))+(a&u^~a&h)+l[f]+c[f];d=h,h=u,u=a,a=i+b|0,i=o,o=s,s=r,r=b+(_+y)|0}n[0]=n[0]+r|0,n[1]=n[1]+s|0,n[2]=n[2]+o|0,n[3]=n[3]+i|0,n[4]=n[4]+a|0,n[5]=n[5]+u|0,n[6]=n[6]+h|0,n[7]=n[7]+d|0},_doFinalize:function(){var e=this._data,n=e.words,r=8*this._nDataBytes,s=8*e.sigBytes;return n[s>>>5]|=128<<24-s%32,n[14+(s+64>>>9<<4)]=t.floor(r/4294967296),n[15+(s+64>>>9<<4)]=r,e.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});n.SHA256=o._createHelper(u),n.HmacSHA256=o._createHmacHelper(u)}(Math),e.SHA256));var e}var sx,ox={exports:{}};var ix,ax={exports:{}};function lx(){return ix||(ix=1,ax.exports=(e=Bw(),Lw(),function(){var t=e,n=t.lib.Hasher,r=t.x64,s=r.Word,o=r.WordArray,i=t.algo;function a(){return s.create.apply(s,arguments)}var l=[a(1116352408,3609767458),a(1899447441,602891725),a(3049323471,3964484399),a(3921009573,2173295548),a(961987163,4081628472),a(1508970993,3053834265),a(2453635748,2937671579),a(2870763221,3664609560),a(3624381080,2734883394),a(310598401,1164996542),a(607225278,1323610764),a(1426881987,3590304994),a(1925078388,4068182383),a(2162078206,991336113),a(2614888103,633803317),a(3248222580,3479774868),a(3835390401,2666613458),a(4022224774,944711139),a(264347078,2341262773),a(604807628,2007800933),a(770255983,1495990901),a(1249150122,1856431235),a(1555081692,3175218132),a(1996064986,2198950837),a(2554220882,3999719339),a(2821834349,766784016),a(2952996808,2566594879),a(3210313671,3203337956),a(3336571891,1034457026),a(3584528711,2466948901),a(113926993,3758326383),a(338241895,168717936),a(666307205,1188179964),a(773529912,1546045734),a(1294757372,1522805485),a(1396182291,2643833823),a(1695183700,2343527390),a(1986661051,1014477480),a(2177026350,1206759142),a(2456956037,344077627),a(2730485921,1290863460),a(2820302411,3158454273),a(3259730800,3505952657),a(3345764771,106217008),a(3516065817,3606008344),a(3600352804,1432725776),a(4094571909,1467031594),a(275423344,851169720),a(430227734,3100823752),a(506948616,1363258195),a(659060556,3750685593),a(883997877,3785050280),a(958139571,3318307427),a(1322822218,3812723403),a(1537002063,2003034995),a(1747873779,3602036899),a(1955562222,1575990012),a(2024104815,1125592928),a(2227730452,2716904306),a(2361852424,442776044),a(2428436474,593698344),a(2756734187,3733110249),a(3204031479,2999351573),a(3329325298,3815920427),a(3391569614,3928383900),a(3515267271,566280711),a(3940187606,3454069534),a(4118630271,4000239992),a(116418474,1914138554),a(174292421,2731055270),a(289380356,3203993006),a(460393269,320620315),a(685471733,587496836),a(852142971,1086792851),a(1017036298,365543100),a(1126000580,2618297676),a(1288033470,3409855158),a(1501505948,4234509866),a(1607167915,987167468),a(1816402316,1246189591)],c=[];!function(){for(var e=0;e<80;e++)c[e]=a()}();var u=i.SHA512=n.extend({_doReset:function(){this._hash=new o.init([new s.init(1779033703,4089235720),new s.init(3144134277,2227873595),new s.init(1013904242,4271175723),new s.init(2773480762,1595750129),new s.init(1359893119,2917565137),new s.init(2600822924,725511199),new s.init(528734635,4215389547),new s.init(1541459225,327033209)])},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],s=n[1],o=n[2],i=n[3],a=n[4],u=n[5],h=n[6],d=n[7],f=r.high,p=r.low,g=s.high,v=s.low,m=o.high,y=o.low,_=i.high,b=i.low,w=a.high,x=a.low,S=u.high,k=u.low,T=h.high,E=h.low,C=d.high,O=d.low,P=f,A=p,$=g,j=v,B=m,I=y,R=_,L=b,M=w,D=x,N=S,F=k,U=T,z=E,H=C,W=O,q=0;q<80;q++){var V,X,K=c[q];if(q<16)X=K.high=0|e[t+2*q],V=K.low=0|e[t+2*q+1];else{var Y=c[q-15],J=Y.high,G=Y.low,Z=(J>>>1|G<<31)^(J>>>8|G<<24)^J>>>7,Q=(G>>>1|J<<31)^(G>>>8|J<<24)^(G>>>7|J<<25),ee=c[q-2],te=ee.high,ne=ee.low,re=(te>>>19|ne<<13)^(te<<3|ne>>>29)^te>>>6,se=(ne>>>19|te<<13)^(ne<<3|te>>>29)^(ne>>>6|te<<26),oe=c[q-7],ie=oe.high,ae=oe.low,le=c[q-16],ce=le.high,ue=le.low;X=(X=(X=Z+ie+((V=Q+ae)>>>0<Q>>>0?1:0))+re+((V+=se)>>>0<se>>>0?1:0))+ce+((V+=ue)>>>0<ue>>>0?1:0),K.high=X,K.low=V}var he,de=M&N^~M&U,fe=D&F^~D&z,pe=P&$^P&B^$&B,ge=A&j^A&I^j&I,ve=(P>>>28|A<<4)^(P<<30|A>>>2)^(P<<25|A>>>7),me=(A>>>28|P<<4)^(A<<30|P>>>2)^(A<<25|P>>>7),ye=(M>>>14|D<<18)^(M>>>18|D<<14)^(M<<23|D>>>9),_e=(D>>>14|M<<18)^(D>>>18|M<<14)^(D<<23|M>>>9),be=l[q],we=be.high,xe=be.low,Se=H+ye+((he=W+_e)>>>0<W>>>0?1:0),ke=me+ge;H=U,W=z,U=N,z=F,N=M,F=D,M=R+(Se=(Se=(Se=Se+de+((he+=fe)>>>0<fe>>>0?1:0))+we+((he+=xe)>>>0<xe>>>0?1:0))+X+((he+=V)>>>0<V>>>0?1:0))+((D=L+he|0)>>>0<L>>>0?1:0)|0,R=B,L=I,B=$,I=j,$=P,j=A,P=Se+(ve+pe+(ke>>>0<me>>>0?1:0))+((A=he+ke|0)>>>0<he>>>0?1:0)|0}p=r.low=p+A,r.high=f+P+(p>>>0<A>>>0?1:0),v=s.low=v+j,s.high=g+$+(v>>>0<j>>>0?1:0),y=o.low=y+I,o.high=m+B+(y>>>0<I>>>0?1:0),b=i.low=b+L,i.high=_+R+(b>>>0<L>>>0?1:0),x=a.low=x+D,a.high=w+M+(x>>>0<D>>>0?1:0),k=u.low=k+F,u.high=S+N+(k>>>0<F>>>0?1:0),E=h.low=E+z,h.high=T+U+(E>>>0<z>>>0?1:0),O=d.low=O+W,d.high=C+H+(O>>>0<W>>>0?1:0)},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[30+(r+128>>>10<<5)]=Math.floor(n/4294967296),t[31+(r+128>>>10<<5)]=n,e.sigBytes=4*t.length,this._process(),this._hash.toX32()},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e},blockSize:32});t.SHA512=n._createHelper(u),t.HmacSHA512=n._createHmacHelper(u)}(),e.SHA512)),ax.exports;var e}var cx,ux={exports:{}};var hx,dx={exports:{}};function fx(){return hx?dx.exports:(hx=1,dx.exports=(e=Bw(),Lw(),function(t){var n=e,r=n.lib,s=r.WordArray,o=r.Hasher,i=n.x64.Word,a=n.algo,l=[],c=[],u=[];!function(){for(var e=1,t=0,n=0;n<24;n++){l[e+5*t]=(n+1)*(n+2)/2%64;var r=(2*e+3*t)%5;e=t%5,t=r}for(e=0;e<5;e++)for(t=0;t<5;t++)c[e+5*t]=t+(2*e+3*t)%5*5;for(var s=1,o=0;o<24;o++){for(var a=0,h=0,d=0;d<7;d++){if(1&s){var f=(1<<d)-1;f<32?h^=1<<f:a^=1<<f-32}128&s?s=s<<1^113:s<<=1}u[o]=i.create(a,h)}}();var h=[];!function(){for(var e=0;e<25;e++)h[e]=i.create()}();var d=a.SHA3=o.extend({cfg:o.cfg.extend({outputLength:512}),_doReset:function(){for(var e=this._state=[],t=0;t<25;t++)e[t]=new i.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(e,t){for(var n=this._state,r=this.blockSize/2,s=0;s<r;s++){var o=e[t+2*s],i=e[t+2*s+1];o=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),i=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),(O=n[s]).high^=i,O.low^=o}for(var a=0;a<24;a++){for(var d=0;d<5;d++){for(var f=0,p=0,g=0;g<5;g++)f^=(O=n[d+5*g]).high,p^=O.low;var v=h[d];v.high=f,v.low=p}for(d=0;d<5;d++){var m=h[(d+4)%5],y=h[(d+1)%5],_=y.high,b=y.low;for(f=m.high^(_<<1|b>>>31),p=m.low^(b<<1|_>>>31),g=0;g<5;g++)(O=n[d+5*g]).high^=f,O.low^=p}for(var w=1;w<25;w++){var x=(O=n[w]).high,S=O.low,k=l[w];k<32?(f=x<<k|S>>>32-k,p=S<<k|x>>>32-k):(f=S<<k-32|x>>>64-k,p=x<<k-32|S>>>64-k);var T=h[c[w]];T.high=f,T.low=p}var E=h[0],C=n[0];for(E.high=C.high,E.low=C.low,d=0;d<5;d++)for(g=0;g<5;g++){var O=n[w=d+5*g],P=h[w],A=h[(d+1)%5+5*g],$=h[(d+2)%5+5*g];O.high=P.high^~A.high&$.high,O.low=P.low^~A.low&$.low}O=n[0];var j=u[a];O.high^=j.high,O.low^=j.low}},_doFinalize:function(){var e=this._data,n=e.words;this._nDataBytes;var r=8*e.sigBytes,o=32*this.blockSize;n[r>>>5]|=1<<24-r%32,n[(t.ceil((r+1)/o)*o>>>5)-1]|=128,e.sigBytes=4*n.length,this._process();for(var i=this._state,a=this.cfg.outputLength/8,l=a/8,c=[],u=0;u<l;u++){var h=i[u],d=h.high,f=h.low;d=16711935&(d<<8|d>>>24)|4278255360&(d<<24|d>>>8),f=16711935&(f<<8|f>>>24)|4278255360&(f<<24|f>>>8),c.push(f),c.push(d)}return new s.init(c,a)},clone:function(){for(var e=o.clone.call(this),t=e._state=this._state.slice(0),n=0;n<25;n++)t[n]=t[n].clone();return e}});n.SHA3=o._createHelper(d),n.HmacSHA3=o._createHmacHelper(d)}(Math),e.SHA3));var e}var px,gx={exports:{}};var vx,mx={exports:{}};function yx(){return vx?mx.exports:(vx=1,mx.exports=(e=Bw(),n=(t=e).lib.Base,r=t.enc.Utf8,void(t.algo.HMAC=n.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=r.parse(t));var n=e.blockSize,s=4*n;t.sigBytes>s&&(t=e.finalize(t)),t.clamp();for(var o=this._oKey=t.clone(),i=this._iKey=t.clone(),a=o.words,l=i.words,c=0;c<n;c++)a[c]^=1549556828,l[c]^=909522486;o.sigBytes=i.sigBytes=s,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}}))));var e,t,n,r}var _x,bx={exports:{}};var wx,xx={exports:{}};function Sx(){return wx?xx.exports:(wx=1,xx.exports=(a=Bw(),ex(),yx(),t=(e=a).lib,n=t.Base,r=t.WordArray,s=e.algo,o=s.MD5,i=s.EvpKDF=n.extend({cfg:n.extend({keySize:4,hasher:o,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var n,s=this.cfg,o=s.hasher.create(),i=r.create(),a=i.words,l=s.keySize,c=s.iterations;a.length<l;){n&&o.update(n),n=o.update(e).finalize(t),o.reset();for(var u=1;u<c;u++)n=o.finalize(n),o.reset();i.concat(n)}return i.sigBytes=4*l,i}}),e.EvpKDF=function(e,t,n){return i.create(n).compute(e,t)},a.EvpKDF));var e,t,n,r,s,o,i,a}var kx,Tx={exports:{}};function Ex(){return kx?Tx.exports:(kx=1,Tx.exports=(e=Bw(),Sx(),void(e.lib.Cipher||function(t){var n=e,r=n.lib,s=r.Base,o=r.WordArray,i=r.BufferedBlockAlgorithm,a=n.enc;a.Utf8;var l=a.Base64,c=n.algo.EvpKDF,u=r.Cipher=i.extend({cfg:s.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,n){this.cfg=this.cfg.extend(n),this._xformMode=e,this._key=t,this.reset()},reset:function(){i.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function e(e){return"string"==typeof e?_:m}return function(t){return{encrypt:function(n,r,s){return e(r).encrypt(t,n,r,s)},decrypt:function(n,r,s){return e(r).decrypt(t,n,r,s)}}}}()});r.StreamCipher=u.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var h=n.mode={},d=r.BlockCipherMode=s.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),f=h.CBC=function(){var e=d.extend();function n(e,n,r){var s,o=this._iv;o?(s=o,this._iv=t):s=this._prevBlock;for(var i=0;i<r;i++)e[n+i]^=s[i]}return e.Encryptor=e.extend({processBlock:function(e,t){var r=this._cipher,s=r.blockSize;n.call(this,e,t,s),r.encryptBlock(e,t),this._prevBlock=e.slice(t,t+s)}}),e.Decryptor=e.extend({processBlock:function(e,t){var r=this._cipher,s=r.blockSize,o=e.slice(t,t+s);r.decryptBlock(e,t),n.call(this,e,t,s),this._prevBlock=o}}),e}(),p=(n.pad={}).Pkcs7={pad:function(e,t){for(var n=4*t,r=n-e.sigBytes%n,s=r<<24|r<<16|r<<8|r,i=[],a=0;a<r;a+=4)i.push(s);var l=o.create(i,r);e.concat(l)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}};r.BlockCipher=u.extend({cfg:u.cfg.extend({mode:f,padding:p}),reset:function(){var e;u.reset.call(this);var t=this.cfg,n=t.iv,r=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=r.createEncryptor:(e=r.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,n&&n.words):(this._mode=e.call(r,this,n&&n.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4});var g=r.CipherParams=s.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),v=(n.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext,n=e.salt;return(n?o.create([1398893684,1701076831]).concat(n).concat(t):t).toString(l)},parse:function(e){var t,n=l.parse(e),r=n.words;return 1398893684==r[0]&&1701076831==r[1]&&(t=o.create(r.slice(2,4)),r.splice(0,4),n.sigBytes-=16),g.create({ciphertext:n,salt:t})}},m=r.SerializableCipher=s.extend({cfg:s.extend({format:v}),encrypt:function(e,t,n,r){r=this.cfg.extend(r);var s=e.createEncryptor(n,r),o=s.finalize(t),i=s.cfg;return g.create({ciphertext:o,key:n,iv:i.iv,algorithm:e,mode:i.mode,padding:i.padding,blockSize:e.blockSize,formatter:r.format})},decrypt:function(e,t,n,r){return r=this.cfg.extend(r),t=this._parse(t,r.format),e.createDecryptor(n,r).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),y=(n.kdf={}).OpenSSL={execute:function(e,t,n,r,s){if(r||(r=o.random(8)),s)i=c.create({keySize:t+n,hasher:s}).compute(e,r);else var i=c.create({keySize:t+n}).compute(e,r);var a=o.create(i.words.slice(t),4*n);return i.sigBytes=4*t,g.create({key:i,iv:a,salt:r})}},_=r.PasswordBasedCipher=m.extend({cfg:m.cfg.extend({kdf:y}),encrypt:function(e,t,n,r){var s=(r=this.cfg.extend(r)).kdf.execute(n,e.keySize,e.ivSize,r.salt,r.hasher);r.iv=s.iv;var o=m.encrypt.call(this,e,t,s.key,r);return o.mixIn(s),o},decrypt:function(e,t,n,r){r=this.cfg.extend(r),t=this._parse(t,r.format);var s=r.kdf.execute(n,e.keySize,e.ivSize,t.salt,r.hasher);return r.iv=s.iv,m.decrypt.call(this,e,t,s.key,r)}})}())));var e}var Cx,Ox={exports:{}};var Px,Ax={exports:{}};var $x,jx={exports:{}};function Bx(){return $x?jx.exports:($x=1,jx.exports=(e=Bw(),Ex(),
/** @preserve
       * Counter block mode compatible with  Dr Brian Gladman fileenc.c
       * derived from CryptoJS.mode.CTR
       * <NAME_EMAIL>
       */
e.mode.CTRGladman=function(){var t=e.lib.BlockCipherMode.extend();function n(e){if(255&~(e>>24))e+=1<<24;else{var t=e>>16&255,n=e>>8&255,r=255&e;255===t?(t=0,255===n?(n=0,255===r?r=0:++r):++n):++t,e=0,e+=t<<16,e+=n<<8,e+=r}return e}function r(e){return 0===(e[0]=n(e[0]))&&(e[1]=n(e[1])),e}var s=t.Encryptor=t.extend({processBlock:function(e,t){var n=this._cipher,s=n.blockSize,o=this._iv,i=this._counter;o&&(i=this._counter=o.slice(0),this._iv=void 0),r(i);var a=i.slice(0);n.encryptBlock(a,0);for(var l=0;l<s;l++)e[t+l]^=a[l]}});return t.Decryptor=s,t}(),e.mode.CTRGladman));var e}var Ix,Rx={exports:{}};var Lx,Mx={exports:{}};var Dx,Nx={exports:{}};var Fx,Ux={exports:{}};var zx,Hx={exports:{}};var Wx,qx={exports:{}};var Vx,Xx={exports:{}};var Kx,Yx={exports:{}};var Jx,Gx={exports:{}};var Zx,Qx={exports:{}};function eS(){return Zx?Qx.exports:(Zx=1,Qx.exports=(e=Bw(),qw(),Gw(),Sx(),Ex(),function(){var t=e,n=t.lib,r=n.WordArray,s=n.BlockCipher,o=t.algo,i=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],a=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],l=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],c=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],u=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],h=o.DES=s.extend({_doReset:function(){for(var e=this._key.words,t=[],n=0;n<56;n++){var r=i[n]-1;t[n]=e[r>>>5]>>>31-r%32&1}for(var s=this._subKeys=[],o=0;o<16;o++){var c=s[o]=[],u=l[o];for(n=0;n<24;n++)c[n/6|0]|=t[(a[n]-1+u)%28]<<31-n%6,c[4+(n/6|0)]|=t[28+(a[n+24]-1+u)%28]<<31-n%6;for(c[0]=c[0]<<1|c[0]>>>31,n=1;n<7;n++)c[n]=c[n]>>>4*(n-1)+3;c[7]=c[7]<<5|c[7]>>>27}var h=this._invSubKeys=[];for(n=0;n<16;n++)h[n]=s[15-n]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._subKeys)},decryptBlock:function(e,t){this._doCryptBlock(e,t,this._invSubKeys)},_doCryptBlock:function(e,t,n){this._lBlock=e[t],this._rBlock=e[t+1],d.call(this,4,252645135),d.call(this,16,65535),f.call(this,2,858993459),f.call(this,8,16711935),d.call(this,1,1431655765);for(var r=0;r<16;r++){for(var s=n[r],o=this._lBlock,i=this._rBlock,a=0,l=0;l<8;l++)a|=c[l][((i^s[l])&u[l])>>>0];this._lBlock=i,this._rBlock=o^a}var h=this._lBlock;this._lBlock=this._rBlock,this._rBlock=h,d.call(this,1,1431655765),f.call(this,8,16711935),f.call(this,2,858993459),d.call(this,16,65535),d.call(this,4,252645135),e[t]=this._lBlock,e[t+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function d(e,t){var n=(this._lBlock>>>e^this._rBlock)&t;this._rBlock^=n,this._lBlock^=n<<e}function f(e,t){var n=(this._rBlock>>>e^this._lBlock)&t;this._lBlock^=n,this._rBlock^=n<<e}t.DES=s._createHelper(h);var p=o.TripleDES=s.extend({_doReset:function(){var e=this._key.words;if(2!==e.length&&4!==e.length&&e.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var t=e.slice(0,2),n=e.length<4?e.slice(0,2):e.slice(2,4),s=e.length<6?e.slice(0,2):e.slice(4,6);this._des1=h.createEncryptor(r.create(t)),this._des2=h.createEncryptor(r.create(n)),this._des3=h.createEncryptor(r.create(s))},encryptBlock:function(e,t){this._des1.encryptBlock(e,t),this._des2.decryptBlock(e,t),this._des3.encryptBlock(e,t)},decryptBlock:function(e,t){this._des3.decryptBlock(e,t),this._des2.encryptBlock(e,t),this._des1.decryptBlock(e,t)},keySize:6,ivSize:2,blockSize:2});t.TripleDES=s._createHelper(p)}(),e.TripleDES));var e}var tS,nS={exports:{}};var rS,sS={exports:{}};var oS,iS={exports:{}};var aS,lS,cS,uS,hS,dS,fS,pS={exports:{}};function gS(){return aS?pS.exports:(aS=1,pS.exports=(e=Bw(),qw(),Gw(),Sx(),Ex(),function(){var t=e,n=t.lib.BlockCipher,r=t.algo;const s=16,o=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731],i=[[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946],[1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055],[3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504],[976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462]];var a={pbox:[],sbox:[]};function l(e,t){let n=t>>24&255,r=t>>16&255,s=t>>8&255,o=255&t,i=e.sbox[0][n]+e.sbox[1][r];return i^=e.sbox[2][s],i+=e.sbox[3][o],i}function c(e,t,n){let r,o=t,i=n;for(let a=0;a<s;++a)o^=e.pbox[a],i=l(e,o)^i,r=o,o=i,i=r;return r=o,o=i,i=r,i^=e.pbox[s],o^=e.pbox[s+1],{left:o,right:i}}function u(e,t,n){let r,o=t,i=n;for(let a=s+1;a>1;--a)o^=e.pbox[a],i=l(e,o)^i,r=o,o=i,i=r;return r=o,o=i,i=r,i^=e.pbox[1],o^=e.pbox[0],{left:o,right:i}}function h(e,t,n){for(let s=0;s<4;s++){e.sbox[s]=[];for(let t=0;t<256;t++)e.sbox[s][t]=i[s][t]}let r=0;for(let i=0;i<s+2;i++)e.pbox[i]=o[i]^t[r],r++,r>=n&&(r=0);let a=0,l=0,u=0;for(let o=0;o<s+2;o+=2)u=c(e,a,l),a=u.left,l=u.right,e.pbox[o]=a,e.pbox[o+1]=l;for(let s=0;s<4;s++)for(let t=0;t<256;t+=2)u=c(e,a,l),a=u.left,l=u.right,e.sbox[s][t]=a,e.sbox[s][t+1]=l;return!0}var d=r.Blowfish=n.extend({_doReset:function(){if(this._keyPriorReset!==this._key){var e=this._keyPriorReset=this._key,t=e.words,n=e.sigBytes/4;h(a,t,n)}},encryptBlock:function(e,t){var n=c(a,e[t],e[t+1]);e[t]=n.left,e[t+1]=n.right},decryptBlock:function(e,t){var n=u(a,e[t],e[t+1]);e[t]=n.left,e[t+1]=n.right},blockSize:2,keySize:4,ivSize:2});t.Blowfish=n._createHelper(d)}(),e.Blowfish));var e}const vS=zm(Pw.exports=function(e){return e}(Bw(),Lw(),Nw(),zw(),qw(),Kw(),Gw(),ex(),rx(),sx||(sx=1,ox.exports=(fS=Bw(),rx(),cS=(lS=fS).lib.WordArray,uS=lS.algo,hS=uS.SHA256,dS=uS.SHA224=hS.extend({_doReset:function(){this._hash=new cS.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var e=hS._doFinalize.call(this);return e.sigBytes-=4,e}}),lS.SHA224=hS._createHelper(dS),lS.HmacSHA224=hS._createHmacHelper(dS),fS.SHA224)),lx(),function(){return cx?ux.exports:(cx=1,ux.exports=(a=Bw(),Lw(),lx(),t=(e=a).x64,n=t.Word,r=t.WordArray,s=e.algo,o=s.SHA512,i=s.SHA384=o.extend({_doReset:function(){this._hash=new r.init([new n.init(3418070365,3238371032),new n.init(1654270250,914150663),new n.init(2438529370,812702999),new n.init(355462360,4144912697),new n.init(1731405415,4290775857),new n.init(2394180231,1750603025),new n.init(3675008525,1694076839),new n.init(1203062813,3204075428)])},_doFinalize:function(){var e=o._doFinalize.call(this);return e.sigBytes-=16,e}}),e.SHA384=o._createHelper(i),e.HmacSHA384=o._createHmacHelper(i),a.SHA384));var e,t,n,r,s,o,i,a}(),fx(),function(){return px?gx.exports:(px=1,gx.exports=(e=Bw(),
/** @preserve
      			(c) 2012 by Cédric Mesnil. All rights reserved.
      
      			Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:
      
      			    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
      			    - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.
      
      			THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
      			*/
function(){var t=e,n=t.lib,r=n.WordArray,s=n.Hasher,o=t.algo,i=r.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),a=r.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),l=r.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),c=r.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),u=r.create([0,1518500249,1859775393,2400959708,2840853838]),h=r.create([1352829926,1548603684,1836072691,2053994217,0]),d=o.RIPEMD160=s.extend({_doReset:function(){this._hash=r.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var r=t+n,s=e[r];e[r]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8)}var o,d,_,b,w,x,S,k,T,E,C,O=this._hash.words,P=u.words,A=h.words,$=i.words,j=a.words,B=l.words,I=c.words;for(x=o=O[0],S=d=O[1],k=_=O[2],T=b=O[3],E=w=O[4],n=0;n<80;n+=1)C=o+e[t+$[n]]|0,C+=n<16?f(d,_,b)+P[0]:n<32?p(d,_,b)+P[1]:n<48?g(d,_,b)+P[2]:n<64?v(d,_,b)+P[3]:m(d,_,b)+P[4],C=(C=y(C|=0,B[n]))+w|0,o=w,w=b,b=y(_,10),_=d,d=C,C=x+e[t+j[n]]|0,C+=n<16?m(S,k,T)+A[0]:n<32?v(S,k,T)+A[1]:n<48?g(S,k,T)+A[2]:n<64?p(S,k,T)+A[3]:f(S,k,T)+A[4],C=(C=y(C|=0,I[n]))+E|0,x=E,E=T,T=y(k,10),k=S,S=C;C=O[1]+_+T|0,O[1]=O[2]+b+E|0,O[2]=O[3]+w+x|0,O[3]=O[4]+o+S|0,O[4]=O[0]+d+k|0,O[0]=C},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,r=8*e.sigBytes;t[r>>>5]|=128<<24-r%32,t[14+(r+64>>>9<<4)]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8),e.sigBytes=4*(t.length+1),this._process();for(var s=this._hash,o=s.words,i=0;i<5;i++){var a=o[i];o[i]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8)}return s},clone:function(){var e=s.clone.call(this);return e._hash=this._hash.clone(),e}});function f(e,t,n){return e^t^n}function p(e,t,n){return e&t|~e&n}function g(e,t,n){return(e|~t)^n}function v(e,t,n){return e&n|t&~n}function m(e,t,n){return e^(t|~n)}function y(e,t){return e<<t|e>>>32-t}t.RIPEMD160=s._createHelper(d),t.HmacRIPEMD160=s._createHmacHelper(d)}(),e.RIPEMD160));var e}(),yx(),function(){return _x?bx.exports:(_x=1,bx.exports=(l=Bw(),rx(),yx(),t=(e=l).lib,n=t.Base,r=t.WordArray,s=e.algo,o=s.SHA256,i=s.HMAC,a=s.PBKDF2=n.extend({cfg:n.extend({keySize:4,hasher:o,iterations:25e4}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var n=this.cfg,s=i.create(n.hasher,e),o=r.create(),a=r.create([1]),l=o.words,c=a.words,u=n.keySize,h=n.iterations;l.length<u;){var d=s.update(t).finalize(a);s.reset();for(var f=d.words,p=f.length,g=d,v=1;v<h;v++){g=s.finalize(g),s.reset();for(var m=g.words,y=0;y<p;y++)f[y]^=m[y]}o.concat(d),c[0]++}return o.sigBytes=4*u,o}}),e.PBKDF2=function(e,t,n){return a.create(n).compute(e,t)},l.PBKDF2));var e,t,n,r,s,o,i,a,l}(),Sx(),Ex(),function(){return Cx?Ox.exports:(Cx=1,Ox.exports=(e=Bw(),Ex(),e.mode.CFB=function(){var t=e.lib.BlockCipherMode.extend();function n(e,t,n,r){var s,o=this._iv;o?(s=o.slice(0),this._iv=void 0):s=this._prevBlock,r.encryptBlock(s,0);for(var i=0;i<n;i++)e[t+i]^=s[i]}return t.Encryptor=t.extend({processBlock:function(e,t){var r=this._cipher,s=r.blockSize;n.call(this,e,t,s,r),this._prevBlock=e.slice(t,t+s)}}),t.Decryptor=t.extend({processBlock:function(e,t){var r=this._cipher,s=r.blockSize,o=e.slice(t,t+s);n.call(this,e,t,s,r),this._prevBlock=o}}),t}(),e.mode.CFB));var e}(),function(){return Px?Ax.exports:(Px=1,Ax.exports=(n=Bw(),Ex(),n.mode.CTR=(e=n.lib.BlockCipherMode.extend(),t=e.Encryptor=e.extend({processBlock:function(e,t){var n=this._cipher,r=n.blockSize,s=this._iv,o=this._counter;s&&(o=this._counter=s.slice(0),this._iv=void 0);var i=o.slice(0);n.encryptBlock(i,0),o[r-1]=o[r-1]+1|0;for(var a=0;a<r;a++)e[t+a]^=i[a]}}),e.Decryptor=t,e),n.mode.CTR));var e,t,n}(),Bx(),function(){return Ix?Rx.exports:(Ix=1,Rx.exports=(n=Bw(),Ex(),n.mode.OFB=(e=n.lib.BlockCipherMode.extend(),t=e.Encryptor=e.extend({processBlock:function(e,t){var n=this._cipher,r=n.blockSize,s=this._iv,o=this._keystream;s&&(o=this._keystream=s.slice(0),this._iv=void 0),n.encryptBlock(o,0);for(var i=0;i<r;i++)e[t+i]^=o[i]}}),e.Decryptor=t,e),n.mode.OFB));var e,t,n}(),function(){return Lx?Mx.exports:(Lx=1,Mx.exports=(t=Bw(),Ex(),t.mode.ECB=((e=t.lib.BlockCipherMode.extend()).Encryptor=e.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),e.Decryptor=e.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),e),t.mode.ECB));var e,t}(),function(){return Dx?Nx.exports:(Dx=1,Nx.exports=(e=Bw(),Ex(),e.pad.AnsiX923={pad:function(e,t){var n=e.sigBytes,r=4*t,s=r-n%r,o=n+s-1;e.clamp(),e.words[o>>>2]|=s<<24-o%4*8,e.sigBytes+=s},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},e.pad.Ansix923));var e}(),function(){return Fx?Ux.exports:(Fx=1,Ux.exports=(e=Bw(),Ex(),e.pad.Iso10126={pad:function(t,n){var r=4*n,s=r-t.sigBytes%r;t.concat(e.lib.WordArray.random(s-1)).concat(e.lib.WordArray.create([s<<24],1))},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},e.pad.Iso10126));var e}(),function(){return zx?Hx.exports:(zx=1,Hx.exports=(e=Bw(),Ex(),e.pad.Iso97971={pad:function(t,n){t.concat(e.lib.WordArray.create([2147483648],1)),e.pad.ZeroPadding.pad(t,n)},unpad:function(t){e.pad.ZeroPadding.unpad(t),t.sigBytes--}},e.pad.Iso97971));var e}(),function(){return Wx?qx.exports:(Wx=1,qx.exports=(e=Bw(),Ex(),e.pad.ZeroPadding={pad:function(e,t){var n=4*t;e.clamp(),e.sigBytes+=n-(e.sigBytes%n||n)},unpad:function(e){var t=e.words,n=e.sigBytes-1;for(n=e.sigBytes-1;n>=0;n--)if(t[n>>>2]>>>24-n%4*8&255){e.sigBytes=n+1;break}}},e.pad.ZeroPadding));var e}(),function(){return Vx?Xx.exports:(Vx=1,Xx.exports=(e=Bw(),Ex(),e.pad.NoPadding={pad:function(){},unpad:function(){}},e.pad.NoPadding));var e}(),function(){return Kx?Yx.exports:(Kx=1,Yx.exports=(r=Bw(),Ex(),t=(e=r).lib.CipherParams,n=e.enc.Hex,e.format.Hex={stringify:function(e){return e.ciphertext.toString(n)},parse:function(e){var r=n.parse(e);return t.create({ciphertext:r})}},r.format.Hex));var e,t,n,r}(),function(){return Jx?Gx.exports:(Jx=1,Gx.exports=(e=Bw(),qw(),Gw(),Sx(),Ex(),function(){var t=e,n=t.lib.BlockCipher,r=t.algo,s=[],o=[],i=[],a=[],l=[],c=[],u=[],h=[],d=[],f=[];!function(){for(var e=[],t=0;t<256;t++)e[t]=t<128?t<<1:t<<1^283;var n=0,r=0;for(t=0;t<256;t++){var p=r^r<<1^r<<2^r<<3^r<<4;p=p>>>8^255&p^99,s[n]=p,o[p]=n;var g=e[n],v=e[g],m=e[v],y=257*e[p]^16843008*p;i[n]=y<<24|y>>>8,a[n]=y<<16|y>>>16,l[n]=y<<8|y>>>24,c[n]=y,y=16843009*m^65537*v^257*g^16843008*n,u[p]=y<<24|y>>>8,h[p]=y<<16|y>>>16,d[p]=y<<8|y>>>24,f[p]=y,n?(n=g^e[e[e[m^g]]],r^=e[e[r]]):n=r=1}}();var p=[0,1,2,4,8,16,32,64,128,27,54],g=r.AES=n.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e=this._keyPriorReset=this._key,t=e.words,n=e.sigBytes/4,r=4*((this._nRounds=n+6)+1),o=this._keySchedule=[],i=0;i<r;i++)i<n?o[i]=t[i]:(c=o[i-1],i%n?n>6&&i%n==4&&(c=s[c>>>24]<<24|s[c>>>16&255]<<16|s[c>>>8&255]<<8|s[255&c]):(c=s[(c=c<<8|c>>>24)>>>24]<<24|s[c>>>16&255]<<16|s[c>>>8&255]<<8|s[255&c],c^=p[i/n|0]<<24),o[i]=o[i-n]^c);for(var a=this._invKeySchedule=[],l=0;l<r;l++){if(i=r-l,l%4)var c=o[i];else c=o[i-4];a[l]=l<4||i<=4?c:u[s[c>>>24]]^h[s[c>>>16&255]]^d[s[c>>>8&255]]^f[s[255&c]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,i,a,l,c,s)},decryptBlock:function(e,t){var n=e[t+1];e[t+1]=e[t+3],e[t+3]=n,this._doCryptBlock(e,t,this._invKeySchedule,u,h,d,f,o),n=e[t+1],e[t+1]=e[t+3],e[t+3]=n},_doCryptBlock:function(e,t,n,r,s,o,i,a){for(var l=this._nRounds,c=e[t]^n[0],u=e[t+1]^n[1],h=e[t+2]^n[2],d=e[t+3]^n[3],f=4,p=1;p<l;p++){var g=r[c>>>24]^s[u>>>16&255]^o[h>>>8&255]^i[255&d]^n[f++],v=r[u>>>24]^s[h>>>16&255]^o[d>>>8&255]^i[255&c]^n[f++],m=r[h>>>24]^s[d>>>16&255]^o[c>>>8&255]^i[255&u]^n[f++],y=r[d>>>24]^s[c>>>16&255]^o[u>>>8&255]^i[255&h]^n[f++];c=g,u=v,h=m,d=y}g=(a[c>>>24]<<24|a[u>>>16&255]<<16|a[h>>>8&255]<<8|a[255&d])^n[f++],v=(a[u>>>24]<<24|a[h>>>16&255]<<16|a[d>>>8&255]<<8|a[255&c])^n[f++],m=(a[h>>>24]<<24|a[d>>>16&255]<<16|a[c>>>8&255]<<8|a[255&u])^n[f++],y=(a[d>>>24]<<24|a[c>>>16&255]<<16|a[u>>>8&255]<<8|a[255&h])^n[f++],e[t]=g,e[t+1]=v,e[t+2]=m,e[t+3]=y},keySize:8});t.AES=n._createHelper(g)}(),e.AES));var e}(),eS(),function(){return tS?nS.exports:(tS=1,nS.exports=(e=Bw(),qw(),Gw(),Sx(),Ex(),function(){var t=e,n=t.lib.StreamCipher,r=t.algo,s=r.RC4=n.extend({_doReset:function(){for(var e=this._key,t=e.words,n=e.sigBytes,r=this._S=[],s=0;s<256;s++)r[s]=s;s=0;for(var o=0;s<256;s++){var i=s%n,a=t[i>>>2]>>>24-i%4*8&255;o=(o+r[s]+a)%256;var l=r[s];r[s]=r[o],r[o]=l}this._i=this._j=0},_doProcessBlock:function(e,t){e[t]^=o.call(this)},keySize:8,ivSize:0});function o(){for(var e=this._S,t=this._i,n=this._j,r=0,s=0;s<4;s++){n=(n+e[t=(t+1)%256])%256;var o=e[t];e[t]=e[n],e[n]=o,r|=e[(e[t]+e[n])%256]<<24-8*s}return this._i=t,this._j=n,r}t.RC4=n._createHelper(s);var i=r.RC4Drop=s.extend({cfg:s.cfg.extend({drop:192}),_doReset:function(){s._doReset.call(this);for(var e=this.cfg.drop;e>0;e--)o.call(this)}});t.RC4Drop=n._createHelper(i)}(),e.RC4));var e}(),function(){return rS?sS.exports:(rS=1,sS.exports=(e=Bw(),qw(),Gw(),Sx(),Ex(),function(){var t=e,n=t.lib.StreamCipher,r=t.algo,s=[],o=[],i=[],a=r.Rabbit=n.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,n=0;n<4;n++)e[n]=16711935&(e[n]<<8|e[n]>>>24)|4278255360&(e[n]<<24|e[n]>>>8);var r=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],s=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];for(this._b=0,n=0;n<4;n++)l.call(this);for(n=0;n<8;n++)s[n]^=r[n+4&7];if(t){var o=t.words,i=o[0],a=o[1],c=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),u=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),h=c>>>16|4294901760&u,d=u<<16|65535&c;for(s[0]^=c,s[1]^=h,s[2]^=u,s[3]^=d,s[4]^=c,s[5]^=h,s[6]^=u,s[7]^=d,n=0;n<4;n++)l.call(this)}},_doProcessBlock:function(e,t){var n=this._X;l.call(this),s[0]=n[0]^n[5]>>>16^n[3]<<16,s[1]=n[2]^n[7]>>>16^n[5]<<16,s[2]=n[4]^n[1]>>>16^n[7]<<16,s[3]=n[6]^n[3]>>>16^n[1]<<16;for(var r=0;r<4;r++)s[r]=16711935&(s[r]<<8|s[r]>>>24)|4278255360&(s[r]<<24|s[r]>>>8),e[t+r]^=s[r]},blockSize:4,ivSize:2});function l(){for(var e=this._X,t=this._C,n=0;n<8;n++)o[n]=t[n];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<o[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<o[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<o[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<o[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<o[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<o[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<o[6]>>>0?1:0)|0,this._b=t[7]>>>0<o[7]>>>0?1:0,n=0;n<8;n++){var r=e[n]+t[n],s=65535&r,a=r>>>16,l=((s*s>>>17)+s*a>>>15)+a*a,c=((4294901760&r)*r|0)+((65535&r)*r|0);i[n]=l^c}e[0]=i[0]+(i[7]<<16|i[7]>>>16)+(i[6]<<16|i[6]>>>16)|0,e[1]=i[1]+(i[0]<<8|i[0]>>>24)+i[7]|0,e[2]=i[2]+(i[1]<<16|i[1]>>>16)+(i[0]<<16|i[0]>>>16)|0,e[3]=i[3]+(i[2]<<8|i[2]>>>24)+i[1]|0,e[4]=i[4]+(i[3]<<16|i[3]>>>16)+(i[2]<<16|i[2]>>>16)|0,e[5]=i[5]+(i[4]<<8|i[4]>>>24)+i[3]|0,e[6]=i[6]+(i[5]<<16|i[5]>>>16)+(i[4]<<16|i[4]>>>16)|0,e[7]=i[7]+(i[6]<<8|i[6]>>>24)+i[5]|0}t.Rabbit=n._createHelper(a)}(),e.Rabbit));var e}(),function(){return oS?iS.exports:(oS=1,iS.exports=(e=Bw(),qw(),Gw(),Sx(),Ex(),function(){var t=e,n=t.lib.StreamCipher,r=t.algo,s=[],o=[],i=[],a=r.RabbitLegacy=n.extend({_doReset:function(){var e=this._key.words,t=this.cfg.iv,n=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],r=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];this._b=0;for(var s=0;s<4;s++)l.call(this);for(s=0;s<8;s++)r[s]^=n[s+4&7];if(t){var o=t.words,i=o[0],a=o[1],c=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),u=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),h=c>>>16|4294901760&u,d=u<<16|65535&c;for(r[0]^=c,r[1]^=h,r[2]^=u,r[3]^=d,r[4]^=c,r[5]^=h,r[6]^=u,r[7]^=d,s=0;s<4;s++)l.call(this)}},_doProcessBlock:function(e,t){var n=this._X;l.call(this),s[0]=n[0]^n[5]>>>16^n[3]<<16,s[1]=n[2]^n[7]>>>16^n[5]<<16,s[2]=n[4]^n[1]>>>16^n[7]<<16,s[3]=n[6]^n[3]>>>16^n[1]<<16;for(var r=0;r<4;r++)s[r]=16711935&(s[r]<<8|s[r]>>>24)|4278255360&(s[r]<<24|s[r]>>>8),e[t+r]^=s[r]},blockSize:4,ivSize:2});function l(){for(var e=this._X,t=this._C,n=0;n<8;n++)o[n]=t[n];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<o[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<o[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<o[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<o[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<o[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<o[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<o[6]>>>0?1:0)|0,this._b=t[7]>>>0<o[7]>>>0?1:0,n=0;n<8;n++){var r=e[n]+t[n],s=65535&r,a=r>>>16,l=((s*s>>>17)+s*a>>>15)+a*a,c=((4294901760&r)*r|0)+((65535&r)*r|0);i[n]=l^c}e[0]=i[0]+(i[7]<<16|i[7]>>>16)+(i[6]<<16|i[6]>>>16)|0,e[1]=i[1]+(i[0]<<8|i[0]>>>24)+i[7]|0,e[2]=i[2]+(i[1]<<16|i[1]>>>16)+(i[0]<<16|i[0]>>>16)|0,e[3]=i[3]+(i[2]<<8|i[2]>>>24)+i[1]|0,e[4]=i[4]+(i[3]<<16|i[3]>>>16)+(i[2]<<16|i[2]>>>16)|0,e[5]=i[5]+(i[4]<<8|i[4]>>>24)+i[3]|0,e[6]=i[6]+(i[5]<<16|i[5]>>>16)+(i[4]<<16|i[4]>>>16)|0,e[7]=i[7]+(i[6]<<8|i[6]>>>24)+i[5]|0}t.RabbitLegacy=n._createHelper(a)}(),e.RabbitLegacy));var e}(),gS())),mS=e=>vS.SHA256(e).toString(),yS=()=>vS.lib.WordArray.random(32).toString(),_S=function(e,t,n){let r,s;const o="function"==typeof t;function i(e,n){const i=Ho();(e=e||i&&Vs(_m,null))&&ym(e),(e=mm)._s.has(r)||(o?jm(r,t,s,e):$m(r,s,e));return e._s.get(r)}return r=e,s=o?n:t,i.$id=r,i}("auth",()=>{const e=$n(null),t=$n(""),n=$n(!1),r=$n(null),s=ei(()=>!!t.value&&!!e.value),o=ei(()=>s.value),i=ei(()=>{var t;return(null==(t=e.value)?void 0:t.nickname)||""}),a=ei(()=>{var t;return(null==(t=e.value)?void 0:t.id)||""}),l=ei(()=>{var t;return(null==(t=e.value)?void 0:t.avatar_url)||""}),c=n=>{n.user&&(e.value=n.user),n.token&&(t.value=n.token,_g("auth_token",n.token)),r.value=null},u=()=>{e.value=null,t.value="",xg("auth_token"),r.value=null},h=async()=>{const e=wg("auth_token");if(!e)return!1;n.value=!0,r.value=null;try{const t=await(async e=>{try{if(!e)return{success:!1,error:"令牌不存在"};const{data:t,error:n}=await Cw().from("user_sessions").select("user_id, expires_at").eq("token",e).single();if(n)return"PGRST116"===n.code?{success:!1,error:"无效的令牌"}:(console.error("验证会话失败:",n),{success:!1,error:"验证会话失败"});if(new Date(t.expires_at)<new Date)return await Cw().from("user_sessions").delete().eq("token",e),{success:!1,error:"令牌已过期"};const{data:r,error:s}=await Cw().from("users").select("id, phone, nickname, avatar_url, gender, bio").eq("id",t.user_id).single();return s?(console.error("获取用户信息失败:",s),{success:!1,error:"获取用户信息失败"}):{success:!0,data:{user:{id:r.id,phone:r.phone,nickname:r.nickname,gender:r.gender,avatar_url:r.avatar_url,bio:r.bio}}}}catch(r){return console.error("验证会话异常:",r),{success:!1,error:r.message||"验证会话失败"}}})(e);return t.success&&t.data?(c({user:t.data.user,token:e}),!0):(u(),!1)}catch(t){return console.error("验证会话失败:",t),u(),!1}finally{n.value=!1}};return{user:e,token:t,isLoading:n,error:r,isAuthenticated:s,isLoggedIn:o,username:i,userId:a,avatarUrl:l,registerWithPhone:async e=>{n.value=!0,r.value=null;try{const t=await(async e=>{try{const{phone:n,password:r,nickname:s,gender:o=0,avatarUrl:i="",invitationCode:a=""}=e,l={0:"undisclosed",1:"male",2:"female"}[o]||"undisclosed";if(console.log("[注册流程] 开始手机号注册:",{phone:n,nickname:s,gender:o,timestamp:(new Date).toISOString()}),!n||!r||!s)return console.error("[注册流程] 表单数据不完整:",{phone:!!n,password:!!r,nickname:!!s}),{success:!1,error:"手机号、密码和昵称不能为空"};if(r.length<6)return console.error("[注册流程] 密码长度不足:",r.length),{success:!1,error:"密码长度不能少于6位"};console.log("[注册流程] 开始密码加密");const c=mS(r);console.log("[注册流程] 密码加密完成"),console.log("[注册流程] 开始创建用户记录");const{data:u,error:h}=await Cw().from("users").insert({phone:n,password_hash:c,nickname:s,gender:l,avatar_url:i}).select().single();if(console.log("[注册流程] 用户创建结果:",{user:u,userError:h}),h){if(console.error("[注册流程] 创建用户失败:",h),"23505"===h.code){if(h.message.includes("phone"))return{success:!1,error:"手机号已被注册"};if(h.message.includes("nickname"))return{success:!1,error:"昵称已被使用"}}return{success:!1,error:"注册失败，请重试"}}console.log("[注册流程] 开始创建用户会话");const d=yS(),f=new Date(Date.now()+6048e5),{data:p,error:g}=await Cw().from("user_sessions").insert({user_id:u.id,token:d,expires_at:f}).select().single();if(console.log("[注册流程] 会话创建结果:",{session:p,sessionError:g}),g)return console.error("[注册流程] 创建会话失败:",g),{success:!1,error:"注册失败，请重试"};if(a){console.log("[注册流程] 开始处理邀请码:",a);try{const{data:e}=await Cw().from("invitations").select("current_uses").eq("code",a).single();e&&await Cw().from("invitations").update({current_uses:(e.current_uses||0)+1,used_by:u.id}).eq("code",a)}catch(t){console.warn("[注册流程] 邀请码处理失败，但不影响注册:",t)}}return console.log("[注册流程] 注册成功:",u.id),{success:!0,data:{user:u,token:d},message:"注册成功"}}catch(r){return console.error("注册失败:",r),{success:!1,error:r.message||"注册失败，请重试"}}})(e);return t.success&&t.data?(c(t.data),t):(r.value=t.error||"注册失败",t)}catch(t){return r.value=t.message||"注册失败",console.error("注册失败:",t),{success:!1,error:t.message||"注册失败"}}finally{n.value=!1}},login:async e=>{n.value=!0,r.value=null;try{const t=await(async({phone:e,password:t})=>{try{console.log("开始登录:",{phone:e});const{data:n,error:r}=await Cw().from("users").select("id, phone, password_hash, nickname, avatar_url, gender").eq("phone",e).single();if(r)return"PGRST116"===r.code?{success:!1,error:"用户不存在"}:(console.error("查找用户失败:",r),{success:!1,error:"登录失败"});const s=mS(t);if(n.password_hash!==s)return{success:!1,error:"密码错误"};const o=yS(),i=new Date(Date.now()+6048e5),{data:a,error:l}=await Cw().from("user_sessions").insert({user_id:n.id,token:o,expires_at:i}).select().single();return l?(console.error("创建会话失败:",l),{success:!1,error:"登录失败"}):(delete n.password_hash,{success:!0,data:{user:{id:n.id,phone:n.phone,nickname:n.nickname,gender:n.gender,avatar_url:n.avatar_url},token:o},message:"登录成功"})}catch(r){return console.error("登录失败:",r),{success:!1,error:r.message||"登录失败，请重试"}}})(e);return t.success&&t.data?(c(t.data),t):(r.value=t.error||"登录失败",t)}catch(t){return r.value=t.message||"登录失败",console.error("登录失败:",t),{success:!1,error:t.message||"登录失败"}}finally{n.value=!1}},logout:async()=>{n.value=!0,r.value=null;try{await(async e=>{try{return e&&await Cw().from("user_sessions").delete().eq("token",e),{success:!0,message:"退出成功"}}catch(r){return console.error("退出登录失败:",r),{success:!1,error:r.message||"退出失败"}}})(t.value),u()}catch(e){r.value=e.message||"退出失败",console.error("退出失败:",e)}finally{n.value=!1}},validateSession:h,getCurrentUser:async()=>{try{return s.value||await h(),e.value}catch(t){return console.error("获取当前用户失败:",t),null}}}}),bS=e=>(t,n=Ho())=>{!Yo&&cs(e,t,n)},wS=bS(se),xS=bS(oe),SS=bS(ie),kS=bS(he),TS=bS(de),ES=bS(_e),CS=bS(be),OS=Vr({__name:"App",setup:e=>(SS(async()=>{console.log("App Launch");const e=_S();try{await e.validateSession()}catch(t){console.error("应用启动时验证会话失败:",t)}}),wS(()=>{console.log("App Show")}),xS(()=>{console.log("App Hide")}),TS(()=>{_S()}),(e,t)=>{const n=_r("router-view"),r=Nf;return _o(),ko(r,{class:"app"},{default:fr(()=>[$o(n)]),_:1})})});eg(OS,{init:Zp,setup(e){const t=Gf(),n=()=>{var n;n=e,Object.keys(Xh).forEach(e=>{Xh[e].forEach(t=>{cs(e,t,n)})});const{onLaunch:r,onShow:s,onPageNotFound:o,onError:i}=e,a=function({path:e,query:t}){return c(_d,{path:e,query:t}),c(bd,_d),c({},_d)}({path:t.path.slice(1)||__uniRoutes[0].meta.route,query:qe(t.query)});if(r&&I(r,a),s&&I(s,a),!t.matched.length){const e={notFound:!0,openType:"appLaunch",path:t.path,query:{},scene:1001};o&&I(o,e)}i&&(e.appContext.config.errorHandler=e=>{I(i,e)})};return Vs(xl).isReady().then(n),ds(()=>{window.addEventListener("resize",Ke(ng,50,{setTimeout:setTimeout,clearTimeout:clearTimeout})),window.addEventListener("message",rg),document.addEventListener("visibilitychange",sg),function(){let e=null;try{e=window.matchMedia("(prefers-color-scheme: dark)")}catch(t){}if(e){let t=e=>{Bv.emit(le,{theme:e.matches?"dark":"light"})};e.addEventListener?e.addEventListener("change",t):e.addListener(t)}}()}),t.query},before(e){e.mpType="app";const{setup:t}=e,n=()=>(_o(),ko($v));e.setup=(e,r)=>{const s=t&&t(e,r);return v(s)?n:s},e.render=n}});const PS=function(){const e=lt(!0),t=e.run(()=>$n({}));let n=[],r=[];const s=kn({install(e){ym(s),s._a=e,e.provide(_m,s),e.config.globalProperties.$pinia=s,r.forEach(e=>n.push(e)),r=[]},use(e){return this._a?n.push(e):r.push(e),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return s}();(function(){const e=Qi(OS);return e.use(PS),{app:e}})().app.use(Hp).mount("#app");export{Wh as $,kS as A,Cw as B,rv as C,wS as D,yv as E,fo as F,wg as G,CS as H,ES as I,xg as J,_S as K,Xd as L,Rn as M,zg as N,gv as O,Fg as P,np as Q,_g as R,Cf as S,ip as T,Ug as U,sp as V,Tg as W,Df as X,qh as Y,ov as Z,t as _,$o as a,iv as a0,Ow as a1,So as b,ko as c,Be as d,If as e,Bo as f,xs as g,$n as h,Nf as i,ei as j,Or as k,ds as l,_r as m,Ie as n,_o as o,Io as p,Br as q,ws as r,Od as s,X as t,Vr as u,Si as v,fr as w,Ao as x,Ji as y,Vu as z};
