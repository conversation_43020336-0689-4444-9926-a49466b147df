import{c as a,w as s,N as e,C as t,T as o,i as l,o as r,a as n,e as i,f as u,s as c,t as d,z as g}from"./index-D2g-qjTN.js";import{a as f}from"./auth-manager.Cl_11pDL.js";import{g as m}from"./avatar.DQyLU1U7.js";import{_ as p}from"./_plugin-vue_export-helper.BCo6x5W8.js";const _=p({name:"ProfilePage",computed:{isLoggedIn:()=>f.isLoggedIn(),username:()=>f.getUsername(),userId:()=>f.getUserId(),userAvatar:()=>m(f.getAvatarUrl(),f.getUsername())},methods:{async handleLogout(){try{await this.authStore.logout(),t({title:"已退出登录",icon:"success"}),setTimeout(()=>{o({url:"/pages/login/login"})},1500)}catch(a){t({title:"退出失败",icon:"none"})}},goToLogin(){e({url:"/pages/login/login"})}}},[["render",function(e,t,o,f,m,p){const _=i,h=l,I=c,k=g;return r(),a(h,{class:"profile-container"},{default:s(()=>[n(h,{class:"header"},{default:s(()=>[n(_,{class:"title"},{default:s(()=>[u("我的")]),_:1})]),_:1}),n(h,{class:"content"},{default:s(()=>[p.isLoggedIn?(r(),a(h,{key:0,class:"user-info"},{default:s(()=>[n(I,{src:p.userAvatar,class:"avatar",mode:"aspectFit"},null,8,["src"]),n(_,{class:"username"},{default:s(()=>[u(d(p.username),1)]),_:1}),n(_,{class:"user-id"},{default:s(()=>[u("ID: "+d(p.userId),1)]),_:1}),n(k,{class:"logout-btn",onClick:p.handleLogout},{default:s(()=>[u(" 退出登录 ")]),_:1},8,["onClick"])]),_:1})):(r(),a(h,{key:1,class:"login-prompt"},{default:s(()=>[n(_,{class:"prompt-text"},{default:s(()=>[u("请先登录")]),_:1}),n(k,{class:"login-btn",onClick:p.goToLogin},{default:s(()=>[u(" 立即登录 ")]),_:1},8,["onClick"])]),_:1}))]),_:1})]),_:1})}],["__scopeId","data-v-0a7b78dd"]]);export{_ as default};
