import{c as t,w as a,P as s,U as e,Y as o,Q as n,i as c,C as i,O as l,o as r,a as u,p as m,s as d,e as h,f as p,t as f,b as _,F as D,r as g,n as k,L as P,z as C}from"./index-D2g-qjTN.js";import{w,f as v,a as T}from"./time-utils.BUXFJoP1.js";import{P as S}from"./storage-manager.CvuXSRsG.js";import{a as I}from"./post.DjEMR5TD.js";import{_ as b}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./auth-manager.Cl_11pDL.js";import"./avatar.DQyLU1U7.js";const E=b({name:"PostDetailPage",data:()=>({postId:"",postData:{id:"",content:"",images:[],created_at:"",username:"",avatar_url:"",like_count:0,comment_count:0,share_count:0,liked:!1},commentContent:"",comments:[],touchStartX:0,touchStartY:0,minSwipeDistance:50}),onLoad(t){t.id&&(this.postId=t.id,this.loadPostData(this.postId))},methods:{handleTouchStart(t){const a=t.touches[0];this.touchStartX=a.clientX,this.touchStartY=a.clientY},handleTouchEnd(t){const a=t.changedTouches[0],s=a.clientX-this.touchStartX,e=a.clientY-this.touchStartY;s>this.minSwipeDistance&&Math.abs(e)<.5*Math.abs(s)&&this.navigateToProfile()},navigateToProfile(){n({url:"/pages/profile/profile"})},goBack(){o("returnFromDetail"),e()},loadPostData(t){try{const a=S.getPostById(t);a?(this.postData={...a},console.log("加载帖子成功:",this.postData)):(console.error("未找到ID为"+t+"的帖子"),this.showPostNotFoundError())}catch(a){console.error("加载帖子数据失败:",a),this.showPostNotFoundError()}},showPostNotFoundError(){T(()=>Promise.reject(new Error("帖子不存在")),{showLoading:!1,handleError:!1,showError:!0,errorMessage:"帖子不存在"}),setTimeout(()=>{e()},1500)},formatTime:t=>v(t),likePost:w(function(){const t=this.postData.liked,a=this.postData.like_count;this.postData.liked=!this.postData.liked,this.postData.like_count=this.postData.liked?a+1:a-1,S.updatePost(this.postData),T(()=>I(this.postId,this.postData.liked),{showLoading:!1,handleError:!1,showError:!1}).then(s=>{s.success||(this.postData.liked=t,this.postData.like_count=a,S.updatePost(this.postData))})},{customMessage:"登录后才能点赞哦"}),commentPost:w(function(){},{customMessage:"登录后才能评论哦"}),sharePost:w(function(){l({itemList:["分享到微信","分享到QQ","分享到微博","复制链接"],success:t=>{this.postData.share_count=(this.postData.share_count||0)+1,S.updatePost(this.postData);i({title:`已分享到${["微信","QQ","微博","链接"][t.tapIndex]}`,icon:"success"})}})}),sendComment:w(function(){if(!this.commentContent.trim())return;const t={id:Date.now().toString(),content:this.commentContent,username:"当前用户",avatar_url:"",created_at:(new Date).toISOString()};this.comments.unshift(t),this.commentContent="",this.postData.comment_count=(this.postData.comment_count||0)+1,S.updatePost(this.postData),i({title:"评论成功",icon:"success"})},{customMessage:"登录后才能评论哦"}),previewImage(t){const{images:a,index:e}=t;s({urls:a,current:a[e]})}}},[["render",function(s,e,o,n,i,l){const w=d,v=h,T=c,S=P,I=C;return r(),t(T,{class:"post-detail-container",onTouchstart:l.handleTouchStart,onTouchend:l.handleTouchEnd},{default:a(()=>[u(T,{class:"post-content"},{default:a(()=>[u(T,{class:"user-info"},{default:a(()=>[u(w,{src:i.postData.avatar_url||"/static/default-avatar.png",class:"user-avatar",mode:"aspectFit"},null,8,["src"]),u(T,{class:"user-details"},{default:a(()=>[u(v,{class:"username"},{default:a(()=>[p(f(i.postData.username),1)]),_:1}),u(v,{class:"time"},{default:a(()=>[p(f(l.formatTime(i.postData.created_at)),1)]),_:1})]),_:1})]),_:1}),u(T,{class:"content-text"},{default:a(()=>[u(v,null,{default:a(()=>[p(f(i.postData.content),1)]),_:1})]),_:1}),i.postData.images&&i.postData.images.length>0?(r(),t(T,{key:0,class:"images-container"},{default:a(()=>[(r(!0),_(D,null,g(i.postData.images,(a,s)=>(r(),t(w,{key:s,src:a,class:"content-image",mode:"aspectFill",onClick:t=>l.previewImage({images:i.postData.images,index:s})},null,8,["src","onClick"]))),128))]),_:1})):m("",!0),u(T,{class:"post-actions"},{default:a(()=>[u(T,{class:"action-btn",onClick:l.likePost},{default:a(()=>[u(v,{class:k(["action-icon",{liked:i.postData.liked}])},{default:a(()=>[p("❤️")]),_:1},8,["class"]),u(v,{class:"action-count"},{default:a(()=>[p(f(i.postData.like_count||0),1)]),_:1})]),_:1},8,["onClick"]),u(T,{class:"action-btn",onClick:l.commentPost},{default:a(()=>[u(v,{class:"action-icon"},{default:a(()=>[p("💬")]),_:1}),u(v,{class:"action-count"},{default:a(()=>[p(f(i.postData.comment_count||0),1)]),_:1})]),_:1},8,["onClick"]),u(T,{class:"action-btn",onClick:l.sharePost},{default:a(()=>[u(v,{class:"action-icon"},{default:a(()=>[p("🔗")]),_:1}),u(v,{class:"action-count"},{default:a(()=>[p(f(i.postData.share_count||0),1)]),_:1})]),_:1},8,["onClick"])]),_:1})]),_:1}),u(T,{class:"comments-section"},{default:a(()=>[u(T,{class:"section-title"},{default:a(()=>[p("评论")]),_:1}),u(T,{class:"comment-input-area"},{default:a(()=>[u(S,{type:"text",placeholder:"添加评论...",class:"comment-input",modelValue:i.commentContent,"onUpdate:modelValue":e[0]||(e[0]=t=>i.commentContent=t)},null,8,["modelValue"]),u(I,{class:"send-btn",disabled:!i.commentContent.trim(),onClick:l.sendComment},{default:a(()=>[p(" 发送 ")]),_:1},8,["disabled","onClick"])]),_:1}),u(T,{class:"comments-list"},{default:a(()=>[(r(!0),_(D,null,g(i.comments,(s,e)=>(r(),t(T,{key:e,class:"comment-item"},{default:a(()=>[u(w,{src:s.avatar_url||"/static/default-avatar.png",class:"comment-avatar",mode:"aspectFit"},null,8,["src"]),u(T,{class:"comment-content"},{default:a(()=>[u(T,{class:"comment-user"},{default:a(()=>[p(f(s.username),1)]),_:2},1024),u(T,{class:"comment-text"},{default:a(()=>[p(f(s.content),1)]),_:2},1024),u(T,{class:"comment-time"},{default:a(()=>[p(f(l.formatTime(s.created_at)),1)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1})]),_:1},8,["onTouchstart","onTouchend"])}],["__scopeId","data-v-7273caeb"]]);export{E as default};
