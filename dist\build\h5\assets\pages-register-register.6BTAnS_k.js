function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["assets/utils-form-error-handler.C7UvW1-4.js","assets/index-D2g-qjTN.js","assets/index-CJZafukL.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
import{c as a,w as e,U as l,_ as t,C as r,Q as s,K as o,i as n,o as d,a as i,e as u,f as c,p as f,L as m,n as p,t as _,z as g}from"./index-D2g-qjTN.js";import{_ as h}from"./_plugin-vue_export-helper.BCo6x5W8.js";const k=h({name:"RegisterPage",data:()=>({formData:{phone:"",password:"",nickname:"",gender:0},loading:!1,error:""}),computed:{authStore:()=>o(),canSubmit(){return!this.validateForm()}},methods:{async handleRegister(){const a=this.validateForm();if(a)this.error=a;else{this.loading=!0,this.error="";try{const{FormErrorHandler:a}=await t(()=>import("./utils-form-error-handler.C7UvW1-4.js"),__vite__mapDeps([0,1,2])),e=await this.authStore.registerWithPhone(this.formData);if(e&&e.success)r({title:"注册成功",icon:"success",duration:1500}),setTimeout(()=>{s({url:"/pages/index/index"})},1500);else{const l=a.handleError((null==e?void 0:e.error)||"注册失败，请重试",{showToast:!1});this.error=l}}catch(e){const a=FormErrorHandler.handleError(e,{showToast:!1});this.error=a}finally{this.loading=!1}}},validateForm(){const{FormErrorHandler:a}=require("@/utils/form-error-handler");return a.validateRegisterForm(this.formData)},goToLogin(){l()}}},[["render",function(l,t,r,s,o,h){const k=u,D=n,b=m,V=g;return d(),a(D,{class:"register-container"},{default:e(()=>[i(D,{class:"header"},{default:e(()=>[i(k,{class:"title"},{default:e(()=>[c("注册账号")]),_:1}),i(k,{class:"subtitle"},{default:e(()=>[c("加入Biu，开始分享生活")]),_:1})]),_:1}),i(D,{class:"form-container"},{default:e(()=>[i(D,{class:"input-group"},{default:e(()=>[i(k,{class:"label"},{default:e(()=>[c("手机号")]),_:1}),i(b,{modelValue:o.formData.phone,"onUpdate:modelValue":t[0]||(t[0]=a=>o.formData.phone=a),type:"text",placeholder:"请输入手机号",class:"form-input"},null,8,["modelValue"])]),_:1}),i(D,{class:"input-group"},{default:e(()=>[i(k,{class:"label"},{default:e(()=>[c("密码")]),_:1}),i(b,{modelValue:o.formData.password,"onUpdate:modelValue":t[1]||(t[1]=a=>o.formData.password=a),type:"password",placeholder:"请输入密码（至少6位）",class:"form-input"},null,8,["modelValue"])]),_:1}),i(D,{class:"input-group"},{default:e(()=>[i(k,{class:"label"},{default:e(()=>[c("昵称")]),_:1}),i(b,{modelValue:o.formData.nickname,"onUpdate:modelValue":t[2]||(t[2]=a=>o.formData.nickname=a),type:"text",placeholder:"请输入昵称",class:"form-input"},null,8,["modelValue"])]),_:1}),i(D,{class:"input-group"},{default:e(()=>[i(k,{class:"label"},{default:e(()=>[c("性别")]),_:1}),i(D,{class:"gender-options"},{default:e(()=>[i(D,{class:p(["gender-option",{active:1===o.formData.gender}]),onClick:t[3]||(t[3]=a=>o.formData.gender=1)},{default:e(()=>[i(k,null,{default:e(()=>[c("男")]),_:1})]),_:1},8,["class"]),i(D,{class:p(["gender-option",{active:2===o.formData.gender}]),onClick:t[4]||(t[4]=a=>o.formData.gender=2)},{default:e(()=>[i(k,null,{default:e(()=>[c("女")]),_:1})]),_:1},8,["class"])]),_:1})]),_:1}),o.error?(d(),a(D,{key:0,class:"error-message"},{default:e(()=>[i(k,null,{default:e(()=>[c(_(o.error),1)]),_:1})]),_:1})):f("",!0),i(V,{class:"register-btn",disabled:o.loading||!h.canSubmit,onClick:h.handleRegister},{default:e(()=>[o.loading?(d(),a(k,{key:0},{default:e(()=>[c("注册中...")]),_:1})):(d(),a(k,{key:1},{default:e(()=>[c("注册")]),_:1}))]),_:1},8,["disabled","onClick"]),i(D,{class:"login-link"},{default:e(()=>[i(k,{class:"link-text"},{default:e(()=>[c("已有账号？")]),_:1}),i(k,{class:"link-btn",onClick:h.goToLogin},{default:e(()=>[c("立即登录")]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1})}],["__scopeId","data-v-8dd30a04"]]);export{k as default};
