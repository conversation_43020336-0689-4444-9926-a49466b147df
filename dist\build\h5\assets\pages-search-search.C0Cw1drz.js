import{c as e,w as a,i as s,o as t,a as r,L as l,z as c,f as h,b as d,F as o,r as n,e as u,t as i}from"./index-D2g-qjTN.js";import{_ as f}from"./_plugin-vue_export-helper.BCo6x5W8.js";const m=f({name:"SearchPage",data:()=>({searchKeyword:"",searchResults:[]}),methods:{handleSearch(){this.searchKeyword.trim()&&(this.searchResults=[`搜索结果1: ${this.searchKeyword}`,`搜索结果2: ${this.searchKeyword}`,`搜索结果3: ${this.searchKeyword}`])}}},[["render",function(f,m,p,y,_,w){const K=l,k=c,x=s,C=u;return t(),e(x,{class:"search-container"},{default:a(()=>[r(x,{class:"search-header"},{default:a(()=>[r(K,{type:"text",placeholder:"搜索内容或用户",class:"search-input",modelValue:_.searchKeyword,"onUpdate:modelValue":m[0]||(m[0]=e=>_.searchKeyword=e),onConfirm:w.handleSearch},null,8,["modelValue","onConfirm"]),r(k,{class:"search-btn",onClick:w.handleSearch},{default:a(()=>[h("搜索")]),_:1},8,["onClick"])]),_:1}),_.searchResults.length>0?(t(),e(x,{key:0,class:"search-results"},{default:a(()=>[(t(!0),d(o,null,n(_.searchResults,(s,l)=>(t(),e(x,{key:l,class:"result-item"},{default:a(()=>[r(C,null,{default:a(()=>[h(i(s),1)]),_:2},1024)]),_:2},1024))),128))]),_:1})):(t(),e(x,{key:1,class:"empty-state"},{default:a(()=>[r(C,{class:"empty-text"},{default:a(()=>[h("请输入关键词搜索")]),_:1})]),_:1}))]),_:1})}],["__scopeId","data-v-b5023558"]]);export{m as default};
