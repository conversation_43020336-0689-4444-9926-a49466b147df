import{K as t}from"./index-D2g-qjTN.js";var e=Object.defineProperty,r=(t,r,o)=>(((t,r,o)=>{r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[r]=o})(t,r+"",o),o);const o=new class{constructor(){r(this,"authStore",null)}getAuthStore(){return this.authStore||(this.authStore=t()),this.authStore}isLoggedIn(){return this.getAuthStore().isLoggedIn}async getCurrentUser(){const t=this.getAuthStore();return await t.getCurrentUser()}async validateSession(){const t=this.getAuthStore();return await t.validateSession()}async login(t){const e=this.getAuthStore();return await e.login(t)}async register(t){const e=this.getAuthStore();return await e.registerWithPhone(t)}async logout(){const t=this.getAuthStore();await t.logout()}getUserId(){return this.getAuthStore().userId}getUsername(){return this.getAuthStore().username}getAvatarUrl(){return this.getAuthStore().avatarUrl}getToken(){return this.getAuthStore().token}isLoading(){return this.getAuthStore().isLoading}getError(){return this.getAuthStore().error}clearError(){this.getAuthStore().error=null}};export{o as a};
