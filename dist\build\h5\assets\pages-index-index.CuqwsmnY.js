import{c as e,o as t,w as a,a as l,i as s,n as i,b as o,F as n,r,d as c,e as u,f as d,t as m,g as v,h as f,j as h,k as p,l as g,m as _,p as y,q as k,s as b,v as x,u as w,x as C,y as S,z as T,A as I,B as L,C as H,D as M,E as $,G as j,$ as z,H as N,I as P,J as F,K as R,L as Y,M as E,S as D,N as Q,O as W,P as A,Q as V,R as q}from"./index-D2g-qjTN.js";import{g as B,l as U}from"./post.DjEMR5TD.js";import{P as X}from"./storage-manager.CvuXSRsG.js";import{a as G}from"./auth-manager.Cl_11pDL.js";import{f as J,a as K,w as O}from"./time-utils.BUXFJoP1.js";import{_ as Z}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./avatar.DQyLU1U7.js";const ee=Z({__name:"LoadingSpinner",props:{text:{type:String,default:"加载中..."},size:{type:String,default:"normal"}},setup:v=>(f,h)=>{const p=s,g=u;return t(),e(p,{class:"loading-container"},{default:a(()=>[l(p,{class:i(["spinner",[v.size]])},{default:a(()=>[(t(),o(n,null,r(8,e=>l(p,{class:"spinner-item",key:e,style:c({transform:`rotate(${45*e}deg)`})},{default:a(()=>[l(p,{class:"spinner-dot",style:c({animationDelay:.1*e+"s"})},null,8,["style"])]),_:2},1032,["style"])),64))]),_:1},8,["class"]),l(g,{class:"loading-text"},{default:a(()=>[d(m(v.text),1)]),_:1})]),_:1})}},[["__scopeId","data-v-ebc9152e"]]);const te=Z({name:"VirtualList",props:{items:{type:Array,required:!0},itemHeight:{type:Number,default:200},containerHeight:{type:Number,default:600},initialScrollTop:{type:Number,default:0}},data:()=>({visibleCount:0,offsetY:0}),computed:{totalHeight(){return this.items.length*this.itemHeight},visibleStart(){return Math.floor(this.offsetY/this.itemHeight)},visibleEnd(){return this.visibleStart+this.visibleCount},visibleData(){return this.items.slice(this.visibleStart,this.visibleEnd)}},mounted(){this.visibleCount=Math.ceil(this.containerHeight/this.itemHeight),this.initialScrollTop>0&&(this.offsetY=this.initialScrollTop,this.$nextTick(()=>{this.scrollToPosition(this.initialScrollTop)}))},methods:{handleScroll(e){const{scrollTop:t}=e.detail;this.offsetY=t,this.$emit("scroll",e)},scrollToPosition(e){this.offsetY=e}}},[["render",function(i,u,d,m,f,h){const p=s;return t(),e(p,{class:"virtual-list",onScroll:h.handleScroll,style:c({height:d.containerHeight+"px"})},{default:a(()=>[l(p,{class:"virtual-list-phantom",style:c({height:h.totalHeight+"px"})},null,8,["style"]),l(p,{class:"virtual-list-content",style:c({transform:`translate3d(0,${f.offsetY}px,0)`})},{default:a(()=>[(t(!0),o(n,null,r(h.visibleData,l=>(t(),e(p,{key:l.id,"data-id":l.id,class:"virtual-list-item",style:c({height:d.itemHeight+"px"})},{default:a(()=>[v(i.$slots,"default",{item:l},void 0,!0)]),_:2},1032,["data-id","style"]))),128))]),_:3},8,["style"])]),_:3},8,["onScroll","style"])}],["__scopeId","data-v-da14a5fb"]]),ae=Z({__name:"LazyImage",props:{src:{type:String,required:!0},placeholder:{type:String,default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},borderRadius:{type:[String,Number],default:0},immediate:{type:Boolean,default:!1},threshold:{type:Number,default:100}},emits:["load","error","click"],setup(i,{emit:o}){const n=i,r=o,m=f(!1),v=f(!1),w=f(!1),C=f(""),S=h(()=>({width:"number"==typeof n.width?`${n.width}rpx`:n.width,height:"number"==typeof n.height?`${n.height}rpx`:n.height,borderRadius:"number"==typeof n.borderRadius?`${n.borderRadius}rpx`:n.borderRadius,overflow:"hidden",position:"relative"})),T=h(()=>({width:"100%",height:"100%",display:"block"})),I=h(()=>({width:"100%",height:"100%",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"#f5f5f5",position:"absolute",top:0,left:0})),L=()=>{m.value=!0,v.value=!1,r("load")},H=()=>{if(n.src&&n.src.startsWith("blob:"))return m.value=!1,void r("error");m.value=!1,v.value=!0,r("error")},M=e=>{r("click",e)},$=()=>{!C.value&&n.src&&(n.src.startsWith("blob:")||n.src.startsWith("http")||n.src.startsWith("/")||n.src.startsWith("data:"),C.value=n.src)};return p(()=>n.src,e=>{e&&(m.value=!1,v.value=!1,C.value="",(w.value||n.immediate)&&$())}),g(()=>{(()=>{if(n.immediate)return w.value=!0,void $();w.value=!0,$()})()}),(o,n)=>{const r=_("uni-icons"),f=u,h=s,p=b;return t(),e(h,{class:"lazy-image-container",style:c(S.value)},{default:a(()=>[m.value||v.value?y("",!0):(t(),e(h,{key:0,class:"placeholder",style:c(I.value)},{default:a(()=>[l(h,{class:"placeholder-content"},{default:a(()=>[l(r,{type:"image",size:"40",color:"#ccc"}),l(f,{class:"placeholder-text"},{default:a(()=>[d("加载中...")]),_:1})]),_:1})]),_:1},8,["style"])),k(l(p,{src:C.value,mode:i.mode,"lazy-load":!0,"fade-show":!0,class:"lazy-image",style:c(T.value),onLoad:L,onError:H,onClick:M},null,8,["src","mode","style"]),[[x,m.value&&!v.value]]),v.value?(t(),e(h,{key:1,class:"error-placeholder",style:c(I.value)},{default:a(()=>[l(h,{class:"error-content"},{default:a(()=>[l(r,{type:"close",size:"40",color:"#999"}),l(f,{class:"error-text"},{default:a(()=>[d("加载失败")]),_:1})]),_:1})]),_:1},8,["style"])):y("",!0)]),_:1},8,["style"])}}},[["__scopeId","data-v-d908c289"]]),le=Z(w({__name:"FeedItem",props:{item:{},formatTime:{type:Function,default:void 0}},emits:["click","more","like","comment","share","imagePreview"],setup(c,{emit:v}){const f=c,h=v,p=()=>{h("click",f.item)},g=()=>{h("more",f.item)},_=()=>{h("like",f.item)},k=()=>{h("comment",f.item)},x=()=>{h("share",f.item)};return(c,v)=>{const w=b,I=u,L=s,H=T;return t(),e(L,{class:"social-card",onClick:p},{default:a(()=>[l(L,{class:"card-inner"},{default:a(()=>[l(w,{src:c.item.avatar_url||"/static/default-avatar.png",class:"avatar",mode:"aspectFit"},null,8,["src"]),l(L,{class:"content-area"},{default:a(()=>[l(L,{class:"header-info"},{default:a(()=>{return[C("div",{class:"user-info"},[C("span",{class:"username"},m(c.item.username),1),C("span",{class:"timestamp"},m((e=c.item.created_at,f.formatTime?f.formatTime(e):J(e))),1)]),l(L,{class:"more-btn",onClick:S(g,["stop"])},{default:a(()=>[l(I,null,{default:a(()=>[d("⋯")]),_:1})]),_:1})];var e}),_:1}),c.item.content?(t(),e(L,{key:0,class:"content-text"},{default:a(()=>[l(I,null,{default:a(()=>[d(m(c.item.content),1)]),_:1})]),_:1})):y("",!0),c.item.location?(t(),e(L,{key:1,class:"location"},{default:a(()=>[l(I,{class:"location-text"},{default:a(()=>[d("📍 "+m(c.item.location),1)]),_:1})]),_:1})):y("",!0)]),_:1}),c.item.images&&c.item.images.length>0?(t(),e(L,{key:0,class:"media-container"},{default:a(()=>[(t(!0),o(n,null,r(c.item.images,(a,l)=>(t(),e(ae,{key:l,src:a,class:"media",mode:"aspectFill",onClick:S(e=>{return t={images:c.item.images,index:l},void h("imagePreview",t);var t},["stop"])},null,8,["src","onClick"]))),128))]),_:1})):y("",!0),C("div",{class:"actions-container"},[l(H,{class:"action-btn like-btn",onClick:S(_,["stop"])},{default:a(()=>[l(I,{class:i(["action-icon",{liked:c.item.liked}])},{default:a(()=>[d("❤️")]),_:1},8,["class"]),l(I,{class:"action-text"},{default:a(()=>[d(m(c.item.likes||0),1)]),_:1})]),_:1}),l(H,{class:"action-btn comment-btn",onClick:S(k,["stop"])},{default:a(()=>[l(I,{class:"action-icon"},{default:a(()=>[d("💬")]),_:1}),l(I,{class:"action-text"},{default:a(()=>[d(m(c.item.comments||0),1)]),_:1})]),_:1}),l(H,{class:"action-btn share-btn",onClick:S(x,["stop"])},{default:a(()=>[l(I,{class:"action-icon"},{default:a(()=>[d("↗")]),_:1}),l(I,{class:"action-text"},{default:a(()=>[d("分享")]),_:1})]),_:1})])]),_:1})]),_:1})}}}),[["__scopeId","data-v-acf5ba9b"]]),se=Z(w({__name:"index",setup(c){const v=f(""),p=f(0),_=f(0),k=f(!1),x=f(!0),w=f(0),C=f(0),S=f(50),T=f(1),Z=f(10),ae=f(null),se=f(3e3),ie=f([]),oe=f(null),ne=f(0),re=[{id:"recommend",name:"推荐"},{id:"following",name:"关注"},{id:"hot",name:"热门"},{id:"local",name:"附近"},{id:"interest",name:"兴趣"}],ce=h(()=>R().isLoggedIn),ue=h(()=>{const e=R().user;return(null==e?void 0:e.avatar_url)||"/static/default-avatar.png"}),de=h(()=>{if(!Array.isArray(ie.value))return[];const e=re[p.value].id;return"recommend"===e?ie.value:ie.value.filter(t=>t.tag===e)}),me=e=>J(e),ve=e=>{const t=e.touches[0];w.value=t.clientX,C.value=t.clientY},fe=e=>{const t=e.changedTouches[0],a=t.clientX-w.value,l=t.clientY-C.value;a>S.value&&Math.abs(l)<.5*Math.abs(a)&&Se()},he=e=>{e&&e.detail&&void 0!==e.detail.scrollTop&&(ne.value=e.detail.scrollTop)};I(async()=>{console.log("📱 首页加载中...");try{await G.validateSession()}catch(e){console.error("验证会话失败:",e)}console.log("🔍 开始数据库连接测试...");try{const e=L(),{data:t,error:a}=await e.from("posts").select("*").limit(1);a?(console.error("❌ 数据库连接失败:",a),H({title:"数据库连接异常",icon:"error",duration:3e3})):(console.log(`✅ 数据库连接正常，找到 ${(null==t?void 0:t.length)||0} 条数据`),console.log("🔄 加载首页内容..."),await pe(!0))}catch(e){console.error("❌ 数据库测试异常:",e),H({title:"数据加载失败",icon:"error",duration:3e3})}}),M(async()=>{console.log("📱 首页显示，检查是否需要刷新内容...");X.hasNewContent()?(console.log("🔄 检测到新内容或用户请求刷新，刷新列表..."),X.clearNewContentFlag(),await pe(!0),H({title:"内容已更新",icon:"success",duration:1500})):ie.value&&0!==ie.value.length||await pe(!0)});const pe=async(e=!1)=>{var t;if(!k.value){k.value=!0,e&&(T.value=1,x.value=!0,ie.value=[]),console.log("🔄 开始获取内容列表...");try{const a=null==(t=re[p.value])?void 0:t.id,l=await K(()=>B(T.value,Z.value,"recommend"===a?void 0:a),{showLoading:0===ie.value.length,showError:!0,customErrorMessage:"获取数据失败，请检查网络连接"});if(l.success&&l.data){const t=l.data,a=t.posts||[],s=t.hasMore;e?ie.value=a:ie.value.push(...a),x.value=s,a.length>0&&T.value++,X.setPosts(ie.value)}}catch(a){console.error("获取内容失败:",a)}finally{k.value=!1,$()}}},ge=()=>{v.value.trim()&&Q({url:`/pages/search/search?keyword=${encodeURIComponent(v.value)}`})},_e=O(e=>{const t=e.liked,a=e.like_count;e.liked=!e.liked,e.like_count=e.liked?a+1:a-1;const l=ie.value.findIndex(t=>t.id===e.id);-1!==l&&(ie.value[l]={...e}),K(()=>U(e.id,e.liked),{showLoading:!1,showError:!1}).then(l=>{if(!l.success){e.liked=t,e.like_count=a;const l=ie.value.findIndex(t=>t.id===e.id);-1!==l&&(ie.value[l]={...e})}})},{customMessage:"登录后才能点赞哦"}),ye=O(e=>{H({title:"评论功能开发中",icon:"none"})},{customMessage:"登录后才能评论哦"}),ke=O(e=>{W({itemList:["分享到微信","分享到QQ","分享到微博","复制链接"],success:t=>{e.share_count=(e.share_count||0)+1;const a=ie.value.findIndex(t=>t.id===e.id);-1!==a&&(ie.value[a]={...e}),X.updatePost(e.id,e);H({title:`已分享到${["微信","QQ","微博","链接"][t.tapIndex]}`,icon:"success"})}})}),be=O(()=>{Q({url:"/pages/create-post/create-post"})},{customMessage:"登录后才能发布内容哦"}),xe=e=>{(()=>{const e=ne.value;void 0!==e&&(q("feed_virtual_scroll_top",e),console.log("保存虚拟滚动位置:",e))})(),Q({url:`/pages/post-detail/post-detail?id=${e.id}`})},we=e=>{W({itemList:["举报","不感兴趣","关注作者"],success:e=>{switch(e.tapIndex){case 0:H({title:"举报成功",icon:"success"});break;case 1:H({title:"已减少类似内容",icon:"success"});break;case 2:H({title:"关注成功",icon:"success"})}}})},Ce=e=>{const{images:t,index:a}=e;A({urls:t,current:t[a]})},Se=O(()=>{V({url:"/pages/profile/profile"})},{customMessage:"登录后才能查看个人资料哦"});return g(()=>{console.log("🔧 组件挂载完成")}),M(()=>{console.log("🔧 页面显示，检查是否需要恢复滚动位置");const e=j("feed_virtual_scroll_top");void 0!==e&&e>0&&(ne.value=e,console.log("设置虚拟滚动位置:",e)),setTimeout(()=>{F("feed_virtual_scroll_top"),ne.value=0},500)}),g(()=>{z("returnFromDetail",()=>{console.log("收到从详情页返回的事件")})}),N(async()=>{const e=Date.now();if(ae.value&&e-ae.value<se.value)return console.log("⏱️ 刷新过于频繁，请稍后再试"),H({title:"刷新过于频繁，请稍后再试",icon:"none",duration:1500}),void $();ae.value=e,console.log("🔄 用户触发下拉刷新..."),await pe(!0)}),P(()=>{x.value&&!k.value&&pe()}),(c,f)=>{const h=u,g=Y,x=s,w=b,C=D;return t(),e(x,{class:"container",onTouchstart:ve,onTouchend:fe},{default:a(()=>[l(x,{class:"navbar"},{default:a(()=>[l(h,{class:"app-name"},{default:a(()=>[d("Biu")]),_:1}),l(x,{class:"search-container"},{default:a(()=>[l(g,{type:"text",placeholder:"搜索内容或用户",class:"search-input",modelValue:v.value,"onUpdate:modelValue":f[0]||(f[0]=e=>v.value=e),onConfirm:ge},null,8,["modelValue"])]),_:1}),ce.value?(t(),e(x,{key:0,class:"user-avatar",onClick:E(Se)},{default:a(()=>[l(w,{src:ue.value||"/static/default-avatar.png",mode:"aspectFit"},null,8,["src"])]),_:1},8,["onClick"])):y("",!0)]),_:1}),l(x,{class:"tabs-container"},{default:a(()=>[l(C,{"scroll-x":"",class:"tabs-scroll","scroll-left":_.value},{default:a(()=>[(t(),o(n,null,r(re,(e,t)=>l(x,{key:e.id,class:i(["tab-item",{active:p.value===t}]),onClick:e=>(e=>{p.value=e,_.value=100*e,pe(!0)})(t)},{default:a(()=>[d(m(e.name),1)]),_:2},1032,["class","onClick"])),64))]),_:1},8,["scroll-left"])]),_:1}),l(x,{class:"content"},{default:a(()=>[!k.value||ie.value&&0!==ie.value.length?de.value&&de.value.length>0?(t(),e(x,{key:1,class:"feed-list"},{default:a(()=>[de.value.length>20?(t(),e(te,{key:0,ref_key:"virtualListRef",ref:oe,items:de.value,itemHeight:350,containerHeight:600,initialScrollTop:ne.value,onScroll:he},{default:a(({item:e})=>[l(le,{item:e,formatTime:me,onClick:xe,onMore:we,onLike:E(_e),onComment:E(ye),onShare:E(ke),onImagePreview:Ce},null,8,["item","onLike","onComment","onShare"])]),_:1},8,["items","initialScrollTop"])):(t(!0),o(n,{key:1},r(de.value,(a,l)=>(t(),e(le,{key:a.id,item:a,formatTime:me,onClick:xe,onMore:we,onLike:E(_e),onComment:E(ye),onShare:E(ke),onImagePreview:Ce},null,8,["item","onLike","onComment","onShare"]))),128)),k.value&&ie.value&&ie.value.length>0?(t(),e(x,{key:2,class:"loading-more"},{default:a(()=>[l(ee,{text:"加载更多内容...",size:"small"})]),_:1})):y("",!0)]),_:1})):(t(),e(x,{key:2,class:"empty-state"},{default:a(()=>[l(h,{class:"empty-text"},{default:a(()=>[d("暂无内容")]),_:1}),l(h,{class:"empty-hint"},{default:a(()=>[d("快来发布第一条动态吧！")]),_:1})]),_:1})):(t(),e(x,{key:0,class:"loading-container"},{default:a(()=>[l(ee,{text:"正在加载内容..."})]),_:1}))]),_:1}),l(x,{class:"create-btn",onClick:E(be)},{default:a(()=>[l(h,{class:"create-icon"},{default:a(()=>[d("➕")]),_:1})]),_:1},8,["onClick"])]),_:1})}}}),[["__scopeId","data-v-95943f07"]]);export{se as default};
