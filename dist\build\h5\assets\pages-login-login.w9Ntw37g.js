function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["assets/utils-form-error-handler.C7UvW1-4.js","assets/index-D2g-qjTN.js","assets/index-CJZafukL.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
import{c as a,w as e,N as s,Q as l,_ as o,C as r,K as t,i as n,o as i,a as d,e as u,f as c,p as f,L as p,t as m,z as _,n as g}from"./index-D2g-qjTN.js";import{_ as h}from"./_plugin-vue_export-helper.BCo6x5W8.js";const b=h({name:"LoginPage",data:()=>({formData:{phone:"",password:""},loading:!1,error:""}),computed:{authStore:()=>t(),canSubmit(){return!this.validateForm()}},methods:{clearError(){this.error=""},async handleLogin(){const a=this.validateForm();if(a)this.error=a;else{this.loading=!0,this.error="";try{const{FormErrorHandler:a}=await o(()=>import("./utils-form-error-handler.C7UvW1-4.js"),__vite__mapDeps([0,1,2])),e=this.formData.phone.trim(),s=this.formData.password,l=await this.authStore.login({phone:e,password:s});if(l&&l.success)r({title:"登录成功",icon:"success",duration:1500}),setTimeout(()=>{this.handleLoginSuccess()},1500);else{const e=a.handleError((null==l?void 0:l.error)||"手机号或密码错误",{showToast:!1});this.error=e}}catch(e){const a=FormErrorHandler.handleError(e,{showToast:!1});this.error=a}finally{this.loading=!1}}},validateForm(){const{FormErrorHandler:a}=require("@/utils/form-error-handler");return a.validateLoginForm(this.formData)},handleLoginSuccess(){l({url:"/pages/index/index"})},goToRegister(){s({url:"/pages/register/register"})}}},[["render",function(s,l,o,r,t,h){const b=u,x=n,w=p,k=_;return i(),a(x,{class:"login-container"},{default:e(()=>[d(x,{class:"logo-section"},{default:e(()=>[d(x,{class:"logo"},{default:e(()=>[d(b,{class:"logo-text"},{default:e(()=>[c("Biu")]),_:1})]),_:1}),d(b,{class:"app-name"},{default:e(()=>[c("Biu")]),_:1}),d(b,{class:"app-slogan"},{default:e(()=>[c("连接你我，分享生活")]),_:1})]),_:1}),d(x,{class:"form-section"},{default:e(()=>[d(x,{class:"form-container"},{default:e(()=>[d(x,{class:"input-group"},{default:e(()=>[d(x,{class:"input-label"},{default:e(()=>[d(b,{class:"label-icon"},{default:e(()=>[c("📱")]),_:1}),d(b,{class:"label-text"},{default:e(()=>[c("手机号")]),_:1})]),_:1}),d(w,{modelValue:t.formData.phone,"onUpdate:modelValue":l[0]||(l[0]=a=>t.formData.phone=a),type:"text",placeholder:"请输入手机号",class:"form-input",disabled:t.loading,onInput:h.clearError},null,8,["modelValue","disabled","onInput"])]),_:1}),d(x,{class:"input-group"},{default:e(()=>[d(x,{class:"input-label"},{default:e(()=>[d(b,{class:"label-icon"},{default:e(()=>[c("🔒")]),_:1}),d(b,{class:"label-text"},{default:e(()=>[c("密码")]),_:1})]),_:1}),d(w,{modelValue:t.formData.password,"onUpdate:modelValue":l[1]||(l[1]=a=>t.formData.password=a),type:"password",placeholder:"请输入密码",class:"form-input",disabled:t.loading,onInput:h.clearError},null,8,["modelValue","disabled","onInput"])]),_:1}),t.error?(i(),a(x,{key:0,class:"error-message"},{default:e(()=>[d(b,{class:"error-icon"},{default:e(()=>[c("⚠️")]),_:1}),d(b,{class:"error-text"},{default:e(()=>[c(m(t.error),1)]),_:1})]),_:1})):f("",!0),d(k,{class:g(["login-btn",{loading:t.loading}]),disabled:t.loading||!h.canSubmit,onClick:h.handleLogin},{default:e(()=>[t.loading?(i(),a(b,{key:0},{default:e(()=>[c("登录中...")]),_:1})):(i(),a(b,{key:1},{default:e(()=>[c("登录")]),_:1}))]),_:1},8,["class","disabled","onClick"]),d(x,{class:"register-link"},{default:e(()=>[d(b,{class:"link-text"},{default:e(()=>[c("还没有账号？")]),_:1}),d(b,{class:"link-btn",onClick:h.goToRegister},{default:e(()=>[c("立即注册")]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1}),d(x,{class:"footer-decoration"},{default:e(()=>[d(b,{class:"footer-text"},{default:e(()=>[c("让生活更有趣")]),_:1})]),_:1})]),_:1})}],["__scopeId","data-v-8fc9a168"]]);export{b as default};
