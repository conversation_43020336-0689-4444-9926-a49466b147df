# 首页功能实现文档

## 概述

Biu社交应用的首页是用户进入应用后的核心展示页面，用于展示动态内容流、处理用户交互以及提供导航功能。首页集成了内容展示、用户认证、数据加载、交互操作等多种功能。

## 页面结构

首页主要由以下几个部分组成：

1. **顶部导航栏** - 包含应用名称、搜索框和用户头像
2. **内容分类标签栏** - 提供不同内容分类的切换
3. **主内容区域** - 展示动态内容列表
4. **创建按钮** - 快速发布新动态的入口

## 技术架构

```
首页组件 (pages/index/index.vue)
  ↓
认证管理器 (utils/auth-manager.ts)
  ↓
帖子服务 (services/post.ts)
  ↓
Supabase数据库
```

## 核心功能实现

### 1. 用户认证与会话管理

首页在加载时会验证用户会话状态：

```javascript
async onLoad() {
  console.log('📱 首页加载中...')
  
  // 验证会话
  await authManager.validateSession()
  
  // 检查登录状态
  const isLoggedIn = await this.checkAuthStatus()
  
  // 加载内容
  if (isLoggedIn) {
    console.log('🔄 加载首页内容...')
    await this.fetchContentList(true)
  }
}
```

### 2. 内容加载与展示

首页支持两种内容加载模式：
- **真实数据模式** - 从Supabase数据库获取真实数据
- **本地数据模式** - 使用本地存储的模拟数据（当前默认模式）

#### 数据获取流程

1. 根据当前页码和每页数量计算分页参数
2. 调用帖子服务获取数据
3. 处理返回的数据，包括用户信息、点赞数等关联数据
4. 更新UI展示

#### 虚拟列表优化

对于大量数据展示，首页使用虚拟列表组件优化性能：

```vue
<VirtualList
  v-if="filteredFeedItems.length > 20"
  :items="filteredFeedItems"
  :itemHeight="350"
  :containerHeight="600"
  @scroll="handleScroll"
>
  <template #default="{ item }">
    <!-- 动态项组件 -->
  </template>
</VirtualList>
```

### 3. 内容分类与筛选

首页提供多种内容分类标签：
- 推荐
- 关注
- 热门
- 附近
- 兴趣

用户可以通过点击标签切换不同的内容视图。

### 4. 下拉刷新与上拉加载

首页支持下拉刷新和上拉加载更多功能：

```javascript
onPullDownRefresh() {
  if (this.onPullDownRefresh) {
    this.onPullDownRefresh()
  }
},

onReachBottom() {
  if (this.onReachBottom) {
    this.onReachBottom()
  }
}
```

### 5. 动态交互功能

首页支持对动态进行多种交互操作：

#### 点赞功能
- 采用乐观更新策略，先更新UI再发送请求
- 支持点赞和取消点赞
- 实时更新点赞数

#### 评论功能
- 跳转到评论页面（待实现）

#### 分享功能
- 提供多种分享渠道选项

### 6. 搜索功能

首页提供全局搜索入口，支持搜索内容和用户。

### 7. 手势操作

支持右滑手势快速跳转到个人资料页面。

## 数据管理

### 本地数据存储

当前版本主要使用本地存储管理数据：

```javascript
// 存储动态数据
uni.setStorageSync('posts', JSON.stringify(posts))

// 读取动态数据
const localPosts = JSON.parse(uni.getStorageSync('posts') || '[]')
```

### 数据结构

动态数据包含以下字段：
- id: 动态唯一标识
- content: 动态内容文本
- images: 图片URL数组
- location: 位置信息
- created_at: 创建时间
- user_id: 用户ID
- username: 用户名
- avatar_url: 用户头像URL
- like_count: 点赞数
- comment_count: 评论数
- share_count: 分享数
- liked: 当前用户是否已点赞

## 组件依赖

首页依赖以下自定义组件：
- LoadingSpinner - 加载指示器
- LazyImage - 懒加载图片组件
- VirtualList - 虚拟列表组件

## 性能优化

1. **虚拟滚动** - 对于大量数据使用虚拟滚动优化渲染性能
2. **懒加载图片** - 图片按需加载，减少初始加载压力
3. **数据缓存** - 使用本地存储缓存数据，提升加载速度
4. **防抖处理** - 避免重复请求

## 错误处理

系统对以下错误情况进行处理：
- 网络连接异常
- 数据加载失败
- 用户认证失败
- 操作执行失败

所有错误都会向用户显示友好的提示信息，同时在控制台记录详细日志便于调试。

## 代码示例

### 获取内容列表方法
```javascript
async fetchContentList(refresh = false) {
  if (this.loading) return
  
  try {
    this.loading = true
    
    if (refresh) {
      this.page = 1
      this.hasMore = true
      this.feedItems = []
      
      // 每次刷新都重新加载本地数据，确保最新
      await this.loadLocalDataAsFallback(true)
    } else {
      // 加载更多数据
      await this.loadLocalDataAsFallback(false)
    }
    
  } catch (error) {
    console.error('获取内容失败:', error)
    
    uni.showToast({
      title: '加载失败',
      icon: 'none',
      duration: 1500
    })
  } finally {
    this.loading = false
    uni.stopPullDownRefresh()
  }
}
```

### 点赞功能实现
```javascript
async likePost(item) {
  if (!authManager.isLoggedIn()) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }

  const originalLiked = item.liked
  const originalCount = item.like_count

  try {
    // 乐观更新UI
    item.liked = !item.liked
    item.like_count = item.liked ? (originalCount + 1) : (originalCount - 1)

    const feedIndex = this.feedItems.findIndex(feedItem => feedItem.id === item.id)
    if (feedIndex !== -1) {
      this.feedItems[feedIndex] = { ...item }
    }

    // 更新本地存储
    this.updateLocalPostData(item)

  } catch (error) {
    console.error('点赞操作失败:', error)
    
    // 回滚UI状态
    item.liked = originalLiked
    item.like_count = originalCount
    
    const feedIndex = this.feedItems.findIndex(feedItem => feedItem.id === item.id)
    if (feedIndex !== -1) {
      this.feedItems[feedIndex] = { ...item }
    }

    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none'
    })
  }
}
```

## 待办事项

1. 完全切换到真实数据模式，移除本地数据降级方案
2. 实现完整的评论功能
3. 优化附近动态功能
4. 实现关注用户功能
5. 增加个性化推荐算法