# 📄 项目开发规则文档  
**项目名称：Biu生活社交移动端应用（基于 uni-app + Vue3 + TypeScript）**
**适用技术栈：uni-app + Vue3 (Composition API) + TypeScript + Vite + Pinia**

---

## 1. 项目目标
- 使用 uni-app 框架开发跨平台应用（支持微信小程序、H5、App）
- 采用 Vue3 的 Composition API + `<script setup>` 语法
- 全项目启用 TypeScript，提升代码健壮性
- 实现模块化、组件化、可维护性强的前端架构

---

## 2. 技术栈规范

| 类别 | 技术选型 | 版本要求 |
|------|--------|---------|
| 框架 | uni-app (Vue3 版本) | `3.0.0-4020920240930001` |
| Vue 核心 | Vue 3 | `^3.4.21` |
| 构建工具 | Vite | `5.2.8` |
| 语言 | TypeScript | `^5.4.0` |
| 状态管理 | Pinia | `2.0.36` |
| 路由 | uni-app 内置路由 + `uni.navigateTo` 等 API |
| 样式 | SCSS / CSS3 | 支持 `@/styles/variables.scss` 全局引入 |
| UI 组件 | uni-ui | `^1.5.10` |
| 数据加密 | crypto-js | `^4.2.0` |
| 国际化 | vue-i18n | `^9.1.9` |
| 数据库 | supabase-js | `^2.45.1` |

---

## 3. 项目结构规范


---

## 10. 团队协作规范

- 分支管理：`main`（主干）、`dev`（开发）、`feature/xxx`（功能分支）
- Pull Request 必须经过至少 1 人 Code Review
- 每日站会同步进度，Trello / 飞书 / Jira 跟踪任务
- 开发前先更新文档，确保文档与代码一致

---

## 11. 文档要求

- 所有接口需在 `docs/api.md` 中记录
- 关键逻辑需在代码中添加 `// TODO` 或 `@description` 注释
- 项目 README 包含：启动命令、环境配置、部署说明
- 每个功能模块应有单独的实现说明文档，存放在 `docs/` 目录下

---

## 12. 违规处理

| 违规行为 | 处理方式 |
|--------|---------|
| 提交代码未格式化 | CI 拒绝合并，需本地运行 `npm run format` |
| 未写类型定义 | Code Review 打回 |
| 直接推送 `main` 分支 | 回滚 + 团队通报 |
| 不按规范编写文档 | 要求补充完整后再进行 Review |

---

## ✅ 附录：推荐配置文件

### `tsconfig.json` 示例
```json
{
  "extends": "@vue/tsconfig/tsconfig.json",
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "skipLibCheck": true,
    "types": ["@dcloudio/types"],

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",

    /* Linting - 放宽一些严格检查 */
    "strict": false,
    "noImplicitAny": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,

    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "src/**/*.d.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.js"
  ]
}
```

### `main.ts` 配置示例
```ts
import { createSSRApp, h } from 'vue'
import './App.vue'
import pinia from './stores'

export function createApp(): { app: App } {
  const app = createSSRApp({
    onShow: () => {
      console.log('App Show')
    },
    render: () => h("router-view")
  })
  
  // 使用Pinia状态管理
  app.use(pinia)
  
  return {
    app
  }
}
```

### `stores/index.ts` 配置示例
```ts
import { createPinia } from 'pinia'

// 创建pinia实例
const pinia = createPinia()

// 这里可以添加pinia的插件配置

// 导出pinia实例
export default pinia
```bash
src/
├── api/               # 接口请求封装
├── assets/            # 静态资源（图片、字体等）
├── components/        # 通用组件（支持跨页面复用）
│   └── multi-step/    # 多步骤表单相关组件
├── config/            # 应用配置
├── pages/             # 页面文件（uni-app 要求）
│   ├── login/         # 登录相关页面
│   └── register/      # 注册相关页面
├── services/          # 业务逻辑服务层
├── stores/            # Pinia 状态管理
│   ├── index.ts       # Pinia 初始化配置
│   ├── auth.ts        # 认证状态管理
│   └── registration.ts # 注册流程状态管理
├── types/             # 全局 TypeScript 类型定义
├── utils/             # 工具函数
├── App.vue            # 根组件
├── main.ts            # 入口文件
├── manifest.json      # uni-app 配置文件
├── pages.json         # 页面路由配置
└── uni.scss           # uni-app 全局样式文件
```

> ✅ 所有路径使用 `@` 别名指向 `src/`，配置在 `vite.config.js` 和 `tsconfig.json`

---

## 4. TypeScript 使用规范

### 4.1 类型定义
- 所有接口返回数据必须定义 `interface` 或 `type`
- 文件命名：在 `src/types/index.ts` 中统一管理核心类型定义
- 类型注释：使用 JSDoc 格式注释关键类型定义

```ts
// src/types/index.ts
/**
 * 用户类型
 */
export interface User {
  id: string
  phone: string
  nickname: string
  gender: string
  avatar_url?: string
  bio?: string
  created_at?: string
  updated_at?: string
}
```

### 4.2 API 请求类型安全
- 所有 API 请求和响应必须使用 TypeScript 类型定义
- 服务层使用泛型接收返回类型，确保类型安全

```ts
// src/services/auth.ts
import type { ApiResponse, AuthData, LoginCredentials, RegisterData } from '@/types'

export const loginWithPhone = async (credentials: LoginCredentials): Promise<ApiResponse<AuthData>> => {
  // 实现登录逻辑
}
```

### 4.3 编译配置
- 项目使用 `tsconfig.json` 配置编译选项
- 允许适当放宽严格检查，提高开发效率
- 详细配置见附录

---

## 5. 组件开发规范

### 5.1 组件命名
- 文件名：**大驼峰命名法**，如 `MultiStepForm.vue`
- 目录结构：通用组件放在 `src/components/` 目录下，按功能模块分组

### 5.2 Props 与 Emits
- 所有 `props` 必须使用 `interface` 定义并使用 `withDefaults` 设置默认值
- `emits` 使用泛型方式明确声明事件名和参数类型
- 组件内部状态使用 `ref`、`reactive` 等 Vue 3 Composition API

```vue
<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  totalSteps: number
  initialStep?: number
  showProgress?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  initialStep: 1,
  showProgress: true
})

const emit = defineEmits<{
  (e: 'stepChange', step: number): void
  (e: 'next'): void
  (e: 'previous'): void
  (e: 'complete'): void
}>()
</script>
```

### 5.3 注释规范
- 组件文件顶部使用 JSDoc 格式注释组件功能、作者信息
- 关键逻辑和复杂计算使用行内注释说明

```vue
<!--
 * 多步骤表单组件
 * @description 通用的多步骤表单组件，可用于注册、设置等流程
 * <AUTHOR> AI
 * @since 2025-01-13
 -->
<template>
  <!-- 组件内容 -->
</template>
```

---

## 6. 代码风格与格式化

### 6.1 代码风格
- 使用 Vue 3 Composition API + `<script setup>` 语法
- 优先使用 TypeScript 而非 JavaScript
- 使用单引号而非双引号
- 缩进使用 2 个空格

### 6.2 提交规范（Commit Convention）
使用 **Conventional Commits** 规范：

| 类型 | 说明 |
|------|------|
| `feat` | 新功能 |
| `fix` | 修复 bug |
| `docs` | 文档更新 |
| `style` | 样式调整（不影响逻辑） |
| `refactor` | 重构 |
| `perf` | 性能优化 |
| `test` | 测试相关 |
| `chore` | 构建/工具变动 |

示例：
```bash
git commit -m "feat(login): 添加微信一键登录功能"
git commit -m "fix(profile): 修复头像不显示问题"
```

---

## 7. API 请求规范

- 所有请求封装在 `src/api/` 或 `src/services/` 目录下
- 使用 `uni.request` 进行网络请求
- 封装统一的请求拦截器（如需）
- 处理网络错误和超时情况

---

## 8. 状态管理（Pinia）

- 每个功能模块一个 store，如 `auth.ts`、`registration.ts`
- 使用 `defineStore` + Composition API 语法
- 在 `src/stores/index.ts` 中创建并导出 pinia 实例
- 状态、计算属性、方法分离清晰

```ts
// src/stores/auth.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, AuthData, LoginCredentials } from '@/types'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref('')
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  
  // 方法
  const login = async (credentials: LoginCredentials) => {
    // 登录逻辑
  }
  
  return {
    user, token, isLoading, error, isAuthenticated,
    login
  }
})
```

---

## 9. 构建与发布流程

| 环境 | 命令 | 输出目标 |
|------|------|---------|
| H5 开发 | `npm run dev:h5` | 本地开发服务器 |
| 微信小程序开发 | `npm run dev:mp-weixin` | 微信开发者工具 |
| App 开发 | `npm run dev:app` | App 调试 |
| H5 构建 | `npm run build:h5` | 生产环境 H5 版本 |
| 微信小程序构建 | `npm run build:mp-weixin` | 生产环境小程序版本 |
| TypeScript 类型检查 | `npm run type-check` | 检查 TypeScript 编译错误 |

> 使用 `.env` 文件管理环境变量

## 10. 更新日志
- 在 CHANGELOG.md 书写每个版本的变更
- 对用户使用上无感知的改动建议（文档修复、微笑的样式优化、代码风格重构等）不要体积，保持CHANGELOG的内容有效性
- 非组件的改动（如工程化、构建工具、开发流动等）在changelog 区块中写 “_”
- 用面向开发者的角度和叙述方式撰写CHANGELOG，不描述修复细节，描述问题和对开发者的影响；描述用户的原始问题，而非你的解决方式。
