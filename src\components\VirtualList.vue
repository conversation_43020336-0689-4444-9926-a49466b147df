<template>
  <view class="virtual-list" @scroll="handleScroll" :style="{ height: containerHeight + 'px' }">
    <view class="virtual-list-phantom" :style="{ height: totalHeight + 'px' }"></view>
    <view class="virtual-list-content" :style="{ transform: `translate3d(0,${offsetY}px,0)` }">
      <view
        v-for="item in visibleData"
        :key="item.id"
        :data-id="item.id"
        class="virtual-list-item"
        :style="{ height: itemHeight + 'px' }"
      >
        <slot :item="item"></slot>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'VirtualList',
  props: {
    // 所有数据
    items: {
      type: Array,
      required: true
    },
    // 每项高度
    itemHeight: {
      type: Number,
      default: 200
    },
    // 容器高度
    containerHeight: {
      type: Number,
      default: 600
    },
    // 初始滚动位置
    initialScrollTop: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      // 可视区域项数量
      visibleCount: 0,
      // 偏移量
      offsetY: 0
    }
  },
  computed: {
    // 总高度
    totalHeight() {
      return this.items.length * this.itemHeight
    },
    // 可视区域起始索引
    visibleStart() {
      return Math.floor(this.offsetY / this.itemHeight)
    },
    // 可视区域结束索引
    visibleEnd() {
      return this.visibleStart + this.visibleCount
    },
    // 可视区域数据
    visibleData() {
      return this.items.slice(this.visibleStart, this.visibleEnd)
    }
  },
  mounted() {
    // 计算可视区域项数量
    this.visibleCount = Math.ceil(this.containerHeight / this.itemHeight)

    // 如果有初始滚动位置，设置offsetY并滚动到对应位置
    if (this.initialScrollTop > 0) {
      this.offsetY = this.initialScrollTop
      this.$nextTick(() => {
        // 确保DOM更新后滚动到正确位置
        const container = this.$el
        if (container) {
          container.scrollTop = this.initialScrollTop
        }
      })
    }
  },
  methods: {
    handleScroll(event) {
      const { scrollTop } = event.detail
      this.offsetY = scrollTop
      this.$emit('scroll', event)
    },
    
    /**
     * 滚动到指定位置
     * @param {number} scrollTop - 要滚动到的位置
     */
    scrollToPosition(scrollTop) {
      this.offsetY = scrollTop
    }
  }
}
</script>

<style scoped>
.virtual-list {
  position: relative;
  overflow: auto;
}

.virtual-list-phantom {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  z-index: -1;
}

.virtual-list-content {
  left: 0;
  right: 0;
  top: 0;
  position: absolute;
}

.virtual-list-item {
  display: flex;
  flex-direction: column;
}
</style>