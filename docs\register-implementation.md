# 注册功能实现文档

## 概述

Biu社交应用的注册功能基于自定义认证系统实现，不依赖Supabase Auth。整个系统使用Supabase作为后端数据库，通过自定义认证服务处理用户注册流程。

## 技术架构

```
注册页面 (pages/register/register.vue)
  ↓
认证存储 (stores/auth.ts)
  ↓
认证服务 (services/auth.ts)
  ↓
Supabase数据库
```

## 实现细节

### 1. 数据验证

注册过程中会对用户输入进行多重验证：
- 手机号唯一性检查
- 昵称唯一性检查
- 密码长度验证（至少6位）
- 性别信息验证
- 邀请码有效性验证（如果提供）

### 2. 密码安全

- 使用SHA-256算法对用户密码进行加密存储
- 数据库中不存储明文密码，只存储加密后的哈希值

```typescript
const hashPassword = (password: string): string => {
  // 使用SHA-256加密密码
  return crypto.SHA256(password).toString()
}
```

### 3. 注册流程

1. 用户在注册页面填写手机号、密码、昵称等信息
2. 页面调用 `authStore.registerWithPhone()` 方法
3. Store调用 `authService.registerWithPhone()` 服务方法
4. 服务层执行以下操作：
   - 验证输入参数
   - 检查手机号和昵称唯一性
   - 验证邀请码（如果提供）
   - 创建新用户记录
   - 更新邀请码使用次数（如果使用了邀请码）
   - 生成会话令牌
   - 创建用户会话记录
   - 返回用户信息和令牌

### 4. 邀请码机制

系统支持邀请码注册机制，用于控制用户注册：
- 邀请码有使用次数限制
- 邀请码可以设置过期时间
- 邀请码可以激活/停用

### 5. 会话管理

- 使用JWT风格的自定义令牌机制
- 令牌有效期为7天
- 令牌存储在本地存储中，应用启动时自动验证

```typescript
// 生成会话令牌
const generateSessionToken = (): string => {
  return crypto.lib.WordArray.random(32).toString()
}

// 会话有效期设置
const expiresAt = new Date()
expiresAt.setDate(expiresAt.getDate() + 7) // 7天后过期
```

### 6. Supabase数据库表结构

注册功能涉及以下数据库表：

#### users表
存储用户基本信息：
- id: 用户唯一标识
- phone: 手机号（唯一）
- password: 加密后的密码
- nickname: 用户昵称（唯一）
- avatar_url: 头像URL
- gender: 性别
- bio: 个人简介
- created_at: 创建时间

#### user_sessions表
存储用户会话信息：
- user_id: 关联的用户ID
- token: 会话令牌
- expires_at: 过期时间

#### invitations表
存储邀请码信息：
- id: 邀请码唯一标识
- code: 邀请码
- max_uses: 最大使用次数
- current_uses: 当前使用次数
- is_active: 是否激活
- expires_at: 过期时间

## 错误处理

系统对以下错误情况进行处理：
- 手机号已被注册
- 昵称已被使用
- 邀请码无效/过期/已达使用上限
- 数据库插入异常
- 网络连接问题

所有错误都会向用户显示友好的提示信息，同时在控制台记录详细日志便于调试。

## 安全特性

1. 密码加密存储
2. 手机号和昵称唯一性保证
3. 邀请码机制控制注册
4. 会话令牌机制
5. 令牌过期处理
6. 输入验证和参数检查
7. SQL注入防护（通过Supabase SDK）
8. 错误信息不会暴露敏感信息

## 代码示例

### 注册服务方法
```typescript
export const registerWithPhone = async (data: RegisterData): Promise<ApiResponse<AuthData>> => {
  try {
    const supabase = getSupabaseInstance();
    const { phone, password, nickname, gender, avatarUrl, invitationCode } = data

    // 验证输入
    if (!phone || !password || !nickname) {
      return { success: false, error: '手机号、密码和昵称不能为空' }
    }

    // 检查手机号是否已存在
    const { data: existingUser, error: phoneCheckError } = await supabase
      .from('users')
      .select('id')
      .eq('phone', phone)
      .maybeSingle()

    if (phoneCheckError && phoneCheckError.code !== 'PGRST116') {
      console.error('检查手机号失败:', phoneCheckError)
      return { success: false, error: '注册失败' }
    }

    if (existingUser) {
      return { success: false, error: '手机号已被注册' }
    }

    // 检查昵称是否已存在
    const { data: existingNickname, error: nicknameCheckError } = await supabase
      .from('users')
      .select('id')
      .eq('nickname', nickname)
      .maybeSingle()

    if (nicknameCheckError && nicknameCheckError.code !== 'PGRST116') {
      console.error('检查昵称失败:', nicknameCheckError)
      return { success: false, error: '注册失败' }
    }

    if (existingNickname) {
      return { success: false, error: '昵称已被使用' }
    }

    // 验证邀请码（如果提供）
    if (invitationCode) {
      const { data: invitation, error: invitationError } = await supabase
        .from('invitations')
        .select('id, max_uses, current_uses, is_active, expires_at')
        .eq('code', invitationCode)
        .single()

      if (invitationError) {
        if (invitationError.code === 'PGRST116') {
          return { success: false, error: '邀请码不存在' }
        }
        console.error('验证邀请码失败:', invitationError)
        return { success: false, error: '注册失败' }
      }

      // 检查邀请码是否激活
      if (!invitation.is_active) {
        return { success: false, error: '邀请码未激活' }
      }

      // 检查邀请码是否过期
      if (invitation.expires_at && new Date(invitation.expires_at) < new Date()) {
        return { success: false, error: '邀请码已过期' }
      }

      // 检查邀请码使用次数
      if (invitation.current_uses >= invitation.max_uses) {
        return { success: false, error: '邀请码使用次数已达上限' }
      }
    }

    // 加密密码
    const hashedPassword = hashPassword(password)

    // 创建用户
    const { data: newUser, error: createUserError } = await supabase
      .from('users')
      .insert({
        phone,
        password: hashedPassword,
        nickname,
        gender: gender.toString(),
        avatar_url: avatarUrl || null,
        created_at: new Date().toISOString()
      })
      .select()
      .single()

    if (createUserError) {
      console.error('创建用户失败:', createUserError)
      return { success: false, error: '注册失败' }
    }

    // 更新邀请码使用次数（如果使用了邀请码）
    if (invitationCode) {
      const { error: updateInvitationError } = await supabase
        .from('invitations')
        .update({ current_uses: invitation.current_uses + 1 })
        .eq('code', invitationCode)

      if (updateInvitationError) {
        console.warn('更新邀请码使用次数失败:', updateInvitationError)
        // 这里不返回错误，因为用户已经创建成功
      }
    }

    // 删除密码字段，不返回给前端
    delete newUser.password

    // 生成会话令牌
    const token = generateSessionToken()
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 7) // 7天后过期

    // 创建会话记录
    const { error: sessionError } = await supabase
      .from('user_sessions')
      .insert({
        user_id: newUser.id,
        token,
        expires_at: expiresAt.toISOString()
      })

    if (sessionError) {
      console.error('创建会话失败:', sessionError)
      return { success: false, error: '注册成功但登录失败，请尝试手动登录' }
    }

    return {
      success: true,
      data: {
        user: {
          id: newUser.id,
          phone: newUser.phone,
          nickname: newUser.nickname,
          gender: newUser.gender,
          avatar_url: newUser.avatar_url,
          bio: newUser.bio
        },
        token
      },
      message: '注册成功'
    }
  } catch (error: any) {
    console.error('注册异常:', error)
    return {
      success: false,
      error: error.message || '注册异常'
    }
  }
}
```