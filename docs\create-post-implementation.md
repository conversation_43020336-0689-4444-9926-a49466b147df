# 发布动态功能实现文档

## 概述

Biu社交应用的发布动态功能允许用户创建并分享包含文本和图片的内容。该功能提供简洁直观的界面，让用户可以轻松地分享他们的生活点滴。

## 页面结构

发布动态页面主要由以下几个部分组成：

1. **顶部操作栏** - 包含取消按钮、标题和发布按钮
2. **内容输入区** - 文本输入框，用于输入动态内容
3. **媒体上传区** - 图片上传和预览功能

## 技术架构

```
发布动态页面 (pages/create-post/create-post.vue)
  ↓
认证存储 (stores/auth.ts)
  ↓
本地存储 (uni.setStorageSync/uni.getStorageSync)
```

## 核心功能实现

### 1. 页面布局与组件

发布动态页面采用简洁的布局设计，主要包括：

- 顶部导航栏：提供取消和发布按钮
- 内容输入区：支持最多500字符的文本输入
- 字符计数器：实时显示已输入字符数
- 图片上传区：支持添加和预览图片

### 2. 内容输入

用户可以在文本区域输入动态内容，系统提供以下功能：

```vue
<textarea 
  v-model="postContent"
  placeholder="分享你的生活..." 
  class="content-input"
  maxlength="500"
/>

<view class="content-info">
  <text class="char-count">{{ postContent.length }}/500</text>
</view>
```

- 最大字符限制：500个字符
- 实时字符计数显示
- 占位符提示文本

### 3. 图片上传与管理

用户可以上传最多9张图片：

```javascript
chooseImage() {
  uni.chooseImage({
    count: 9 - this.imageList.length,
    success: (res) => {
      this.imageList = [...this.imageList, ...res.tempFilePaths]
    }
  })
}
```

功能特点：
- 限制最多9张图片
- 支持多选图片
- 图片预览功能
- 删除已选图片

### 4. 发布流程

发布动态的完整流程如下：

1. 验证输入内容（必须有文本内容）
2. 构造动态对象
3. 从本地存储获取现有动态列表
4. 将新动态添加到列表开头
5. 保存更新后的列表到本地存储
6. 设置新内容标记
7. 返回上一页

```javascript
async publishPost() {
  if (!this.canPublish || this.publishing) return
  
  this.publishing = true
  
  try {
    // 创建新帖子对象
    const newPost = {
      id: this.generateId(),
      content: this.postContent,
      images: [...this.imageList], // 克隆图片列表
      location: null,
      created_at: new Date().toISOString(),
      user_id: this.authStore.userId,
      username: this.authStore.username,
      avatar_url: this.authStore.avatarUrl || getUserAvatar('', this.authStore.username),
      like_count: 0,
      comment_count: 0,
      share_count: 0,
      liked: false
    }
    
    // 从本地存储获取现有帖子
    let existingPosts = []
    try {
      const storedPosts = uni.getStorageSync('posts')
      if (storedPosts) {
        existingPosts = JSON.parse(storedPosts)
      }
    } catch (e) {
      console.error('解析本地存储的帖子失败:', e)
      existingPosts = []
    }
    
    // 将新帖子添加到最前面
    existingPosts.unshift(newPost)
    
    // 保存到本地存储
    uni.setStorageSync('posts', JSON.stringify(existingPosts))
    
    // 显示成功提示
    uni.showToast({
      title: '发布成功',
      icon: 'success'
    })
    
    // 设置标记表示有新内容
    uni.setStorageSync('hasNewContent', true)
    
    // 延迟返回首页
    setTimeout(() => {
      this.publishing = false
      uni.navigateBack()
    }, 1500)
  } catch (error) {
    console.error('发布失败:', error)
    this.publishing = false
    
    uni.showToast({
      title: '发布失败，请重试',
      icon: 'none'
    })
  }
}
```

### 5. 数据结构

发布的动态包含以下字段：

- `id`: 动态唯一标识（使用时间戳+随机字符串生成）
- `content`: 动态内容文本
- `images`: 图片路径数组
- `location`: 位置信息（当前未实现）
- `created_at`: 创建时间（ISO格式）
- `user_id`: 用户ID
- `username`: 用户名
- `avatar_url`: 用户头像URL
- `like_count`: 点赞数（初始为0）
- `comment_count`: 评论数（初始为0）
- `share_count`: 分享数（初始为0）
- `liked`: 当前用户是否已点赞（初始为false）

### 6. 用户认证集成

发布动态功能与认证系统集成，自动获取当前用户信息：

```javascript
computed: {
  authStore() {
    return useAuthStore()
  },
  canPublish() {
    return this.postContent.trim().length > 0
  }
}
```

- 自动填充用户ID、用户名和头像
- 发布按钮状态根据内容是否为空进行控制

### 7. 状态管理

页面管理以下状态：

- `postContent`: 用户输入的文本内容
- `imageList`: 已选择的图片列表
- `publishing`: 发布状态（防止重复提交）

### 8. 用户体验优化

1. **发布状态反馈**：
   - 发布按钮显示"发布中..."状态
   - 成功后显示Toast提示
   - 失败后显示错误提示

2. **防止重复提交**：
   - 发布过程中禁用发布按钮
   - 设置[publishing](file:///c:\Users\<USER>\Desktop\aicode\社交\uniapp-frontend\uniapp\Biu\src\pages\create-post\create-post.vue#L23-L23)状态标志

3. **数据持久化**：
   - 使用本地存储保存动态数据
   - 发布后设置新内容标记，首页会自动刷新

## 错误处理

系统对以下错误情况进行处理：
- 本地存储读取/解析失败
- 发布过程中出现异常
- 图片选择失败

所有错误都会向用户显示友好的提示信息，同时在控制台记录详细日志便于调试。

## 限制与约束

1. 文本内容不能为空
2. 最多支持500个字符
3. 最多支持9张图片
4. 图片格式依赖系统支持

## 待办事项

1. 集成真实数据发布功能（对接Supabase）
2. 实现位置信息获取功能
3. 添加图片压缩功能
4. 支持视频上传
5. 增加草稿保存功能
6. 实现图片上传到云存储

## 代码示例

### 完整的发布方法
```javascript
async publishPost() {
  if (!this.canPublish || this.publishing) return
  
  this.publishing = true
  
  try {
    // 创建新帖子对象
    const newPost = {
      id: this.generateId(),
      content: this.postContent,
      images: [...this.imageList], // 克隆图片列表
      location: null,
      created_at: new Date().toISOString(),
      user_id: this.authStore.userId,
      username: this.authStore.username,
      avatar_url: this.authStore.avatarUrl || getUserAvatar('', this.authStore.username),
      like_count: 0,
      comment_count: 0,
      share_count: 0,
      liked: false
    }
    
    // 从本地存储获取现有帖子
    let existingPosts = []
    try {
      const storedPosts = uni.getStorageSync('posts')
      if (storedPosts) {
        existingPosts = JSON.parse(storedPosts)
      }
    } catch (e) {
      console.error('解析本地存储的帖子失败:', e)
      existingPosts = []
    }
    
    // 将新帖子添加到最前面
    existingPosts.unshift(newPost)
    
    // 保存到本地存储
    uni.setStorageSync('posts', JSON.stringify(existingPosts))
    
    // 显示成功提示
    uni.showToast({
      title: '发布成功',
      icon: 'success'
    })
    
    // 设置标记表示有新内容
    uni.setStorageSync('hasNewContent', true)
    
    // 延迟返回首页
    setTimeout(() => {
      this.publishing = false
      uni.navigateBack()
    }, 1500)
  } catch (error) {
    console.error('发布失败:', error)
    this.publishing = false
    
    uni.showToast({
      title: '发布失败，请重试',
      icon: 'none'
    })
  }
}
```

### 图片管理方法
```javascript
chooseImage() {
  uni.chooseImage({
    count: 9 - this.imageList.length,
    success: (res) => {
      this.imageList = [...this.imageList, ...res.tempFilePaths]
    }
  })
},

removeImage(index) {
  this.imageList.splice(index, 1)
}
```