<template>
  <view class="container" @touchstart="handleTouchStart" @touchend="handleTouchEnd">
    <!-- 顶部导航栏 -->
    <view class="navbar">
      <text class="app-name">Biu</text>
      <view class="search-container">
        <input 
          type="text" 
          placeholder="搜索内容或用户" 
          class="search-input"
          v-model="searchKeyword"
          @confirm="handleSearch"
        />
      </view>
      <view class="user-avatar" @click="navigateToProfile" v-if="isLoggedIn">
        <image :src="userAvatar || '/static/default-avatar.png'" mode="aspectFit"></image>
      </view>
    </view>

    <!-- 内容分类标签栏 -->
    <view class="tabs-container">
      <scroll-view scroll-x class="tabs-scroll" :scroll-left="scrollLeft">
        <view
          v-for="(tab, index) in tabs"
          :key="tab.id"
          :class="['tab-item', { 'active': currentTab === index }]"
          @click="switchTab(index)"
        >
          {{ tab.name }}
        </view>
      </scroll-view>
    </view>

    <!-- 主内容区域 -->
    <view class="content">
      <!-- 加载状态 -->
      <view v-if="loading && (!feedItems || feedItems.length === 0)" class="loading-container">
        <LoadingSpinner text="正在加载内容..." />
      </view>

      <!-- 内容列表 -->
      <view v-else-if="filteredFeedItems && filteredFeedItems.length > 0" class="feed-list">
        <!-- 使用虚拟列表优化长列表渲染 -->
        <VirtualList
          v-if="filteredFeedItems.length > 20"
          ref="virtualListRef"
          :items="filteredFeedItems"
          :itemHeight="350"
          :containerHeight="600"
          :initialScrollTop="virtualScrollTop"
          @scroll="handleScroll"
        >
          <template #default="{ item }">
            <FeedItem
              :item="item"
              :formatTime="formatTime"
              @click="navigateToPostDetail"
              @more="showMoreOptions"
              @like="likePost"
              @comment="commentPost"
              @share="sharePost"
              @imagePreview="previewImage"
            />
          </template>
        </VirtualList>
        
        <!-- 数据量较小时使用普通列表 -->
        <scroll-view
          v-else
          ref="normalScrollRef"
          class="normal-scroll-list"
          scroll-y
          :scroll-top="normalScrollTop"
          @scroll="handleNormalScroll"
        >
          <FeedItem
            v-for="(item, index) in filteredFeedItems"
            :key="item.id"
            :item="item"
            :formatTime="formatTime"
            @click="navigateToPostDetail"
            @more="showMoreOptions"
            @like="likePost"
            @comment="commentPost"
            @share="sharePost"
            @imagePreview="previewImage"
          />
        </scroll-view>
        
        <!-- 加载更多 -->
        <view v-if="loading && feedItems && feedItems.length > 0" class="loading-more">
          <LoadingSpinner text="加载更多内容..." size="small" />
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else class="empty-state">
        <text class="empty-text">暂无内容</text>
        <text class="empty-hint">快来发布第一条动态吧！</text>
      </view>
    </view>

    <!-- 创建按钮 -->
    <view class="create-btn" @click="createPost">
      <text class="create-icon">➕</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
// 注意：uni-app 的生命周期钩子需要从 @dcloudio/uni-app 导入
import { onLoad as uniOnLoad, onShow as uniOnShow, onPullDownRefresh as uniOnPullDownRefresh, onReachBottom as uniOnReachBottom } from '@dcloudio/uni-app'
import { getPosts } from '@/services/post'
import { PostCache } from '@/utils/storage-manager'
import { authManager } from '@/utils/auth-manager'
import { apiHandler } from '@/utils/api-response-handler'
import { AuthChecker, withAuthCheck } from '@/utils/auth-guard'
import { likePostService } from '@/services/post'
import { DatabaseChecker } from '@/utils/database-checker'
import { getAnonymousSupabaseClient } from '@/utils/supabase'
import { formatRelativeTime } from '@/utils/time-utils'
import { useAuthStore } from '@/stores/auth'
import LoadingSpinner from '@/components/LoadingSpinner.vue'
import VirtualList from '@/components/VirtualList.vue'
import FeedItem from '@/components/FeedItem.vue'

// 定义组件名称
const name = 'IndexPage'

// 响应式数据
const searchKeyword = ref('')
const currentTab = ref(0)
const scrollLeft = ref(0)
const loading = ref(false)
const hasMore = ref(true)
const touchStartX = ref(0)
const touchStartY = ref(0)
const minSwipeDistance = ref(50)
const page = ref(1)
const limit = ref(10)
const lastRefreshTime = ref<number | null>(null)
const refreshInterval = ref(3000)
const feedItems = ref<any[]>([])
const virtualListRef = ref<any>(null)
const virtualScrollTop = ref(0)
const normalScrollRef = ref<any>(null)
const normalScrollTop = ref(0)

// 标签数据
const tabs = [
  { id: 'recommend', name: '推荐' },
  { id: 'following', name: '关注' },
  { id: 'hot', name: '热门' },
  { id: 'local', name: '附近' },
  { id: 'interest', name: '兴趣' }
]

// 计算属性
const isLoggedIn = computed(() => {
  const authStore = useAuthStore()
  return authStore.isLoggedIn
})

const userAvatar = computed(() => {
  const authStore = useAuthStore()
  const user = authStore.user
  return user?.avatar_url || '/static/default-avatar.png'
})

const filteredFeedItems = computed(() => {
  if (!Array.isArray(feedItems.value)) {
    return []
  }
  
  const currentTag = tabs[currentTab.value].id
  if (currentTag === 'recommend') {
    return feedItems.value
  }
  return feedItems.value.filter(item => item.tag === currentTag)
})

/**
 * 格式化时间显示
 * @param {string} timestamp - 时间戳或日期字符串
 * @returns {string} 格式化后的相对时间
 */
const formatTime = (timestamp: string): string => {
  return formatRelativeTime(timestamp)
}

/**
 * 处理触摸开始事件
 */
const handleTouchStart = (event: TouchEvent) => {
  const touch = event.touches[0]
  touchStartX.value = touch.clientX
  touchStartY.value = touch.clientY
}

/**
 * 处理触摸结束事件
 */
const handleTouchEnd = (event: TouchEvent) => {
  const touch = event.changedTouches[0]
  const deltaX = touch.clientX - touchStartX.value
  const deltaY = touch.clientY - touchStartY.value
  
  // 判断是否为有效的右滑动作
  if (deltaX > minSwipeDistance.value && Math.abs(deltaY) < Math.abs(deltaX) * 0.5) {
    navigateToProfile()
  }
}

/**
 * 处理VirtualList滚动事件
 * @param event - 滚动事件对象，包含scrollTop值
 */
const handleScroll = (event: any) => {
  // 保存虚拟滚动位置
  if (event && event.detail && event.detail.scrollTop !== undefined) {
    virtualScrollTop.value = event.detail.scrollTop
  }
}

/**
 * 处理普通列表滚动事件
 * @param event - 滚动事件对象，包含scrollTop值
 */
const handleNormalScroll = (event: any) => {
  // 保存普通列表滚动位置
  if (event && event.detail && event.detail.scrollTop !== undefined) {
    normalScrollTop.value = event.detail.scrollTop
  }
}

/**
 * 生命周期 - 页面加载
 */
uniOnLoad(async () => {
  console.log('📱 首页加载中...')
  
  // 验证会话
  try {
    await authManager.validateSession()
  } catch (error) {
    console.error('验证会话失败:', error)
  }
  
  // 数据库连接测试
  console.log('🔍 开始数据库连接测试...')
  try {
    const supabase = getAnonymousSupabaseClient()
    
    // 测试posts表
    const { data: posts, error: dbError } = await supabase
      .from('posts')
      .select('*')
      .limit(1)
    
    if (dbError) {
      console.error('❌ 数据库连接失败:', dbError)
      uni.showToast({
        title: '数据库连接异常',
        icon: 'error',
        duration: 3000
      })
    } else {
      console.log(`✅ 数据库连接正常，找到 ${posts?.length || 0} 条数据`)
      
      // 加载内容
      console.log('🔄 加载首页内容...')
      await fetchContentList(true)
    }
  } catch (error) {
    console.error('❌ 数据库测试异常:', error)
    uni.showToast({
      title: '数据加载失败',
      icon: 'error',
      duration: 3000
    })
  }
})

/**
 * 生命周期 - 页面显示
 */
uniOnShow(async () => {
  console.log('📱 首页显示，检查是否需要刷新内容...')

  // 先尝试恢复滚动位置
  const shouldRestorePosition = restoreScrollPosition()

  // 检查是否有新内容或是否需要刷新
  const hasNewContent = PostCache.hasNewContent()

  if (hasNewContent) {
    console.log('🔄 检测到新内容或用户请求刷新，刷新列表...')
    PostCache.clearNewContentFlag()

    // 如果需要恢复滚动位置，则不刷新数据，避免丢失位置
    if (!shouldRestorePosition) {
      // 强制刷新内容
      await fetchContentList(true)

      uni.showToast({
        title: '内容已更新',
        icon: 'success',
        duration: 1500
      })
    } else {
      console.log('🔧 跳过数据刷新以保持滚动位置')
    }
  } else if (!feedItems.value || feedItems.value.length === 0) {
    // 如果没有内容，尝试加载数据
    await fetchContentList(true)
  }

  // 延迟清除保存的位置，确保列表有时间应用滚动位置
  if (shouldRestorePosition) {
    setTimeout(() => {
      clearScrollPosition()
    }, 1000) // 增加延迟时间确保恢复成功
  }
})

/**
 * 获取内容列表
 */
const fetchContentList = async (refresh = false) => {
  if (loading.value) return
  
  loading.value = true
  
  if (refresh) {
    page.value = 1
    hasMore.value = true
    feedItems.value = []
  }
  
  console.log('🔄 开始获取内容列表...')
  
  try {
    const currentTag = tabs[currentTab.value]?.id
    const response = await apiHandler<{
        posts: any[]
        hasMore: boolean
      }>(
        () => getPosts(
          page.value,
          limit.value,
          currentTag === 'recommend' ? undefined : currentTag
        ),
        {
          showLoading: feedItems.value.length === 0,
          showError: true,
          customErrorMessage: '获取数据失败，请检查网络连接'
        }
      )
    
    if (response.success && response.data) {
      const data = response.data
      const list = data.posts || []
      const newHasMore = data.hasMore
      
      if (refresh) {
        feedItems.value = list
      } else {
        feedItems.value.push(...list)
      }
      
      hasMore.value = newHasMore
      if (list.length > 0) {
        page.value++
      }
      
      // 缓存数据
      PostCache.setPosts(feedItems.value)
    }
  } catch (error) {
    console.error('获取内容失败:', error)
  } finally {
    loading.value = false
    uni.stopPullDownRefresh()
  }
}

/**
 * 切换标签
 */
const switchTab = (index: number) => {
  currentTab.value = index
  scrollLeft.value = index * 100
  fetchContentList(true)
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  if (!searchKeyword.value.trim()) return
  uni.navigateTo({
    url: `/pages/search/search?keyword=${encodeURIComponent(searchKeyword.value)}`
  })
}

/**
 * 点赞动态 - 使用认证守卫包装
 */
const likePost = withAuthCheck((item: any) => {
  const originalLiked = item.liked
  const originalCount = item.like_count

  // 乐观更新UI
  item.liked = !item.liked
  item.like_count = item.liked ? (originalCount + 1) : (originalCount - 1)

  const feedIndex = feedItems.value.findIndex(feedItem => feedItem.id === item.id)
  if (feedIndex !== -1) {
    feedItems.value[feedIndex] = { ...item }
  }

  // 异步更新服务器
  apiHandler<{
      success: boolean
    }>(
    () => likePostService(item.id, item.liked),
    {
      showLoading: false,
      showError: false
    }
  ).then(response => {
    if (!response.success) {
      // 回滚UI状态
      item.liked = originalLiked
      item.like_count = originalCount
      
      const feedIndex = feedItems.value.findIndex(feedItem => feedItem.id === item.id)
      if (feedIndex !== -1) {
        feedItems.value[feedIndex] = { ...item }
      }
    }
  })
}, {
  customMessage: '登录后才能点赞哦'
})

/**
 * 评论动态 - 使用认证守卫包装
 */
const commentPost = withAuthCheck((item: any) => {
  uni.showToast({
    title: '评论功能开发中',
    icon: 'none'
  })
}, {
  customMessage: '登录后才能评论哦'
})

/**
 * 分享动态 - 使用认证守卫包装
 */
const sharePost = withAuthCheck((item: any) => {
  uni.showActionSheet({
    itemList: ['分享到微信', '分享到QQ', '分享到微博', '复制链接'],
    success: (res) => {
      // 更新UI显示
      item.share_count = (item.share_count || 0) + 1
      
      const feedIndex = feedItems.value.findIndex(feedItem => feedItem.id === item.id)
      if (feedIndex !== -1) {
        feedItems.value[feedIndex] = { ...item }
      }
      
      PostCache.updatePost(item.id, item)
      
      const shareTypes = ['微信', 'QQ', '微博', '链接']
      uni.showToast({
        title: `已分享到${shareTypes[res.tapIndex]}`,
        icon: 'success'
      })
    }
  })
})

/**
 * 创建新帖子 - 使用认证守卫包装
 */
const createPost = withAuthCheck(() => {
  uni.navigateTo({
    url: '/pages/create-post/create-post'
  })
}, {
  customMessage: '登录后才能发布内容哦'
})

/**
 * 保存当前滚动位置到本地存储
 * 同时支持虚拟列表和普通列表
 */
const saveScrollPosition = () => {
  // 判断当前使用的是虚拟列表还是普通列表
  const isUsingVirtualList = filteredFeedItems.value && filteredFeedItems.value.length > 20

  if (isUsingVirtualList) {
    const scrollTop = virtualScrollTop.value
    if (scrollTop !== undefined) {
      uni.setStorageSync('feed_virtual_scroll_top', scrollTop)
      uni.setStorageSync('feed_scroll_type', 'virtual')
      console.log('保存虚拟滚动位置:', scrollTop)
    }
  } else {
    const scrollTop = normalScrollTop.value
    if (scrollTop !== undefined) {
      uni.setStorageSync('feed_normal_scroll_top', scrollTop)
      uni.setStorageSync('feed_scroll_type', 'normal')
      console.log('保存普通滚动位置:', scrollTop)
    }
  }
}

/**
 * 恢复保存的滚动位置
 * 根据保存的类型恢复对应的滚动位置
 */
const restoreScrollPosition = () => {
  const scrollType = uni.getStorageSync('feed_scroll_type')

  if (scrollType === 'virtual') {
    const savedScrollTop = uni.getStorageSync('feed_virtual_scroll_top')
    if (savedScrollTop !== undefined && savedScrollTop > 0) {
      virtualScrollTop.value = savedScrollTop
      console.log('恢复虚拟滚动位置:', savedScrollTop)
      return true
    }
  } else if (scrollType === 'normal') {
    const savedScrollTop = uni.getStorageSync('feed_normal_scroll_top')
    if (savedScrollTop !== undefined && savedScrollTop > 0) {
      normalScrollTop.value = savedScrollTop
      console.log('恢复普通滚动位置:', savedScrollTop)
      return true
    }
  }

  return false
}

/**
 * 清除保存的滚动位置
 */
const clearScrollPosition = () => {
  uni.removeStorageSync('feed_virtual_scroll_top')
  uni.removeStorageSync('feed_normal_scroll_top')
  uni.removeStorageSync('feed_scroll_type')
  virtualScrollTop.value = 0
  normalScrollTop.value = 0
}

/**
 * 导航到帖子详情
 */
const navigateToPostDetail = (item: any) => {
  // 保存当前滚动位置
  saveScrollPosition()
  
  uni.navigateTo({
    url: `/pages/post-detail/post-detail?id=${item.id}`
  })
}

/**
 * 显示更多选项
 */
const showMoreOptions = (item: any) => {
  uni.showActionSheet({
    itemList: ['举报', '不感兴趣', '关注作者'],
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          uni.showToast({ title: '举报成功', icon: 'success' })
          break
        case 1:
          uni.showToast({ title: '已减少类似内容', icon: 'success' })
          break
        case 2:
          uni.showToast({ title: '关注成功', icon: 'success' })
          break
      }
    }
  })
}

/**
 * 预览图片
 */
const previewImage = (data: { images: string[], index: number }) => {
  const { images, index } = data
  uni.previewImage({
    urls: images,
    current: images[index]
  })
}



/**
 * 导航到个人资料 - 使用认证守卫包装
 */
const navigateToProfile = withAuthCheck(() => {
  uni.switchTab({
    url: '/pages/profile/profile'
  })
}, {
  customMessage: '登录后才能查看个人资料哦'
})

/**
 * 检查数据库连接
 */
const checkDatabaseConnection = async () => {
  try {
    const supabase = getAnonymousSupabaseClient()
    const { data, error } = await supabase
      .from('posts')
      .select('id')
      .limit(1)
    
    if (error) {
      console.error('❌ 数据库连接失败:', error)
      uni.showToast({
        title: '网络连接异常',
        icon: 'none',
        duration: 2000
      })
    } else {
      console.log('✅ 数据库连接正常')
    }
  } catch (error) {
    console.error('❌ 数据库连接测试异常:', error)
  }
}

/**
 * 验证用户会话
 */
const validateSession = async () => {
  try {
    const authStore = useAuthStore()
    await authStore.validateSession()
    console.log('✅ 用户会话验证完成')
  } catch (error) {
    console.error('❌ 会话验证失败:', error)
  }
}

// 生命周期钩子
onMounted(() => {
  console.log('🔧 组件挂载完成')
})



// 监听从详情页返回的事件
onMounted(() => {
  uni.$on('returnFromDetail', () => {
    console.log('收到从详情页返回的事件')
    // 这里可以触发额外的逻辑，如刷新数据
  })
})

// 下拉刷新
uniOnPullDownRefresh(async () => {
  // 获取当前时间戳
  const currentTime = Date.now()
  
  // 检查是否在刷新间隔内
  if (lastRefreshTime.value && (currentTime - lastRefreshTime.value < refreshInterval.value)) {
    console.log('⏱️ 刷新过于频繁，请稍后再试')
    uni.showToast({
      title: '刷新过于频繁，请稍后再试',
      icon: 'none',
      duration: 1500
    })
    uni.stopPullDownRefresh()
    return
  }
  
  // 记录这次刷新的时间
  lastRefreshTime.value = currentTime
  console.log('🔄 用户触发下拉刷新...')
  await fetchContentList(true)
})

// 上拉加载更多
uniOnReachBottom(() => {
  if (hasMore.value && !loading.value) {
    fetchContentList()
  }
});</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部导航栏 */
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.app-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #4caf50;
  white-space: nowrap;
}

.search-container {
  flex: 1;
  margin: 0 20rpx;
}

.search-input {
  width: 100%;
  height: 60rpx;
  background-color: #f0f0f0;
  border-radius: 30rpx;
  padding: 0 30rpx;
  font-size: 26rpx;
}

.user-avatar {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  overflow: hidden;
}

.user-avatar image {
  width: 100%;
  height: 100%;
}

/* 标签栏 */
.tabs-container {
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 100rpx;
  z-index: 99;
}

.tabs-scroll {
  white-space: nowrap;
  padding: 0 20rpx;
}

.tab-item {
  display: inline-block;
  padding: 20rpx 30rpx;
  margin-right: 10rpx;
  font-size: 28rpx;
  color: #666666;
  position: relative;
  transition: color 0.3s;
}

.tab-item.active {
  color: #4caf50;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #4caf50;
  border-radius: 2rpx;
}

/* 内容区域 */
.content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  background-color: #f8f8f8;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.empty-hint {
  font-size: 26rpx;
  color: #ccc;
}

/* 动态列表 */
.feed-list {
  padding: 20rpx 0;
}

/* 普通滚动列表 */
.normal-scroll-list {
  height: 100%;
  padding: 20rpx 0;
}

/* 操作按钮区域样式 */
.actions {
  display: flex;
  justify-content: space-between;
  padding: 10rpx 0;
}

/* 单个操作按钮样式 */
.action-btn {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
}

/* 操作图标样式 */
.action-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
}

/* 操作计数样式 */
.action-count {
  font-size: 24rpx;
  color: #999;
}

/* 点赞状态样式 */
.liked {
  color: #ff4d4f;
}

/* 创建按钮 */
.create-btn {
  position: fixed;
  bottom: 150rpx;
  right: 30rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #4caf50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.4);
  z-index: 100;
  transition: transform 0.3s;
}

.create-btn:active {
  transform: scale(0.9);
}

.create-icon {
  font-size: 50rpx;
  color: white;
  font-weight: bold;
}
</style>