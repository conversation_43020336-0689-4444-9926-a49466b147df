import{c as a,w as t,W as s,i as e,o as l,a as c,e as r,f as d,s as o,p as i,z as u}from"./index-D2g-qjTN.js";import{g as v}from"./avatar.DQyLU1U7.js";import{_ as n}from"./_plugin-vue_export-helper.BCo6x5W8.js";const f=n({name:"AvatarTestPage",data:()=>({defaultAvatar:"",customAvatar:"",selectedAvatar:""}),mounted(){this.loadAvatars()},methods:{loadAvatars(){this.defaultAvatar=v("","默认用户"),this.customAvatar=v("https://via.placeholder.com/150","自定义用户")},chooseAvatar(){s({count:1,success:a=>{this.selectedAvatar=a.tempFilePaths[0]}})}}},[["render",function(s,v,n,f,_,m){const p=r,A=e,h=o,g=u;return l(),a(A,{class:"avatar-test-container"},{default:t(()=>[c(A,{class:"header"},{default:t(()=>[c(p,{class:"title"},{default:t(()=>[d("头像测试")]),_:1})]),_:1}),c(A,{class:"content"},{default:t(()=>[c(A,{class:"avatar-section"},{default:t(()=>[c(p,{class:"section-title"},{default:t(()=>[d("默认头像")]),_:1}),c(A,{class:"avatar-preview"},{default:t(()=>[c(h,{src:_.defaultAvatar,class:"avatar-image",mode:"aspectFit"},null,8,["src"]),c(p,{class:"avatar-label"},{default:t(()=>[d("默认头像")]),_:1})]),_:1})]),_:1}),c(A,{class:"avatar-section"},{default:t(()=>[c(p,{class:"section-title"},{default:t(()=>[d("自定义头像")]),_:1}),c(A,{class:"avatar-preview"},{default:t(()=>[c(h,{src:_.customAvatar,class:"avatar-image",mode:"aspectFit"},null,8,["src"]),c(p,{class:"avatar-label"},{default:t(()=>[d("自定义头像")]),_:1})]),_:1})]),_:1}),c(A,{class:"avatar-section"},{default:t(()=>[c(p,{class:"section-title"},{default:t(()=>[d("头像上传")]),_:1}),c(g,{class:"upload-btn",onClick:m.chooseAvatar},{default:t(()=>[d("选择头像")]),_:1},8,["onClick"]),_.selectedAvatar?(l(),a(h,{key:0,src:_.selectedAvatar,class:"selected-avatar",mode:"aspectFit"},null,8,["src"])):i("",!0)]),_:1})]),_:1})]),_:1})}],["__scopeId","data-v-c95ddcff"]]);export{f as default};
