import{C as e,N as t,Z as r,a0 as s,T as o}from"./index-D2g-qjTN.js";import{a}from"./auth-manager.Cl_11pDL.js";import{U as n}from"./storage-manager.CvuXSRsG.js";var c=Object.defineProperty,i=(e=>(e.NETWORK="NETWORK",e.API="API",e.VALIDATION="VALIDATION",e.STORAGE="STORAGE",e.AUTH="AUTH",e.UNKNOWN="UNKNOWN",e))(i||{});const l={NETWORK:"网络连接异常，请检查网络",API:"服务器响应异常",VALIDATION:"数据验证失败",STORAGE:"本地存储异常",AUTH:"认证失败，请重新登录",UNKNOWN:"操作失败，请重试"},u={customMessage:"",showToast:!0,logError:!0,errorType:"UNKNOWN",context:null},g=(r,s={})=>{const o={...u,...s};if(o.logError&&console.error(`[${o.errorType}] ${o.customMessage||"操作失败"}:`,{error:r,context:o.context,timestamp:(new Date).toISOString()}),o.showToast){const t=o.customMessage||l[o.errorType];e({title:t,icon:"none",duration:2e3})}switch(o.errorType){case"AUTH":setTimeout(()=>{t({url:"/pages/login/login"})},1500);break;case"NETWORK":console.warn("建议检查网络连接")}};((e,t,r)=>{t in e?c(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r})(class{static record(e,t,r){this.errors.push({error:e,type:t,timestamp:(new Date).toISOString(),context:r}),this.errors.length>100&&(this.errors=this.errors.slice(-50))}static getReport(){return this.errors.reduce((e,t)=>{const r=e.find(e=>e.type===t.type);return r?(r.count++,r.latest=t.timestamp>r.latest?t.timestamp:r.latest):e.push({type:t.type,count:1,latest:t.timestamp}),e},[]).sort((e,t)=>t.count-e.count)}static clear(){this.errors=[]}},"errors"+"",[]);var m=Object.defineProperty,h=(e,t,r)=>(((e,t,r)=>{t in e?m(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r})(e,t+"",r),r);const p={showLoading:!0,showError:!0,customErrorMessage:"",enableCache:!1,cacheTimeout:3e5,retryCount:1,retryDelay:1e3};async function d(t,o={}){const a={...p,...o};try{a.showLoading&&r({title:"加载中...",mask:!0});const o=function(e){if(e&&"object"==typeof e&&"success"in e)return e;if(e&&e.statusCode){const{statusCode:t,data:r,errMsg:s}=e;return t>=200&&t<300?{success:!0,data:r,message:"请求成功"}:{success:!1,message:s||"请求失败",error:(null==r?void 0:r.message)||(null==r?void 0:r.error)||"未知错误",code:t}}return{success:!0,data:e,message:"请求成功"}}(await t());if(a.showLoading&&s(),!o.success&&a.showError){const t=a.customErrorMessage||o.message||"操作失败";e({title:t,icon:"none",duration:2e3}),g(o.error||t,{errorType:i.API,customMessage:t,showToast:!1})}return o}catch(n){a.showLoading&&s();const t=a.customErrorMessage||"网络连接失败";return a.showError&&e({title:t,icon:"none",duration:2e3}),((e,t)=>{g(e,{customMessage:t||"网络连接异常",errorType:"NETWORK"})})(n,t),{success:!1,error:t,message:t}}}new class{constructor(){h(this,"cache",new Map)}getCacheKey(e,t){return`${e}_${JSON.stringify(t||{})}`}set(e,t,r,s){const o=this.getCacheKey(e,t);this.cache.set(o,{data:r,timestamp:Date.now()+s})}get(e,t){const r=this.getCacheKey(e,t),s=this.cache.get(r);return s?Date.now()>s.timestamp?(this.cache.delete(r),null):s.data:null}clear(){this.cache.clear()}cleanup(){const e=Date.now();for(const[t,r]of this.cache.entries())e>r.timestamp&&this.cache.delete(t)}};const T=(t,r={})=>(...s)=>{if(n.isLoggedIn())return t(...s);(async(t={})=>{const{redirectUrl:r="",message:s="请先登录",showToast:n=!0,customMessage:c,redirectToLogin:i=!0}=t;try{if(console.log("🔍 检查用户登录状态..."),await a.getCurrentUser()&&a.isLoggedIn())return console.log("✅ 用户已登录:",a.getUsername()),!0;{console.log("❌ 用户未登录，跳转到登录页面"),(n||c)&&e({title:c||s,icon:"none",duration:1500});let t="/pages/login/login";return r&&(t+=`?redirect=${encodeURIComponent(r)}`),i&&setTimeout(()=>{o({url:t})},n||c?1500:0),!1}}catch(l){return console.error("检查登录状态失败:",l),(n||c)&&e({title:c||s,icon:"none",duration:1500}),i&&setTimeout(()=>{o({url:"/pages/login/login"})},n||c?1500:0),!1}})(r)},w=e=>{try{const t=new Date(e),r=(new Date).getTime()-t.getTime();if(r<0)return"刚刚";const s=Math.floor(r/1e3),o=Math.floor(s/60),a=Math.floor(o/60),n=Math.floor(a/24);return s<60?"刚刚":o<60?`${o}分钟前`:a<24?`${a}小时前`:n<7?`${n}天前`:y(t)}catch(t){return console.error("格式化时间失败:",t),"未知时间"}},y=(e,t="YYYY-MM-DD HH:mm")=>{try{const r="string"==typeof e?new Date(e):e,s=r.getFullYear(),o=String(r.getMonth()+1).padStart(2,"0"),a=String(r.getDate()).padStart(2,"0"),n=String(r.getHours()).padStart(2,"0"),c=String(r.getMinutes()).padStart(2,"0"),i=String(r.getSeconds()).padStart(2,"0");return t.replace("YYYY",s.toString()).replace("MM",o).replace("DD",a).replace("HH",n).replace("mm",c).replace("ss",i)}catch(r){return console.error("格式化日期失败:",r),"日期错误"}};export{d as a,w as f,T as w};
