import{B as e,u as s,h as t,l as a,c as o,w as c,i as l,o as n,a as r,p as u,e as i,f as d,n as g,t as m,z as f,b as v,F as p,r as _,C as h}from"./index-D2g-qjTN.js";import{g as y}from"./post.DjEMR5TD.js";import{P as b}from"./storage-manager.CvuXSRsG.js";import{_ as F}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./avatar.DQyLU1U7.js";async function w(){var s;console.log("🔍 开始检查posts表数据...");try{const t=e();console.log("📋 检查posts表是否存在...");const{data:a,error:o}=await t.from("posts").select("*").limit(1);if(o)return console.error("❌ posts表访问失败:",o),{success:!1,error:"表访问失败",detail:o.message};console.log("✅ posts表存在");const{count:c,error:l}=await t.from("posts").select("*",{count:"exact",head:!0});l?console.error("❌ 获取数据总数失败:",l):console.log(`📊 posts表中共有 ${c} 条数据`);const{data:n,error:r}=await t.from("posts").select("*").order("created_at",{ascending:!1}).limit(5);if(r)console.error("❌ 获取数据样本失败:",r);else if(console.log("📄 数据样本:",n),n&&n.length>0){console.log("📋 表结构字段:",Object.keys(n[0]));const e=n[0];console.log("🔑 关键字段检查:"),console.log("- id:",e.id),console.log("- content:",(null==(s=e.content)?void 0:s.substring(0,50))+"..."),console.log("- created_at:",e.created_at),console.log("- author_id:",e.author_id),console.log("- media:",e.media),console.log("- location:",e.location)}const{data:u,error:i}=await t.from("posts").select("\n        *,\n        users (\n          id,\n          nickname,\n          avatar_url\n        )\n      ").limit(3);return i?console.error("❌ 用户表关联失败:",i):console.log("👥 用户关联数据:",u),{success:!0,totalCount:c,sampleData:n,tableFields:n&&n.length>0?Object.keys(n[0]):[],userAssociation:u}}catch(t){return console.error("❌ 数据库检查异常:",t),{success:!1,error:t instanceof Error?t.message:"未知错误"}}}async function k(){console.log("🔐 开始检查数据库权限...");try{const t=e(),a=[{name:"基本查询",query:()=>t.from("posts").select("id").limit(1)},{name:"带关联查询",query:()=>t.from("posts").select("*, users(id)").limit(1)},{name:"计数查询",query:()=>t.from("posts").select("*",{count:"exact",head:!0})}],o=[];for(const e of a)try{const{data:s,error:t,count:a}=await e.query();o.push({test:e.name,success:!t,error:null==t?void 0:t.message,count:a,data:null==s?void 0:s.length}),t?console.error(`❌ ${e.name}失败:`,t):console.log(`✅ ${e.name}成功`)}catch(s){o.push({test:e.name,success:!1,error:s instanceof Error?s.message:"异常"})}return{success:o.every(e=>e.success),results:o}}catch(t){return console.error("❌ 权限检查异常:",t),{success:!1,error:t instanceof Error?t.message:"未知错误"}}}function x(){console.log("⚙️ 开始检查环境配置...");const e={supabaseUrl:"https://gcrsoruzrxompokkckii.supabase.co",hasAnonKey:!0,hasServiceKey:!0};console.log("📋 环境配置:"),console.log("- Supabase URL:",e.supabaseUrl),console.log("- 匿名密钥:",e.hasAnonKey?"已配置":"未配置"),console.log("- 服务角色密钥:",e.hasServiceKey?"已配置":"未配置");const s=e.supabaseUrl&&e.hasAnonKey;return{success:s,config:e,issues:s?[]:["环境变量配置不完整"]}}const D={checkPostsTable:w,checkDatabasePermissions:k,checkEnvironmentConfig:x,runFullDiagnosis:async function(){console.log("🚀 开始运行完整的数据库诊断...");const e={timestamp:(new Date).toISOString(),environment:x(),permissions:await k(),postsTable:await w()};console.log("📊 诊断结果汇总:"),console.log(JSON.stringify(e,null,2));const s=[];return e.environment.success||s.push("环境变量配置问题"),e.permissions.success||s.push("数据库权限问题"),e.postsTable.success?0===e.postsTable.totalCount&&s.push("posts表中没有数据"):s.push("posts表数据问题"),{success:0===s.length,issues:s,details:e}}};async function C(){console.log("🌱 开始插入测试数据...");try{const s=e(),{count:t}=await s.from("posts").select("*",{count:"exact",head:!0});if(t&&t>0)return console.log(`📊 数据库中已有 ${t} 条数据，跳过插入`),{success:!0,message:"已有数据，跳过插入",existingCount:t};const a="550e8400-e29b-41d4-a716-446655440000",{data:o}=await s.from("users").select("id").eq("id",a).single();if(!o)return console.log("⚠️ 测试用户不存在，需要先创建用户"),{success:!1,message:"测试用户不存在"};const c=[{content:"这是一条测试推荐内容 #1，今天天气真好，适合出去玩！",media:["https://via.placeholder.com/400x300/FF6B6B/FFFFFF?text=推荐1"],location:"北京市朝阳区",author_id:a,created_at:(new Date).toISOString()},{content:"测试推荐内容 #2，分享一个美食发现，这家店的火锅太好吃了！",media:["https://via.placeholder.com/400x300/4ECDC4/FFFFFF?text=推荐2"],location:"上海市黄浦区",author_id:a,created_at:new Date(Date.now()-36e5).toISOString()},{content:"测试推荐内容 #3，今天去爬山，风景太美了，推荐大家也去看看！",media:["https://via.placeholder.com/400x300/45B7D1/FFFFFF?text=推荐3"],location:"杭州市西湖区",author_id:a,created_at:new Date(Date.now()-72e5).toISOString()},{content:"测试推荐内容 #4，刚看完一部超赞的电影，强烈推荐给大家！",media:["https://via.placeholder.com/400x300/96CEB4/FFFFFF?text=推荐4"],location:"深圳市南山区",author_id:a,created_at:new Date(Date.now()-108e5).toISOString()},{content:"测试推荐内容 #5，今天学到了一个新技能，感觉很有成就感！",media:["https://via.placeholder.com/400x300/FECA57/FFFFFF?text=推荐5"],location:"广州市天河区",author_id:a,created_at:new Date(Date.now()-144e5).toISOString()}],{data:l,error:n}=await s.from("posts").insert(c).select();return n?(console.error("❌ 插入测试数据失败:",n),{success:!1,error:n.message}):(console.log(`✅ 成功插入 ${(null==l?void 0:l.length)||0} 条测试数据`),{success:!0,insertedCount:(null==l?void 0:l.length)||0,data:l})}catch(s){return console.error("❌ 插入测试数据异常:",s),{success:!1,error:s instanceof Error?s.message:"未知错误"}}}const S={seedTestPosts:C,setupTestEnvironment:async function(){console.log("🔧 开始设置测试环境...");try{const s=e(),{data:t,error:a}=await s.from("posts").select("id").limit(1);if(a)return console.error("❌ 数据库连接失败:",a),{success:!1,error:"数据库连接失败",detail:a.message};console.log("✅ 数据库连接成功");const{data:o,error:c}=await s.from("users").select("id").limit(1);if(c)return console.error("❌ users表访问失败:",c),{success:!1,error:"users表访问失败",detail:c.message};console.log(`✅ users表存在，共有 ${(null==o?void 0:o.length)||0} 条数据`);return{success:!0,environmentReady:!0,...await C()}}catch(s){return console.error("❌ 测试环境设置异常:",s),{success:!1,error:s instanceof Error?s.message:"未知错误"}}},cleanupTestData:async function(){console.log("🧹 开始清理测试数据...");try{const s=e(),{error:t}=await s.from("posts").delete().like("content","测试推荐内容%");return t?(console.error("❌ 清理测试数据失败:",t),{success:!1,error:t.message}):(console.log("✅ 测试数据清理完成"),{success:!0,message:"测试数据已清理"})}catch(s){return console.error("❌ 清理测试数据异常:",s),{success:!1,error:s instanceof Error?s.message:"未知错误"}}}},j=F(s({__name:"database-debug",setup(s){const F=t("loading"),w=t("检查中..."),k=t(""),x=t(0),C=t([]),j=t([]),E=t(""),O=t(!1),T=t(null);async function I(){E.value="";try{const e=await y(1,10);x.value=e.posts.length,j.value=e.posts.slice(0,3),0===e.posts.length&&(E.value="推荐栏目没有数据，请检查数据库是否有posts表数据")}catch(e){E.value="测试异常: "+e.message}}async function $(){E.value="";try{const s=e(),{data:t,count:a,error:o}=await s.from("posts").select("*",{count:"exact"});if(o)return void(E.value="查询失败: "+o.message);x.value=a||0,j.value=(null==t?void 0:t.slice(0,3))||[],t&&t.length>0&&(C.value=Object.keys(t[0]))}catch(s){E.value="查询异常: "+s.message}}function P(){b.clearPosts(),j.value=[],x.value=0,E.value="",h({title:"缓存已清除",icon:"success"})}async function q(){O.value=!0,E.value="";try{const e=await S.seedTestPosts();T.value=e,e.success?(h({title:`插入${e.insertedCount||0}条测试数据`,icon:"success"}),await I()):E.value="插入失败: "+(e.error||e.message||"未知错误")}catch(e){E.value="插入异常: "+e.message}finally{O.value=!1}}async function A(){try{const e=await S.cleanupTestData();e.success?(h({title:"测试数据已清理",icon:"success"}),await I()):E.value="清理失败: "+(e.error||"未知错误")}catch(e){E.value="清理异常: "+e.message}}async function B(){const e=await D.runFullDiagnosis();e.success?h({title:"诊断完成，数据库正常",icon:"success"}):E.value="发现问题: "+e.issues.join(", ")}return a(async()=>{await async function(){try{const s=e(),{data:t,error:a}=await s.from("posts").select("*").limit(1);return a?(F.value="error",w.value="连接失败",k.value=a.message,!1):(F.value="success",w.value="连接成功",k.value=`找到 ${(null==t?void 0:t.length)||0} 条数据`,t&&t.length>0&&(C.value=Object.keys(t[0])),!0)}catch(s){return F.value="error",w.value="连接异常",k.value=s.message||"未知错误",!1}}(),await I()}),(e,s)=>{const t=i,a=l,h=f;return n(),o(a,{class:"debug-container"},{default:c(()=>[r(a,{class:"debug-header"},{default:c(()=>[r(t,{class:"debug-title"},{default:c(()=>[d("🔍 数据库调试工具")]),_:1}),r(t,{class:"debug-subtitle"},{default:c(()=>[d("检查推荐栏目数据获取问题")]),_:1})]),_:1}),r(a,{class:"debug-section"},{default:c(()=>[r(t,{class:"section-title"},{default:c(()=>[d("📊 数据库连接状态")]),_:1}),r(a,{class:g(["status-card",F.value])},{default:c(()=>[r(t,{class:"status-text"},{default:c(()=>[d(m(w.value),1)]),_:1}),r(t,{class:"status-detail"},{default:c(()=>[d(m(k.value),1)]),_:1})]),_:1},8,["class"])]),_:1}),r(a,{class:"debug-section"},{default:c(()=>[r(t,{class:"section-title"},{default:c(()=>[d("📋 数据检查结果")]),_:1}),r(a,{class:"data-card"},{default:c(()=>[r(a,{class:"data-item"},{default:c(()=>[r(t,{class:"data-label"},{default:c(()=>[d("数据总数:")]),_:1}),r(t,{class:"data-value"},{default:c(()=>[d(m(x.value)+" 条",1)]),_:1})]),_:1}),r(a,{class:"data-item"},{default:c(()=>[r(t,{class:"data-label"},{default:c(()=>[d("表字段:")]),_:1}),r(t,{class:"data-value"},{default:c(()=>[d(m(C.value.join(", ")),1)]),_:1})]),_:1})]),_:1})]),_:1}),r(a,{class:"debug-section"},{default:c(()=>[r(t,{class:"section-title"},{default:c(()=>[d("🔧 测试数据获取")]),_:1}),r(h,{class:"test-button",onClick:I},{default:c(()=>[d("测试推荐数据")]),_:1}),r(h,{class:"test-button",onClick:$},{default:c(()=>[d("测试所有数据")]),_:1}),r(h,{class:"test-button",onClick:P},{default:c(()=>[d("清除缓存")]),_:1})]),_:1}),r(a,{class:"debug-section"},{default:c(()=>[r(t,{class:"section-title"},{default:c(()=>[d("🧪 测试数据管理")]),_:1}),r(h,{class:g(["test-button",{disabled:O.value}]),onClick:q,disabled:O.value},{default:c(()=>[d(m(O.value?"插入中...":"插入测试数据"),1)]),_:1},8,["disabled","class"]),r(h,{class:"test-button",onClick:A},{default:c(()=>[d("清理测试数据")]),_:1}),r(h,{class:"test-button",onClick:B},{default:c(()=>[d("运行完整诊断")]),_:1})]),_:1}),j.value.length>0?(n(),o(a,{key:0,class:"debug-section"},{default:c(()=>[r(t,{class:"section-title"},{default:c(()=>[d("📄 数据样本")]),_:1}),r(a,{class:"sample-list"},{default:c(()=>[(n(!0),v(p,null,_(j.value,(e,s)=>(n(),o(a,{key:s,class:"sample-item"},{default:c(()=>[r(t,{class:"sample-id"},{default:c(()=>[d("ID: "+m(e.id),1)]),_:2},1024),r(t,{class:"sample-content"},{default:c(()=>{var s;return[d(m(null==(s=e.content)?void 0:s.substring(0,50))+"...",1)]}),_:2},1024),r(t,{class:"sample-time"},{default:c(()=>[d(m(e.created_at),1)]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1})):u("",!0),E.value?(n(),o(a,{key:1,class:"debug-section"},{default:c(()=>[r(t,{class:"section-title"},{default:c(()=>[d("❌ 错误信息")]),_:1}),r(a,{class:"error-card"},{default:c(()=>[r(t,{class:"error-text"},{default:c(()=>[d(m(E.value),1)]),_:1})]),_:1})]),_:1})):u("",!0)]),_:1})}}}),[["__scopeId","data-v-1c5dc70e"]]);export{j as default};
