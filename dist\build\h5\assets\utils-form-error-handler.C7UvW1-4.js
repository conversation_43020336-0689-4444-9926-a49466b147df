import{C as r}from"./index-D2g-qjTN.js";class t{static handleError(t,e={}){const{showToast:i=!0,duration:a=2e3,icon:o="error"}=e;let s=this.normalizeErrorMessage(t);return i&&s&&r({title:s,icon:o,duration:a}),s}static normalizeErrorMessage(r){return"string"==typeof r?r:r instanceof Error||(null==r?void 0:r.message)?r.message:(null==r?void 0:r.error)?r.error:"操作失败，请重试"}static validatePhone(r){return r.trim()?/^1[3-9]\d{9}$/.test(r.trim())?null:"请输入正确的手机号格式":"请输入手机号"}static validatePassword(r){return r.trim()?r.length<6?"密码长度不能少于6位":null:"请输入密码"}static validateNickname(r){return r.trim()?r.trim().length<2||r.trim().length>20?"昵称长度应在2-20个字符之间":null:"请输入昵称"}static validateRegisterForm(r){const{phone:t,password:e,nickname:i,gender:a}=r,o=this.validatePhone(t);if(o)return o;const s=this.validatePassword(e);if(s)return s;const n=this.validateNickname(i);return n||(void 0!==a&&0!==a&&1!==a&&2!==a?"请选择正确的性别":null)}static validateLoginForm(r){const{phone:t,password:e}=r,i=this.validatePhone(t);if(i)return i;const a=this.validatePassword(e);return a||null}static clearFormError(r,t="error"){r&&void 0!==r[t]&&(r[t]="")}static setFormError(r,t,e="error"){r&&void 0!==r[e]&&(r[e]=t)}}export{t as FormErrorHandler};
