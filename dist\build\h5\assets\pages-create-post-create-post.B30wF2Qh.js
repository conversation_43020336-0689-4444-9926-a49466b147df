import{c as e,w as s,C as t,V as a,R as i,U as l,W as o,K as n,i as c,o as u,a as r,z as h,f as d,e as g,t as m,X as p,p as f,b,F as _,r as C,s as k}from"./index-D2g-qjTN.js";import{c as L}from"./post.DjEMR5TD.js";import{_ as v}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./avatar.DQyLU1U7.js";const I=v({name:"CreatePostPage",data:()=>({postContent:"",imageList:[],publishing:!1}),computed:{authStore:()=>n(),canPublish(){return this.postContent.trim().length>0}},mounted(){this.checkAuth()},methods:{checkAuth(){this.authStore.isLoggedIn||(t({title:"请先登录",icon:"none"}),setTimeout(()=>{a({url:"/pages/login/login"})},1500))},goBack(){l()},chooseImage(){o({count:9-this.imageList.length,success:e=>{this.imageList=[...this.imageList,...e.tempFilePaths]}})},removeImage(e){this.imageList.splice(e,1)},async publishPost(){if(this.canPublish&&!this.publishing){if(!this.authStore.isLoggedIn)return t({title:"请先登录",icon:"none"}),void setTimeout(()=>{a({url:"/pages/login/login"})},1500);this.publishing=!0;try{const e={content:this.postContent,images:[...this.imageList],location:null,user_id:this.authStore.userId},s=await L(e);if(!s.success)throw new Error(s.error||"发布失败");t({title:"发布成功",icon:"success"}),i("refreshFeed",!0),setTimeout(()=>{this.publishing=!1,l()},1500)}catch(e){console.error("发布失败:",e),this.publishing=!1,t({title:"发布失败，请重试",icon:"none"})}}}}},[["render",function(t,a,i,l,o,n){const L=h,v=g,I=c,P=p,j=k;return u(),e(I,{class:"create-post-container"},{default:s(()=>[r(I,{class:"header"},{default:s(()=>[r(L,{class:"cancel-btn",onClick:n.goBack},{default:s(()=>[d("取消")]),_:1},8,["onClick"]),r(v,{class:"title"},{default:s(()=>[d("发布动态")]),_:1}),r(L,{class:"publish-btn",disabled:!n.canPublish||o.publishing,onClick:n.publishPost},{default:s(()=>[d(m(o.publishing?"发布中...":"发布"),1)]),_:1},8,["disabled","onClick"])]),_:1}),r(I,{class:"content-area"},{default:s(()=>[r(P,{modelValue:o.postContent,"onUpdate:modelValue":a[0]||(a[0]=e=>o.postContent=e),placeholder:"分享你的生活...",class:"content-input",maxlength:"500"},null,8,["modelValue"]),r(I,{class:"content-info"},{default:s(()=>[r(v,{class:"char-count"},{default:s(()=>[d(m(o.postContent.length)+"/500",1)]),_:1})]),_:1}),r(I,{class:"media-section"},{default:s(()=>[r(I,{class:"image-upload"},{default:s(()=>[r(v,{class:"section-title"},{default:s(()=>[d("添加图片")]),_:1}),o.imageList.length>0?(u(),e(I,{key:0,class:"image-preview"},{default:s(()=>[(u(!0),b(_,null,C(o.imageList,(t,a)=>(u(),e(I,{key:a,class:"image-item"},{default:s(()=>[r(j,{src:t,mode:"aspectFill"},null,8,["src"]),r(v,{class:"remove-image",onClick:e=>n.removeImage(a)},{default:s(()=>[d("×")]),_:2},1032,["onClick"])]),_:2},1024))),128))]),_:1})):f("",!0),r(L,{class:"upload-btn",onClick:n.chooseImage,disabled:o.imageList.length>=9},{default:s(()=>[d(" + 添加图片 ")]),_:1},8,["onClick","disabled"])]),_:1})]),_:1})]),_:1})]),_:1})}],["__scopeId","data-v-3e7b8662"]]);export{I as default};
