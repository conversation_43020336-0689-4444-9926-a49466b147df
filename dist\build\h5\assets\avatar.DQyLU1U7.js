const t=(t="default")=>e(t),e=(t="default")=>{const e=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFEAA7","#DDA0DD","#98D8C8","#F7DC6F"],n=e[t.split("").reduce((t,e)=>t+e.charCodeAt(0),0)%e.length],s=t.charAt(0).toUpperCase();return`data:image/svg+xml;charset=utf-8,${encodeURIComponent(`<svg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg">\n    <circle cx="40" cy="40" r="40" fill="${n}"/>\n    <text x="40" y="50" font-family="Arial, sans-serif" font-size="32" font-weight="bold" text-anchor="middle" fill="white">${s}</text>\n  </svg>`)}`},n=(e,n="anonymous")=>{if(e&&"string"==typeof e&&e.trim()){if(e.startsWith("blob:")&&!e.includes("valid"))return console.warn("检测到无效的blob URL，使用默认头像:",e),t(n||"anonymous");if(e.startsWith("http")||e.startsWith("data:")||e.startsWith("/"))return e;console.warn("检测到无效的头像URL格式，使用默认头像:",e)}return t(n||"anonymous")};export{n as g};
