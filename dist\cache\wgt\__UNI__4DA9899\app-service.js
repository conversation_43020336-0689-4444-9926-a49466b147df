if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then(r=>t.resolve(e()).then(()=>r),r=>t.resolve(e()).then(()=>{throw r}))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const e=uni.requireGlobal();ArrayBuffer=e.<PERSON>,Int8Array=e.Int8Array,Uint8Array=e.Uint8Array,Uint8ClampedArray=e.Uint8ClampedArray,Int16Array=e.Int16Array,Uint16Array=e.Uint16Array,Int32Array=e.Int32Array,Uint32Array=e.Uint32Array,Float32Array=e.Float32Array,Float64Array=e.Float64Array,BigInt64Array=e.BigInt64Array,BigUint64Array=e.BigUint64Array}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(e){"use strict";function t(e,t,...r){uni.__log__?uni.__log__(e,t,...r):console[e].apply(console,[...r,t])}const r=t=>(r,s=e.getCurrentInstance())=>{!e.isInSSRComponentSetup&&e.injectHook(t,r,s)},s=r("onShow"),n=r("onHide"),i=r("onLaunch"),o=r("onUnload");
/*!
    * pinia v2.0.36
    * (c) 2023 Eduardo San Martin Morote
    * @license MIT
    */
let a;const c=e=>a=e,l=Symbol();function h(e){return e&&"object"==typeof e&&"[object Object]"===Object.prototype.toString.call(e)&&"function"!=typeof e.toJSON}var u,d;(d=u||(u={})).direct="direct",d.patchObject="patch object",d.patchFunction="patch function";const p=()=>{};function f(t,r,s,n=p){t.push(r);const i=()=>{const e=t.indexOf(r);e>-1&&(t.splice(e,1),n())};return!s&&e.getCurrentScope()&&e.onScopeDispose(i),i}function g(e,...t){e.slice().forEach(e=>{e(...t)})}function m(t,r){t instanceof Map&&r instanceof Map&&r.forEach((e,r)=>t.set(r,e)),t instanceof Set&&r instanceof Set&&r.forEach(t.add,t);for(const s in r){if(!r.hasOwnProperty(s))continue;const n=r[s],i=t[s];h(i)&&h(n)&&t.hasOwnProperty(s)&&!e.isRef(n)&&!e.isReactive(n)?t[s]=m(i,n):t[s]=n}return t}const v=Symbol();function y(e){return!h(e)||!e.hasOwnProperty(v)}const{assign:w}=Object;function _(t){return!(!e.isRef(t)||!t.effect)}function b(t,r,s={},n,i,o){let a;const l=w({actions:{}},s),h={deep:!0};let d,v,b,k=e.markRaw([]),S=e.markRaw([]);const E=n.state.value[t];let x;function T(r){let s;d=v=!1,"function"==typeof r?(r(n.state.value[t]),s={type:u.patchFunction,storeId:t,events:b}):(m(n.state.value[t],r),s={type:u.patchObject,payload:r,storeId:t,events:b});const i=x=Symbol();e.nextTick().then(()=>{x===i&&(d=!0)}),v=!0,g(k,s,n.state.value[t])}o||E||(n.state.value[t]={}),e.ref({});const C=o?function(){const{state:e}=s,t=e?e():{};this.$patch(e=>{w(e,t)})}:p;function A(e,r){return function(){c(n);const s=Array.from(arguments),i=[],o=[];let a;g(S,{args:s,name:e,store:B,after:function(e){i.push(e)},onError:function(e){o.push(e)}});try{a=r.apply(this&&this.$id===t?this:B,s)}catch(l){throw g(o,l),l}return a instanceof Promise?a.then(e=>(g(i,e),e)).catch(e=>(g(o,e),Promise.reject(e))):(g(i,a),a)}}const P={_p:n,$id:t,$onAction:f.bind(null,S),$patch:T,$reset:C,$subscribe(r,s={}){const i=f(k,r,s.detached,()=>o()),o=a.run(()=>e.watch(()=>n.state.value[t],e=>{("sync"===s.flush?v:d)&&r({storeId:t,type:u.direct,events:b},e)},w({},h,s)));return i},$dispose:function(){a.stop(),k=[],S=[],n._s.delete(t)}},B=e.reactive(P);n._s.set(t,B);const N=n._e.run(()=>(a=e.effectScope(),a.run(()=>r())));for(const c in N){const r=N[c];if(e.isRef(r)&&!_(r)||e.isReactive(r))o||(E&&y(r)&&(e.isRef(r)?r.value=E[c]:m(r,E[c])),n.state.value[t][c]=r);else if("function"==typeof r){const e=A(c,r);N[c]=e,l.actions[c]=r}}return w(B,N),w(e.toRaw(B),N),Object.defineProperty(B,"$state",{get:()=>n.state.value[t],set:e=>{T(t=>{w(t,e)})}}),n._p.forEach(e=>{w(B,a.run(()=>e({store:B,app:n._a,pinia:n,options:l})))}),E&&o&&s.hydrate&&s.hydrate(B.$state,E),d=!0,v=!0,B}const k=function(e,t,r){let s=Promise.resolve();return s.then(()=>e()).catch(e=>{const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e})};class S extends Error{constructor(e,t="FunctionsError",r){super(e),this.name=t,this.context=r}}class E extends S{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class x extends S{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class T extends S{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}var C,A;(A=C||(C={})).Any="any",A.ApNortheast1="ap-northeast-1",A.ApNortheast2="ap-northeast-2",A.ApSouth1="ap-south-1",A.ApSoutheast1="ap-southeast-1",A.ApSoutheast2="ap-southeast-2",A.CaCentral1="ca-central-1",A.EuCentral1="eu-central-1",A.EuWest1="eu-west-1",A.EuWest2="eu-west-2",A.EuWest3="eu-west-3",A.SaEast1="sa-east-1",A.UsEast1="us-east-1",A.UsWest1="us-west-1",A.UsWest2="us-west-2";var P=function(e,t,r,s){return new(r||(r=Promise))(function(n,i){function o(e){try{c(s.next(e))}catch(t){i(t)}}function a(e){try{c(s.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}c((s=s.apply(e,t||[])).next())})};class B{constructor(e,{headers:t={},customFetch:r,region:s=C.Any}={}){this.url=e,this.headers=t,this.region=s,this.fetch=(e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>k(()=>Promise.resolve().then(()=>W)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)})(r)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var r;return P(this,void 0,void 0,function*(){try{const{headers:s,method:n,body:i}=t;let o={},{region:a}=t;a||(a=this.region);const c=new URL(`${this.url}/${e}`);let l;a&&"any"!==a&&(o["x-region"]=a,c.searchParams.set("forceFunctionRegion",a)),i&&(s&&!Object.prototype.hasOwnProperty.call(s,"Content-Type")||!s)&&("undefined"!=typeof Blob&&i instanceof Blob||i instanceof ArrayBuffer?(o["Content-Type"]="application/octet-stream",l=i):"string"==typeof i?(o["Content-Type"]="text/plain",l=i):"undefined"!=typeof FormData&&i instanceof FormData?l=i:(o["Content-Type"]="application/json",l=JSON.stringify(i)));const h=yield this.fetch(c.toString(),{method:n||"POST",headers:Object.assign(Object.assign(Object.assign({},o),this.headers),s),body:l}).catch(e=>{throw new E(e)}),u=h.headers.get("x-relay-error");if(u&&"true"===u)throw new x(h);if(!h.ok)throw new T(h);let d,p=(null!==(r=h.headers.get("Content-Type"))&&void 0!==r?r:"text/plain").split(";")[0].trim();return d="application/json"===p?yield h.json():"application/octet-stream"===p?yield h.blob():"text/event-stream"===p?h:"multipart/form-data"===p?yield h.formData():yield h.text(),{data:d,error:null,response:h}}catch(s){return{data:null,error:s,response:s instanceof T||s instanceof x?s.context:void 0}}})}}var N="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function j(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function I(e){if(e.__esModule)return e;var t=e.default;if("function"==typeof t){var r=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach(function(t){var s=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,s.get?s:{enumerable:!0,get:function(){return e[t]}})}),r}var D={},O={},R={},V={},$={},L={},U=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}();const M=U.fetch,z=U.fetch.bind(U),F=U.Headers,H=U.Request,q=U.Response,W=Object.freeze(Object.defineProperty({__proto__:null,Headers:F,Request:H,Response:q,default:z,fetch:M},Symbol.toStringTag,{value:"Module"})),K=I(W);var J={};Object.defineProperty(J,"__esModule",{value:!0});let G=class extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}};J.default=G;var Y=N&&N.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(L,"__esModule",{value:!0});const X=Y(K),Q=Y(J);L.default=class{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=X.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let r=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,r,s;let n=null,i=null,o=null,a=e.status,c=e.statusText;if(e.ok){if("HEAD"!==this.method){const t=await e.text();""===t||(i="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}const s=null===(t=this.headers.Prefer)||void 0===t?void 0:t.match(/count=(exact|planned|estimated)/),l=null===(r=e.headers.get("content-range"))||void 0===r?void 0:r.split("/");s&&l&&l.length>1&&(o=parseInt(l[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(i)&&(i.length>1?(n={code:"PGRST116",details:`Results contain ${i.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},i=null,o=null,a=406,c="Not Acceptable"):i=1===i.length?i[0]:null)}else{const t=await e.text();try{n=JSON.parse(t),Array.isArray(n)&&404===e.status&&(i=[],n=null,a=200,c="OK")}catch(l){404===e.status&&""===t?(a=204,c="No Content"):n={message:t}}if(n&&this.isMaybeSingle&&(null===(s=null==n?void 0:n.details)||void 0===s?void 0:s.includes("0 rows"))&&(n=null,a=200,c="OK"),n&&this.shouldThrowOnError)throw new Q.default(n)}return{error:n,data:i,count:o,status:a,statusText:c}});return this.shouldThrowOnError||(r=r.catch(e=>{var t,r,s;return{error:{message:`${null!==(t=null==e?void 0:e.name)&&void 0!==t?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!==(r=null==e?void 0:e.stack)&&void 0!==r?r:""}`,hint:"",code:`${null!==(s=null==e?void 0:e.code)&&void 0!==s?s:""}`},data:null,count:null,status:0,statusText:""}})),r.then(e,t)}returns(){return this}overrideTypes(){return this}};var Z=N&&N.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty($,"__esModule",{value:!0});const ee=Z(L);let te=class extends ee.default{select(e){let t=!1;const r=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:r,foreignTable:s,referencedTable:n=s}={}){const i=n?`${n}.order`:"order",o=this.url.searchParams.get(i);return this.url.searchParams.set(i,`${o?`${o},`:""}${e}.${t?"asc":"desc"}${void 0===r?"":r?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:r=t}={}){const s=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(s,`${e}`),this}range(e,t,{foreignTable:r,referencedTable:s=r}={}){const n=void 0===s?"offset":`${s}.offset`,i=void 0===s?"limit":`${s}.limit`;return this.url.searchParams.set(n,`${e}`),this.url.searchParams.set(i,""+(t-e+1)),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:r=!1,buffers:s=!1,wal:n=!1,format:i="text"}={}){var o;const a=[e?"analyze":null,t?"verbose":null,r?"settings":null,s?"buffers":null,n?"wal":null].filter(Boolean).join("|"),c=null!==(o=this.headers.Accept)&&void 0!==o?o:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${i}; for="${c}"; options=${a};`,this}rollback(){var e;return(null!==(e=this.headers.Prefer)&&void 0!==e?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};$.default=te;var re=N&&N.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(V,"__esModule",{value:!0});const se=re($);let ne=class extends se.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){const r=Array.from(new Set(t)).map(e=>"string"==typeof e&&new RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${r})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:r,type:s}={}){let n="";"plain"===s?n="pl":"phrase"===s?n="ph":"websearch"===s&&(n="w");const i=void 0===r?"":`(${r})`;return this.url.searchParams.append(e,`${n}fts${i}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,r){return this.url.searchParams.append(e,`not.${t}.${r}`),this}or(e,{foreignTable:t,referencedTable:r=t}={}){const s=r?`${r}.or`:"or";return this.url.searchParams.append(s,`(${e})`),this}filter(e,t,r){return this.url.searchParams.append(e,`${t}.${r}`),this}};V.default=ne;var ie=N&&N.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(R,"__esModule",{value:!0});const oe=ie(V);R.default=class{constructor(e,{headers:t={},schema:r,fetch:s}){this.url=e,this.headers=t,this.schema=r,this.fetch=s}select(e,{head:t=!1,count:r}={}){const s=t?"HEAD":"GET";let n=!1;const i=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!n?"":('"'===e&&(n=!n),e)).join("");return this.url.searchParams.set("select",i),r&&(this.headers.Prefer=`count=${r}`),new oe.default({method:s,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:r=!0}={}){const s=[];if(this.headers.Prefer&&s.push(this.headers.Prefer),t&&s.push(`count=${t}`),r||s.push("missing=default"),this.headers.Prefer=s.join(","),Array.isArray(e)){const t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){const e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new oe.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:r=!1,count:s,defaultToNull:n=!0}={}){const i=[`resolution=${r?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&i.push(this.headers.Prefer),s&&i.push(`count=${s}`),n||i.push("missing=default"),this.headers.Prefer=i.join(","),Array.isArray(e)){const t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){const e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new oe.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){const r=[];return this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),this.headers.Prefer=r.join(","),new oe.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){const t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new oe.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};var ae={},ce={};Object.defineProperty(ce,"__esModule",{value:!0}),ce.version=void 0,ce.version="0.0.0-automated",Object.defineProperty(ae,"__esModule",{value:!0}),ae.DEFAULT_HEADERS=void 0;const le=ce;ae.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${le.version}`};var he=N&&N.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(O,"__esModule",{value:!0});const ue=he(R),de=he(V),pe=ae;O.default=class e{constructor(e,{headers:t={},schema:r,fetch:s}={}){this.url=e,this.headers=Object.assign(Object.assign({},pe.DEFAULT_HEADERS),t),this.schemaName=r,this.fetch=s}from(e){const t=new URL(`${this.url}/${e}`);return new ue.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(t){return new e(this.url,{headers:this.headers,schema:t,fetch:this.fetch})}rpc(e,t={},{head:r=!1,get:s=!1,count:n}={}){let i;const o=new URL(`${this.url}/rpc/${e}`);let a;r||s?(i=r?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{o.searchParams.append(e,t)})):(i="POST",a=t);const c=Object.assign({},this.headers);return n&&(c.Prefer=`count=${n}`),new de.default({method:i,url:o,headers:c,schema:this.schemaName,body:a,fetch:this.fetch,allowEmpty:!1})}};var fe=N&&N.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(D,"__esModule",{value:!0}),D.PostgrestError=D.PostgrestBuilder=D.PostgrestTransformBuilder=D.PostgrestFilterBuilder=D.PostgrestQueryBuilder=D.PostgrestClient=void 0;const ge=fe(O);D.PostgrestClient=ge.default;const me=fe(R);D.PostgrestQueryBuilder=me.default;const ve=fe(V);D.PostgrestFilterBuilder=ve.default;const ye=fe($);D.PostgrestTransformBuilder=ye.default;const we=fe(L);D.PostgrestBuilder=we.default;const _e=fe(J);D.PostgrestError=_e.default;var be=D.default={PostgrestClient:ge.default,PostgrestQueryBuilder:me.default,PostgrestFilterBuilder:ve.default,PostgrestTransformBuilder:ye.default,PostgrestBuilder:we.default,PostgrestError:_e.default};const{PostgrestClient:ke,PostgrestQueryBuilder:Se,PostgrestFilterBuilder:Ee,PostgrestTransformBuilder:xe,PostgrestBuilder:Te,PostgrestError:Ce}=be;class Ae{static detectEnvironment(){var e;if("undefined"!=typeof WebSocket)return{type:"native",constructor:WebSocket};if("undefined"!=typeof globalThis&&void 0!==globalThis.WebSocket)return{type:"native",constructor:globalThis.WebSocket};if("undefined"!=typeof global&&void 0!==global.WebSocket)return{type:"native",constructor:global.WebSocket};if("undefined"!=typeof globalThis&&void 0!==globalThis.WebSocketPair&&void 0===globalThis.WebSocket)return{type:"cloudflare",error:"Cloudflare Workers detected. WebSocket clients are not supported in Cloudflare Workers.",workaround:"Use Cloudflare Workers WebSocket API for server-side WebSocket handling, or deploy to a different runtime."};if("undefined"!=typeof globalThis&&globalThis.EdgeRuntime||"undefined"!=typeof navigator&&(null===(e=navigator.userAgent)||void 0===e?void 0:e.includes("Vercel-Edge")))return{type:"unsupported",error:"Edge runtime detected (Vercel Edge/Netlify Edge). WebSockets are not supported in edge functions.",workaround:"Use serverless functions or a different deployment target for WebSocket functionality."};if("undefined"!=typeof process&&process.versions&&process.versions.node){const e=parseInt(process.versions.node.split(".")[0]);return e>=22?void 0!==globalThis.WebSocket?{type:"native",constructor:globalThis.WebSocket}:{type:"unsupported",error:`Node.js ${e} detected but native WebSocket not found.`,workaround:"Provide a WebSocket implementation via the transport option."}:{type:"unsupported",error:`Node.js ${e} detected without native WebSocket support.`,workaround:'For Node.js < 22, install "ws" package and provide it via the transport option:\nimport ws from "ws"\nnew RealtimeClient(url, { transport: ws })'}}return{type:"unsupported",error:"Unknown JavaScript runtime without WebSocket support.",workaround:"Ensure you're running in a supported environment (browser, Node.js, Deno) or provide a custom WebSocket implementation."}}static getWebSocketConstructor(){const e=this.detectEnvironment();if(e.constructor)return e.constructor;let t=e.error||"WebSocket not supported in this environment.";throw e.workaround&&(t+=`\n\nSuggested solution: ${e.workaround}`),new Error(t)}static createWebSocket(e,t){return new(this.getWebSocketConstructor())(e,t)}static isWebSocketSupported(){try{const e=this.detectEnvironment();return"native"===e.type||"ws"===e.type}catch(e){return!1}}}const Pe=1e4;var Be,Ne,je,Ie,De,Oe,Re,Ve,$e,Le,Ue;(Ne=Be||(Be={}))[Ne.connecting=0]="connecting",Ne[Ne.open=1]="open",Ne[Ne.closing=2]="closing",Ne[Ne.closed=3]="closed",(Ie=je||(je={})).closed="closed",Ie.errored="errored",Ie.joined="joined",Ie.joining="joining",Ie.leaving="leaving",(Oe=De||(De={})).close="phx_close",Oe.error="phx_error",Oe.join="phx_join",Oe.reply="phx_reply",Oe.leave="phx_leave",Oe.access_token="access_token",(Re||(Re={})).websocket="websocket",($e=Ve||(Ve={})).Connecting="connecting",$e.Open="open",$e.Closing="closing",$e.Closed="closed";class Me{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t("string"==typeof e?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}_decodeBroadcast(e,t,r){const s=t.getUint8(1),n=t.getUint8(2);let i=this.HEADER_LENGTH+2;const o=r.decode(e.slice(i,i+s));i+=s;const a=r.decode(e.slice(i,i+n));i+=n;return{ref:null,topic:o,event:a,payload:JSON.parse(r.decode(e.slice(i,e.byteLength)))}}}class ze{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer),this.timer=void 0}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}(Ue=Le||(Le={})).abstime="abstime",Ue.bool="bool",Ue.date="date",Ue.daterange="daterange",Ue.float4="float4",Ue.float8="float8",Ue.int2="int2",Ue.int4="int4",Ue.int4range="int4range",Ue.int8="int8",Ue.int8range="int8range",Ue.json="json",Ue.jsonb="jsonb",Ue.money="money",Ue.numeric="numeric",Ue.oid="oid",Ue.reltime="reltime",Ue.text="text",Ue.time="time",Ue.timestamp="timestamp",Ue.timestamptz="timestamptz",Ue.timetz="timetz",Ue.tsrange="tsrange",Ue.tstzrange="tstzrange";const Fe=(e,t,r={})=>{var s;const n=null!==(s=r.skipTypes)&&void 0!==s?s:[];return Object.keys(t).reduce((r,s)=>(r[s]=He(s,e,t,n),r),{})},He=(e,t,r,s)=>{const n=t.find(t=>t.name===e),i=null==n?void 0:n.type,o=r[e];return i&&!s.includes(i)?qe(i,o):We(o)},qe=(e,t)=>{if("_"===e.charAt(0)){const r=e.slice(1,e.length);return Ye(t,r)}switch(e){case Le.bool:return Ke(t);case Le.float4:case Le.float8:case Le.int2:case Le.int4:case Le.int8:case Le.numeric:case Le.oid:return Je(t);case Le.json:case Le.jsonb:return Ge(t);case Le.timestamp:return Xe(t);case Le.abstime:case Le.date:case Le.daterange:case Le.int4range:case Le.int8range:case Le.money:case Le.reltime:case Le.text:case Le.time:case Le.timestamptz:case Le.timetz:case Le.tsrange:case Le.tstzrange:default:return We(t)}},We=e=>e,Ke=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},Je=e=>{if("string"==typeof e){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},Ge=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(t){return console.log(`JSON parse error: ${t}`),e}return e},Ye=(e,t)=>{if("string"!=typeof e)return e;const r=e.length-1,s=e[r];if("{"===e[0]&&"}"===s){let s;const i=e.slice(1,r);try{s=JSON.parse("["+i+"]")}catch(n){s=i?i.split(","):[]}return s.map(e=>qe(t,e))}return e},Xe=e=>"string"==typeof e?e.replace(" ","T"):e,Qe=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")+"/api/broadcast"};class Ze{constructor(e,t,r={},s=1e4){this.channel=e,this.event=t,this.payload=r,this.timeout=s,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var r;return this._hasReceived(e)&&t(null===(r=this.receivedResp)||void 0===r?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}var et,tt,rt,st,nt,it,ot,at;(tt=et||(et={})).SYNC="sync",tt.JOIN="join",tt.LEAVE="leave";class ct{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.enabled=!1,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const r=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},e=>{const{onJoin:t,onLeave:r,onSync:s}=this.caller;this.joinRef=this.channel._joinRef(),this.state=ct.syncState(this.state,e,t,r),this.pendingDiffs.forEach(e=>{this.state=ct.syncDiff(this.state,e,t,r)}),this.pendingDiffs=[],s()}),this.channel._on(r.diff,{},e=>{const{onJoin:t,onLeave:r,onSync:s}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=ct.syncDiff(this.state,e,t,r),s())}),this.onJoin((e,t,r)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:r})}),this.onLeave((e,t,r)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:r})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,r,s){const n=this.cloneDeep(e),i=this.transformState(t),o={},a={};return this.map(n,(e,t)=>{i[e]||(a[e]=t)}),this.map(i,(e,t)=>{const r=n[e];if(r){const s=t.map(e=>e.presence_ref),n=r.map(e=>e.presence_ref),i=t.filter(e=>n.indexOf(e.presence_ref)<0),c=r.filter(e=>s.indexOf(e.presence_ref)<0);i.length>0&&(o[e]=i),c.length>0&&(a[e]=c)}else o[e]=t}),this.syncDiff(n,{joins:o,leaves:a},r,s)}static syncDiff(e,t,r,s){const{joins:n,leaves:i}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return r||(r=()=>{}),s||(s=()=>{}),this.map(n,(t,s)=>{var n;const i=null!==(n=e[t])&&void 0!==n?n:[];if(e[t]=this.cloneDeep(s),i.length>0){const r=e[t].map(e=>e.presence_ref),s=i.filter(e=>r.indexOf(e.presence_ref)<0);e[t].unshift(...s)}r(t,i,s)}),this.map(i,(t,r)=>{let n=e[t];if(!n)return;const i=r.map(e=>e.presence_ref);n=n.filter(e=>i.indexOf(e.presence_ref)<0),e[t]=n,s(t,n,r),0===n.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(r=>t(r,e[r]))}static transformState(e){return e=this.cloneDeep(e),Object.getOwnPropertyNames(e).reduce((t,r)=>{const s=e[r];return t[r]="metas"in s?s.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):s,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}(st=rt||(rt={})).ALL="*",st.INSERT="INSERT",st.UPDATE="UPDATE",st.DELETE="DELETE",(it=nt||(nt={})).BROADCAST="broadcast",it.PRESENCE="presence",it.POSTGRES_CHANGES="postgres_changes",it.SYSTEM="system",(at=ot||(ot={})).SUBSCRIBED="SUBSCRIBED",at.TIMED_OUT="TIMED_OUT",at.CLOSED="CLOSED",at.CHANNEL_ERROR="CHANNEL_ERROR";class lt{constructor(e,t={config:{}},r){this.topic=e,this.params=t,this.socket=r,this.bindings={},this.state=je.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:"",enabled:!1},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new Ze(this,De.join,this.params,this.timeout),this.rejoinTimer=new ze(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=je.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=je.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=je.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=je.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("error",e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=je.errored,this.rejoinTimer.scheduleTimeout())}),this._on(De.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new ct(this),this.broadcastEndpointURL=Qe(this.socket.endPoint),this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var r,s;if(this.socket.isConnected()||this.socket.connect(),this.state==je.closed){const{config:{broadcast:n,presence:i,private:o}}=this.params,a=null!==(s=null===(r=this.bindings.postgres_changes)||void 0===r?void 0:r.map(e=>e.filter))&&void 0!==s?s:[],c=!!this.bindings[nt.PRESENCE]&&this.bindings[nt.PRESENCE].length>0,l={},h={broadcast:n,presence:Object.assign(Object.assign({},i),{enabled:c}),postgres_changes:a,private:o};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this._onError(t=>null==e?void 0:e(ot.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(ot.CLOSED)),this.updateJoinPayload(Object.assign({config:h},l)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var r;if(this.socket.setAuth(),void 0!==t){const s=this.bindings.postgres_changes,n=null!==(r=null==s?void 0:s.length)&&void 0!==r?r:0,i=[];for(let r=0;r<n;r++){const n=s[r],{filter:{event:o,schema:a,table:c,filter:l}}=n,h=t&&t[r];if(!h||h.event!==o||h.schema!==a||h.table!==c||h.filter!==l)return this.unsubscribe(),this.state=je.errored,void(null==e||e(ot.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes")));i.push(Object.assign(Object.assign({},n),{id:h.id}))}return this.bindings.postgres_changes=i,void(e&&e(ot.SUBSCRIBED))}null==e||e(ot.SUBSCRIBED)}).receive("error",t=>{this.state=je.errored,null==e||e(ot.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(ot.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,r){return this.state===je.joined&&e===nt.PRESENCE&&(this.socket.log("channel",`resubscribe to ${this.topic} due to change in presence callbacks on joined channel`),this.unsubscribe().then(()=>this.subscribe())),this._on(e,t,r)}async send(e,t={}){var r,s;if(this._canPush()||"broadcast"!==e.type)return new Promise(r=>{var s,n,i;const o=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null===(i=null===(n=null===(s=this.params)||void 0===s?void 0:s.config)||void 0===n?void 0:n.broadcast)||void 0===i?void 0:i.ack)||r("ok"),o.receive("ok",()=>r("ok")),o.receive("error",()=>r("error")),o.receive("timeout",()=>r("timed out"))});{const{event:i,payload:o}=e,a={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:i,payload:o,private:this.private}]})};try{const e=await this._fetchWithTimeout(this.broadcastEndpointURL,a,null!==(r=t.timeout)&&void 0!==r?r:this.timeout);return await(null===(s=e.body)||void 0===s?void 0:s.cancel()),e.ok?"ok":"error"}catch(n){return"AbortError"===n.name?"timed out":"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=je.leaving;const t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(De.close,"leave",this._joinRef())};this.joinPush.destroy();let r=null;return new Promise(s=>{r=new Ze(this,De.leave,{},e),r.receive("ok",()=>{t(),s("ok")}).receive("timeout",()=>{t(),s("timed out")}).receive("error",()=>{s("error")}),r.send(),this._canPush()||r.trigger("ok",{})}).finally(()=>{null==r||r.destroy()})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.pushBuffer=[],this.rejoinTimer.reset(),this.joinPush.destroy(),this.state=je.closed,this.bindings={}}async _fetchWithTimeout(e,t,r){const s=new AbortController,n=setTimeout(()=>s.abort(),r),i=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:s.signal}));return clearTimeout(n),i}_push(e,t,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let s=new Ze(this,e,t,r);return this._canPush()?s.send():this._addToPushBuffer(s),s}_addToPushBuffer(e){if(e.startTimeout(),this.pushBuffer.push(e),this.pushBuffer.length>100){const e=this.pushBuffer.shift();e&&(e.destroy(),this.socket.log("channel",`discarded push due to buffer overflow: ${e.event}`,e.payload))}}_onMessage(e,t,r){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,r){var s,n;const i=e.toLocaleLowerCase(),{close:o,error:a,leave:c,join:l}=De;if(r&&[o,a,c,l].indexOf(i)>=0&&r!==this._joinRef())return;let h=this._onMessage(i,t,r);if(t&&!h)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(i)?null===(s=this.bindings.postgres_changes)||void 0===s||s.filter(e=>{var t,r,s;return"*"===(null===(t=e.filter)||void 0===t?void 0:t.event)||(null===(s=null===(r=e.filter)||void 0===r?void 0:r.event)||void 0===s?void 0:s.toLocaleLowerCase())===i}).map(e=>e.callback(h,r)):null===(n=this.bindings[i])||void 0===n||n.filter(e=>{var r,s,n,o,a,c;if(["broadcast","presence","postgres_changes"].includes(i)){if("id"in e){const i=e.id,o=null===(r=e.filter)||void 0===r?void 0:r.event;return i&&(null===(s=t.ids)||void 0===s?void 0:s.includes(i))&&("*"===o||(null==o?void 0:o.toLocaleLowerCase())===(null===(n=t.data)||void 0===n?void 0:n.type.toLocaleLowerCase()))}{const r=null===(a=null===(o=null==e?void 0:e.filter)||void 0===o?void 0:o.event)||void 0===a?void 0:a.toLocaleLowerCase();return"*"===r||r===(null===(c=null==t?void 0:t.event)||void 0===c?void 0:c.toLocaleLowerCase())}}return e.type.toLocaleLowerCase()===i}).map(e=>{if("object"==typeof h&&"ids"in h){const e=h.data,{schema:t,table:r,commit_timestamp:s,type:n,errors:i}=e,o={schema:t,table:r,commit_timestamp:s,eventType:n,new:{},old:{},errors:i};h=Object.assign(Object.assign({},o),this._getPayloadRecords(e))}e.callback(h,r)})}_isClosed(){return this.state===je.closed}_isJoined(){return this.state===je.joined}_isJoining(){return this.state===je.joining}_isLeaving(){return this.state===je.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,r){const s=e.toLocaleLowerCase(),n={type:s,filter:t,callback:r};return this.bindings[s]?this.bindings[s].push(n):this.bindings[s]=[n],this}_off(e,t){const r=e.toLocaleLowerCase();return this.bindings[r]&&(this.bindings[r]=this.bindings[r].filter(e=>{var s;return!((null===(s=e.type)||void 0===s?void 0:s.toLocaleLowerCase())===r&&lt.isEqual(e.filter,t))})),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(e[r]!==t[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(De.close,{},e)}_onError(e){this._on(De.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=je.joining,this.joinPush.resend(e))}_getPayloadRecords(e){const t={new:{},old:{}};return"INSERT"!==e.type&&"UPDATE"!==e.type||(t.new=Fe(e.columns,e.record)),"UPDATE"!==e.type&&"DELETE"!==e.type||(t.old=Fe(e.columns,e.old_record)),t}}const ht=()=>{},ut=25e3,dt=10,pt=100,ft=[1e3,2e3,5e3,1e4];class gt{constructor(e,t){var r;if(this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=Pe,this.transport=null,this.heartbeatIntervalMs=ut,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=ht,this.ref=0,this.reconnectTimer=null,this.logger=ht,this.conn=null,this.sendBuffer=[],this.serializer=new Me,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._connectionState="disconnected",this._wasManualDisconnect=!1,this._authPromise=null,this._resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>k(()=>Promise.resolve().then(()=>W)).then(({default:t})=>t(...e)).catch(e=>{throw new Error(`Failed to load @supabase/node-fetch: ${e.message}. This is required for HTTP requests in Node.js environments without native fetch.`)}):fetch),(...e)=>t(...e)},!(null===(r=null==t?void 0:t.params)||void 0===r?void 0:r.apikey))throw new Error("API key is required to connect to Realtime");this.apiKey=t.params.apikey,this.endPoint=`${e}/${Re.websocket}`,this.httpEndpoint=Qe(e),this._initializeOptions(t),this._setupReconnectionTimer(),this.fetch=this._resolveFetch(null==t?void 0:t.fetch)}connect(){if(!(this.isConnecting()||this.isDisconnecting()||null!==this.conn&&this.isConnected())){if(this._setConnectionState("connecting"),this._setAuthSafely("connect"),this.transport)this.conn=new this.transport(this.endpointURL());else try{this.conn=Ae.createWebSocket(this.endpointURL())}catch(e){this._setConnectionState("disconnected");const t=e.message;if(t.includes("Node.js"))throw new Error(`${t}\n\nTo use Realtime in Node.js, you need to provide a WebSocket implementation:\n\nOption 1: Use Node.js 22+ which has native WebSocket support\nOption 2: Install and provide the "ws" package:\n\n  npm install ws\n\n  import ws from "ws"\n  const client = new RealtimeClient(url, {\n    ...options,\n    transport: ws\n  })`);throw new Error(`WebSocket not available: ${t}`)}this._setupConnectionHandlers()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){if(!this.isDisconnecting())if(this._setConnectionState("disconnecting",!0),this.conn){const r=setTimeout(()=>{this._setConnectionState("disconnected")},100);this.conn.onclose=()=>{clearTimeout(r),this._setConnectionState("disconnected")},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this._teardownConnection()}else this._setConnectionState("disconnected")}getChannels(){return this.channels}async removeChannel(e){const t=await e.unsubscribe();return 0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){const e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,t,r){this.logger(e,t,r)}connectionState(){switch(this.conn&&this.conn.readyState){case Be.connecting:return Ve.Connecting;case Be.open:return Ve.Open;case Be.closing:return Ve.Closing;default:return Ve.Closed}}isConnected(){return this.connectionState()===Ve.Open}isConnecting(){return"connecting"===this._connectionState}isDisconnecting(){return"disconnecting"===this._connectionState}channel(e,t={config:{}}){const r=`realtime:${e}`,s=this.getChannels().find(e=>e.topic===r);if(s)return s;{const r=new lt(`realtime:${e}`,t,this);return this.channels.push(r),r}}push(e){const{topic:t,event:r,payload:s,ref:n}=e,i=()=>{this.encode(e,e=>{var t;null===(t=this.conn)||void 0===t||t.send(e)})};this.log("push",`${t} ${r} (${n})`,s),this.isConnected()?i():this.sendBuffer.push(i)}async setAuth(e=null){this._authPromise=this._performAuth(e);try{await this._authPromise}finally{this._authPromise=null}}async sendHeartbeat(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef)return this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),this._wasManualDisconnect=!1,null===(e=this.conn)||void 0===e||e.close(1e3,"heartbeat timeout"),void setTimeout(()=>{var e;this.isConnected()||null===(e=this.reconnectTimer)||void 0===e||e.scheduleTimeout()},pt);this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),this._setAuthSafely("heartbeat")}else this.heartbeatCallback("disconnected")}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}_onConnMessage(e){this.decode(e.data,e=>{"phoenix"===e.topic&&"phx_reply"===e.event&&this.heartbeatCallback("ok"===e.payload.status?"ok":"error"),e.ref&&e.ref===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null);const{topic:t,event:r,payload:s,ref:n}=e,i=n?`(${n})`:"",o=s.status||"";this.log("receive",`${o} ${t} ${r} ${i}`.trim(),s),this.channels.filter(e=>e._isMember(t)).forEach(e=>e._trigger(r,s,n)),this._triggerStateCallbacks("message",e)})}_clearTimer(e){var t;"heartbeat"===e&&this.heartbeatTimer?(clearInterval(this.heartbeatTimer),this.heartbeatTimer=void 0):"reconnect"===e&&(null===(t=this.reconnectTimer)||void 0===t||t.reset())}_clearAllTimers(){this._clearTimer("heartbeat"),this._clearTimer("reconnect")}_setupConnectionHandlers(){this.conn&&("binaryType"in this.conn&&(this.conn.binaryType="arraybuffer"),this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_teardownConnection(){this.conn&&(this.conn.onopen=null,this.conn.onerror=null,this.conn.onmessage=null,this.conn.onclose=null,this.conn=null),this._clearAllTimers(),this.channels.forEach(e=>e.teardown())}_onConnOpen(){this._setConnectionState("connected"),this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this._clearTimer("reconnect"),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this._triggerStateCallbacks("open")}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(e){var t;this._setConnectionState("disconnected"),this.log("transport","close",e),this._triggerChanError(),this._clearTimer("heartbeat"),this._wasManualDisconnect||null===(t=this.reconnectTimer)||void 0===t||t.scheduleTimeout(),this._triggerStateCallbacks("close",e)}_onConnError(e){this._setConnectionState("disconnected"),this.log("transport",`${e}`),this._triggerChanError(),this._triggerStateCallbacks("error",e)}_triggerChanError(){this.channels.forEach(e=>e._trigger(De.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;const r=e.match(/\?/)?"&":"?";return`${e}${r}${new URLSearchParams(t)}`}_workerObjectUrl(e){let t;if(e)t=e;else{const e=new Blob(['\n  addEventListener("message", (e) => {\n    if (e.data.event === "start") {\n      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);\n    }\n  });'],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}_setConnectionState(e,t=!1){this._connectionState=e,"connecting"===e?this._wasManualDisconnect=!1:"disconnecting"===e&&(this._wasManualDisconnect=t)}async _performAuth(e=null){let t;t=e||(this.accessToken?await this.accessToken():this.accessTokenValue),this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach(e=>{const r={access_token:t,version:"realtime-js/2.15.1"};t&&e.updateJoinPayload(r),e.joinedOnce&&e._isJoined()&&e._push(De.access_token,{access_token:t})}))}async _waitForAuthIfNeeded(){this._authPromise&&await this._authPromise}_setAuthSafely(e="general"){this.setAuth().catch(t=>{this.log("error",`error setting auth in ${e}`,t)})}_triggerStateCallbacks(e,t){try{this.stateChangeCallbacks[e].forEach(r=>{try{r(t)}catch(s){this.log("error",`error in ${e} callback`,s)}})}catch(r){this.log("error",`error triggering ${e} callbacks`,r)}}_setupReconnectionTimer(){this.reconnectTimer=new ze(async()=>{setTimeout(async()=>{await this._waitForAuthIfNeeded(),this.isConnected()||this.connect()},dt)},this.reconnectAfterMs)}_initializeOptions(e){var t,r,s,n,i,o,a,c;if(this.transport=null!==(t=null==e?void 0:e.transport)&&void 0!==t?t:null,this.timeout=null!==(r=null==e?void 0:e.timeout)&&void 0!==r?r:Pe,this.heartbeatIntervalMs=null!==(s=null==e?void 0:e.heartbeatIntervalMs)&&void 0!==s?s:ut,this.worker=null!==(n=null==e?void 0:e.worker)&&void 0!==n&&n,this.accessToken=null!==(i=null==e?void 0:e.accessToken)&&void 0!==i?i:null,(null==e?void 0:e.params)&&(this.params=e.params),(null==e?void 0:e.logger)&&(this.logger=e.logger),((null==e?void 0:e.logLevel)||(null==e?void 0:e.log_level))&&(this.logLevel=e.logLevel||e.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),this.reconnectAfterMs=null!==(o=null==e?void 0:e.reconnectAfterMs)&&void 0!==o?o:e=>ft[e-1]||1e4,this.encode=null!==(a=null==e?void 0:e.encode)&&void 0!==a?a:(e,t)=>t(JSON.stringify(e)),this.decode=null!==(c=null==e?void 0:e.decode)&&void 0!==c?c:this.serializer.decode.bind(this.serializer),this.worker){if("undefined"!=typeof window&&!window.Worker)throw new Error("Web Worker is not supported");this.workerUrl=null==e?void 0:e.workerUrl}}}class mt extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function vt(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class yt extends mt{constructor(e,t,r){super(e),this.name="StorageApiError",this.status=t,this.statusCode=r}toJSON(){return{name:this.name,message:this.message,status:this.status,statusCode:this.statusCode}}}class wt extends mt{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}var _t=function(e,t,r,s){return new(r||(r=Promise))(function(n,i){function o(e){try{c(s.next(e))}catch(t){i(t)}}function a(e){try{c(s.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}c((s=s.apply(e,t||[])).next())})};const bt=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>k(()=>Promise.resolve().then(()=>W)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},kt=e=>{if(Array.isArray(e))return e.map(e=>kt(e));if("function"==typeof e||e!==Object(e))return e;const t={};return Object.entries(e).forEach(([e,r])=>{const s=e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""));t[s]=kt(r)}),t};var St=function(e,t,r,s){return new(r||(r=Promise))(function(n,i){function o(e){try{c(s.next(e))}catch(t){i(t)}}function a(e){try{c(s.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}c((s=s.apply(e,t||[])).next())})};const Et=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),xt=(e,t,r)=>St(void 0,void 0,void 0,function*(){const s=yield _t(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield k(()=>Promise.resolve().then(()=>W))).Response:Response});e instanceof s&&!(null==r?void 0:r.noResolveJson)?e.json().then(r=>{const s=e.status||500,n=(null==r?void 0:r.statusCode)||s+"";t(new yt(Et(r),s,n))}).catch(e=>{t(new wt(Et(e),e))}):t(new wt(Et(e),e))}),Tt=(e,t,r,s)=>{const n={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"!==e&&s?((e=>{if("object"!=typeof e||null===e)return!1;const t=Object.getPrototypeOf(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||Symbol.toStringTag in e||Symbol.iterator in e)})(s)?(n.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),n.body=JSON.stringify(s)):n.body=s,(null==t?void 0:t.duplex)&&(n.duplex=t.duplex),Object.assign(Object.assign({},n),r)):n};function Ct(e,t,r,s,n,i){return St(this,void 0,void 0,function*(){return new Promise((o,a)=>{e(r,Tt(t,s,n,i)).then(e=>{if(!e.ok)throw e;return(null==s?void 0:s.noResolveJson)?e:e.json()}).then(e=>o(e)).catch(e=>xt(e,a,s))})})}function At(e,t,r,s){return St(this,void 0,void 0,function*(){return Ct(e,"GET",t,r,s)})}function Pt(e,t,r,s,n){return St(this,void 0,void 0,function*(){return Ct(e,"POST",t,s,n,r)})}function Bt(e,t,r,s,n){return St(this,void 0,void 0,function*(){return Ct(e,"PUT",t,s,n,r)})}function Nt(e,t,r,s,n){return St(this,void 0,void 0,function*(){return Ct(e,"DELETE",t,s,n,r)})}var jt=function(e,t,r,s){return new(r||(r=Promise))(function(n,i){function o(e){try{c(s.next(e))}catch(t){i(t)}}function a(e){try{c(s.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}c((s=s.apply(e,t||[])).next())})};const It={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},Dt={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class Ot{constructor(e,t={},r,s){this.url=e,this.headers=t,this.bucketId=r,this.fetch=bt(s)}uploadOrUpdate(e,t,r,s){return jt(this,void 0,void 0,function*(){try{let n;const i=Object.assign(Object.assign({},Dt),s);let o=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(i.upsert)});const a=i.metadata;"undefined"!=typeof Blob&&r instanceof Blob?(n=new FormData,n.append("cacheControl",i.cacheControl),a&&n.append("metadata",this.encodeMetadata(a)),n.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(n=r,n.append("cacheControl",i.cacheControl),a&&n.append("metadata",this.encodeMetadata(a))):(n=r,o["cache-control"]=`max-age=${i.cacheControl}`,o["content-type"]=i.contentType,a&&(o["x-metadata"]=this.toBase64(this.encodeMetadata(a)))),(null==s?void 0:s.headers)&&(o=Object.assign(Object.assign({},o),s.headers));const c=this._removeEmptyFolders(t),l=this._getFinalPath(c),h=yield("PUT"==e?Bt:Pt)(this.fetch,`${this.url}/object/${l}`,n,Object.assign({headers:o},(null==i?void 0:i.duplex)?{duplex:i.duplex}:{}));return{data:{path:c,id:h.Id,fullPath:h.Key},error:null}}catch(n){if(vt(n))return{data:null,error:n};throw n}})}upload(e,t,r){return jt(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,r)})}uploadToSignedUrl(e,t,r,s){return jt(this,void 0,void 0,function*(){const n=this._removeEmptyFolders(e),i=this._getFinalPath(n),o=new URL(this.url+`/object/upload/sign/${i}`);o.searchParams.set("token",t);try{let e;const t=Object.assign({upsert:Dt.upsert},s),i=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&r instanceof Blob?(e=new FormData,e.append("cacheControl",t.cacheControl),e.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(e=r,e.append("cacheControl",t.cacheControl)):(e=r,i["cache-control"]=`max-age=${t.cacheControl}`,i["content-type"]=t.contentType);return{data:{path:n,fullPath:(yield Bt(this.fetch,o.toString(),e,{headers:i})).Key},error:null}}catch(a){if(vt(a))return{data:null,error:a};throw a}})}createSignedUploadUrl(e,t){return jt(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e);const s=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(s["x-upsert"]="true");const n=yield Pt(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:s}),i=new URL(this.url+n.url),o=i.searchParams.get("token");if(!o)throw new mt("No token returned by API");return{data:{signedUrl:i.toString(),path:e,token:o},error:null}}catch(r){if(vt(r))return{data:null,error:r};throw r}})}update(e,t,r){return jt(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,r)})}move(e,t,r){return jt(this,void 0,void 0,function*(){try{return{data:yield Pt(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(s){if(vt(s))return{data:null,error:s};throw s}})}copy(e,t,r){return jt(this,void 0,void 0,function*(){try{return{data:{path:(yield Pt(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(s){if(vt(s))return{data:null,error:s};throw s}})}createSignedUrl(e,t,r){return jt(this,void 0,void 0,function*(){try{let s=this._getFinalPath(e),n=yield Pt(this.fetch,`${this.url}/object/sign/${s}`,Object.assign({expiresIn:t},(null==r?void 0:r.transform)?{transform:r.transform}:{}),{headers:this.headers});const i=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return n={signedUrl:encodeURI(`${this.url}${n.signedURL}${i}`)},{data:n,error:null}}catch(s){if(vt(s))return{data:null,error:s};throw s}})}createSignedUrls(e,t,r){return jt(this,void 0,void 0,function*(){try{const s=yield Pt(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),n=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:s.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${n}`):null})),error:null}}catch(s){if(vt(s))return{data:null,error:s};throw s}})}download(e,t){return jt(this,void 0,void 0,function*(){const r=void 0!==(null==t?void 0:t.transform)?"render/image/authenticated":"object",s=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),n=s?`?${s}`:"";try{const t=this._getFinalPath(e),s=yield At(this.fetch,`${this.url}/${r}/${t}${n}`,{headers:this.headers,noResolveJson:!0});return{data:yield s.blob(),error:null}}catch(i){if(vt(i))return{data:null,error:i};throw i}})}info(e){return jt(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{const e=yield At(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:kt(e),error:null}}catch(r){if(vt(r))return{data:null,error:r};throw r}})}exists(e){return jt(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{return yield function(e,t,r,s){return St(this,void 0,void 0,function*(){return Ct(e,"HEAD",t,Object.assign(Object.assign({},r),{noResolveJson:!0}),s)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(r){if(vt(r)&&r instanceof wt){const e=r.originalError;if([400,404].includes(null==e?void 0:e.status))return{data:!1,error:r}}throw r}})}getPublicUrl(e,t){const r=this._getFinalPath(e),s=[],n=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==n&&s.push(n);const i=void 0!==(null==t?void 0:t.transform)?"render/image":"object",o=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==o&&s.push(o);let a=s.join("&");return""!==a&&(a=`?${a}`),{data:{publicUrl:encodeURI(`${this.url}/${i}/public/${r}${a}`)}}}remove(e){return jt(this,void 0,void 0,function*(){try{return{data:yield Nt(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(t){if(vt(t))return{data:null,error:t};throw t}})}list(e,t,r){return jt(this,void 0,void 0,function*(){try{const s=Object.assign(Object.assign(Object.assign({},It),t),{prefix:e||""});return{data:yield Pt(this.fetch,`${this.url}/object/list/${this.bucketId}`,s,{headers:this.headers},r),error:null}}catch(s){if(vt(s))return{data:null,error:s};throw s}})}listV2(e,t){return jt(this,void 0,void 0,function*(){try{const r=Object.assign({},e);return{data:yield Pt(this.fetch,`${this.url}/object/list-v2/${this.bucketId}`,r,{headers:this.headers},t),error:null}}catch(r){if(vt(r))return{data:null,error:r};throw r}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return"undefined"!=typeof Buffer?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e.replace(/^\/+/,"")}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}const Rt={"X-Client-Info":"storage-js/2.11.0"};var Vt=function(e,t,r,s){return new(r||(r=Promise))(function(n,i){function o(e){try{c(s.next(e))}catch(t){i(t)}}function a(e){try{c(s.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}c((s=s.apply(e,t||[])).next())})};class $t{constructor(e,t={},r,s){const n=new URL(e);if(null==s?void 0:s.useNewHostname){/supabase\.(co|in|red)$/.test(n.hostname)&&!n.hostname.includes("storage.supabase.")&&(n.hostname=n.hostname.replace("supabase.","storage.supabase."))}this.url=n.href,this.headers=Object.assign(Object.assign({},Rt),t),this.fetch=bt(r)}listBuckets(){return Vt(this,void 0,void 0,function*(){try{return{data:yield At(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(vt(e))return{data:null,error:e};throw e}})}getBucket(e){return Vt(this,void 0,void 0,function*(){try{return{data:yield At(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(t){if(vt(t))return{data:null,error:t};throw t}})}createBucket(e,t={public:!1}){return Vt(this,void 0,void 0,function*(){try{return{data:yield Pt(this.fetch,`${this.url}/bucket`,{id:e,name:e,type:t.type,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(vt(r))return{data:null,error:r};throw r}})}updateBucket(e,t){return Vt(this,void 0,void 0,function*(){try{return{data:yield Bt(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(vt(r))return{data:null,error:r};throw r}})}emptyBucket(e){return Vt(this,void 0,void 0,function*(){try{return{data:yield Pt(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(t){if(vt(t))return{data:null,error:t};throw t}})}deleteBucket(e){return Vt(this,void 0,void 0,function*(){try{return{data:yield Nt(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(t){if(vt(t))return{data:null,error:t};throw t}})}}class Lt extends $t{constructor(e,t={},r,s){super(e,t,r,s)}from(e){return new Ot(this.url,this.headers,e,this.fetch)}}let Ut="";Ut="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";const Mt={headers:{"X-Client-Info":`supabase-js-${Ut}/2.55.0`}},zt={schema:"public"},Ft={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},Ht={};var qt=function(e,t,r,s){return new(r||(r=Promise))(function(n,i){function o(e){try{c(s.next(e))}catch(t){i(t)}}function a(e){try{c(s.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}c((s=s.apply(e,t||[])).next())})};const Wt=(e,t,r)=>{const s=(e=>{let t;return t=e||("undefined"==typeof fetch?z:fetch),(...e)=>t(...e)})(r),n="undefined"==typeof Headers?F:Headers;return(r,i)=>qt(void 0,void 0,void 0,function*(){var o;const a=null!==(o=yield t())&&void 0!==o?o:e;let c=new n(null==i?void 0:i.headers);return c.has("apikey")||c.set("apikey",e),c.has("Authorization")||c.set("Authorization",`Bearer ${a}`),s(r,Object.assign(Object.assign({},i),{headers:c}))})};var Kt=function(e,t,r,s){return new(r||(r=Promise))(function(n,i){function o(e){try{c(s.next(e))}catch(t){i(t)}}function a(e){try{c(s.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}c((s=s.apply(e,t||[])).next())})};const Jt="2.71.1",Gt=3e4,Yt=9e4,Xt={"X-Client-Info":`gotrue-js/${Jt}`},Qt="X-Supabase-Api-Version",Zt={timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"},er=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;var tr=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,62,0,62,0,63,52,53,54,55,56,57,58,59,60,61,0,0,0,0,0,0,0,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,0,0,0,0,63,0,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51];const rr={getRandomValues(e){if(!(e instanceof Int8Array||e instanceof Uint8Array||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Uint8ClampedArray))throw new Error("Expected an integer array");if(e.byteLength>65536)throw new Error("Can only request a maximum of 65536 bytes");var t;return function(e,t){for(var r,s=e.length,n="="===e[s-2]?2:"="===e[s-1]?1:0,i=0,o=s-n&4294967292,a=0;a<o;a+=4)r=tr[e.charCodeAt(a)]<<18|tr[e.charCodeAt(a+1)]<<12|tr[e.charCodeAt(a+2)]<<6|tr[e.charCodeAt(a+3)],t[i++]=r>>16&255,t[i++]=r>>8&255,t[i++]=255&r;1===n&&(r=tr[e.charCodeAt(a)]<<10|tr[e.charCodeAt(a+1)]<<4|tr[e.charCodeAt(a+2)]>>2,t[i++]=r>>8&255,t[i++]=255&r),2===n&&(r=tr[e.charCodeAt(a)]<<2|tr[e.charCodeAt(a+1)]>>4,t[i++]=255&r)}((t="DCloud-Crypto",weex.requireModule(t)).getRandomValues(e.byteLength),new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e}};class sr extends Error{constructor(e,t,r){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=r}}function nr(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class ir extends sr{constructor(e,t,r){super(e,t,r),this.name="AuthApiError",this.status=t,this.code=r}}class or extends sr{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class ar extends sr{constructor(e,t,r,s){super(e,r,s),this.name=t,this.status=r}}class cr extends ar{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class lr extends ar{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class hr extends ar{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class ur extends ar{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class dr extends ar{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class pr extends ar{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function fr(e){return nr(e)&&"AuthRetryableFetchError"===e.name}class gr extends ar{constructor(e,t,r){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=r}}class mr extends ar{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}const vr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),yr=" \t\n\r=".split(""),wr=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<yr.length;t+=1)e[yr[t].charCodeAt(0)]=-2;for(let t=0;t<vr.length;t+=1)e[vr[t].charCodeAt(0)]=t;return e})();function _r(e,t,r){if(null!==e)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;){const e=t.queue>>t.queuedBits-6&63;r(vr[e]),t.queuedBits-=6}else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;){const e=t.queue>>t.queuedBits-6&63;r(vr[e]),t.queuedBits-=6}}function br(e,t,r){const s=wr[e];if(!(s>-1)){if(-2===s)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}for(t.queue=t.queue<<6|s,t.queuedBits+=6;t.queuedBits>=8;)r(t.queue>>t.queuedBits-8&255),t.queuedBits-=8}function kr(e){const t=[],r=e=>{t.push(String.fromCodePoint(e))},s={utf8seq:0,codepoint:0},n={queue:0,queuedBits:0},i=e=>{!function(e,t,r){if(0===t.utf8seq){if(e<=127)return void r(e);for(let r=1;r<6;r+=1)if(!(e>>7-r&1)){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else{if(4!==t.utf8seq)throw new Error("Invalid UTF-8 sequence");t.codepoint=7&e}t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}}(e,s,r)};for(let o=0;o<e.length;o+=1)br(e.charCodeAt(o),n,i);return t.join("")}function Sr(e,t){if(!(e<=127)){if(e<=2047)return t(192|e>>6),void t(128|63&e);if(e<=65535)return t(224|e>>12),t(128|e>>6&63),void t(128|63&e);if(e<=1114111)return t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),void t(128|63&e);throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}t(e)}function Er(e){const t=[],r={queue:0,queuedBits:0},s=e=>{t.push(e)};for(let n=0;n<e.length;n+=1)br(e.charCodeAt(n),r,s);return new Uint8Array(t)}function xr(e){const t=[];return function(e,t){for(let r=0;r<e.length;r+=1){let s=e.charCodeAt(r);if(s>55295&&s<=56319){const t=1024*(s-55296)&65535;s=65536+(e.charCodeAt(r+1)-56320&65535|t),r+=1}Sr(s,t)}}(e,e=>t.push(e)),new Uint8Array(t)}function Tr(e){const t=[],r={queue:0,queuedBits:0},s=e=>{t.push(e)};return e.forEach(e=>_r(e,r,s)),_r(null,r,s),t.join("")}const Cr=()=>"undefined"!=typeof window&&"undefined"!=typeof document,Ar={tested:!1,writable:!1},Pr=()=>{if(!Cr())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(t){return!1}if(Ar.tested)return Ar.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),Ar.tested=!0,Ar.writable=!0}catch(t){Ar.tested=!0,Ar.writable=!1}return Ar.writable};const Br=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>k(()=>Promise.resolve().then(()=>W)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},Nr=async(e,t,r)=>{await e.setItem(t,JSON.stringify(r))},jr=async(e,t)=>{const r=await e.getItem(t);if(!r)return null;try{return JSON.parse(r)}catch(s){return r}},Ir=async(e,t)=>{await e.removeItem(t)};class Dr{constructor(){this.promise=new Dr.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function Or(e){const t=e.split(".");if(3!==t.length)throw new mr("Invalid JWT structure");for(let r=0;r<t.length;r++)if(!er.test(t[r]))throw new mr("JWT not in base64url format");return{header:JSON.parse(kr(t[0])),payload:JSON.parse(kr(t[1])),signature:Er(t[2]),raw:{header:t[0],payload:t[1]}}}function Rr(e){return("0"+e.toString(16)).substr(-2)}async function Vr(e){if(!(void 0!==rr&&void 0!==rr.subtle&&"undefined"!=typeof TextEncoder))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const t=await async function(e){const t=(new TextEncoder).encode(e),r=await rr.subtle.digest("SHA-256",t),s=new Uint8Array(r);return Array.from(s).map(e=>String.fromCharCode(e)).join("")}(e);return btoa(t).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function $r(e,t,r=!1){const s=function(){const e=new Uint32Array(56);if(void 0===rr){const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length;let r="";for(let s=0;s<56;s++)r+=e.charAt(Math.floor(Math.random()*t));return r}return rr.getRandomValues(e),Array.from(e,Rr).join("")}();let n=s;r&&(n+="/PASSWORD_RECOVERY"),await Nr(e,`${t}-code-verifier`,n);const i=await Vr(s);return[i,s===i?"plain":"s256"]}Dr.promiseConstructor=Promise;const Lr=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;const Ur=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function Mr(e){if(!Ur.test(e))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}function zr(){return new Proxy({},{get:(e,t)=>{if("__isUserNotAvailableProxy"===t)return!0;if("symbol"==typeof t){const e=t.toString();if("Symbol(Symbol.toPrimitive)"===e||"Symbol(Symbol.toStringTag)"===e||"Symbol(util.inspect.custom)"===e)return}throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Accessing the "${t}" property of the session object is not supported. Please use getUser() instead.`)},set:(e,t)=>{throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Setting the "${t}" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`)},deleteProperty:(e,t)=>{throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Deleting the "${t}" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`)}})}function Fr(e){return JSON.parse(JSON.stringify(e))}const Hr=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),qr=[502,503,504];async function Wr(e){var t,r;if(!("object"==typeof(r=e)&&null!==r&&"status"in r&&"ok"in r&&"json"in r&&"function"==typeof r.json))throw new pr(Hr(e),0);if(qr.includes(e.status))throw new pr(Hr(e),e.status);let s,n;try{s=await e.json()}catch(o){throw new or(Hr(o),o)}const i=function(e){const t=e.headers.get(Qt);if(!t)return null;if(!t.match(Lr))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(o){return null}}(e);if(i&&i.getTime()>=Zt.timestamp&&"object"==typeof s&&s&&"string"==typeof s.code?n=s.code:"object"==typeof s&&s&&"string"==typeof s.error_code&&(n=s.error_code),n){if("weak_password"===n)throw new gr(Hr(s),e.status,(null===(t=s.weak_password)||void 0===t?void 0:t.reasons)||[]);if("session_not_found"===n)throw new cr}else if("object"==typeof s&&s&&"object"==typeof s.weak_password&&s.weak_password&&Array.isArray(s.weak_password.reasons)&&s.weak_password.reasons.length&&s.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new gr(Hr(s),e.status,s.weak_password.reasons);throw new ir(Hr(s),e.status||500,n)}async function Kr(e,t,r,s){var n;const i=Object.assign({},null==s?void 0:s.headers);i[Qt]||(i[Qt]=Zt.name),(null==s?void 0:s.jwt)&&(i.Authorization=`Bearer ${s.jwt}`);const o=null!==(n=null==s?void 0:s.query)&&void 0!==n?n:{};(null==s?void 0:s.redirectTo)&&(o.redirect_to=s.redirectTo);const a=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",c=await async function(e,t,r,s,n,i){const o=((e,t,r,s)=>{const n={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?n:(n.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),n.body=JSON.stringify(s),Object.assign(Object.assign({},n),r))})(t,s,n,i);let a;try{a=await e(r,Object.assign({},o))}catch(c){throw console.error(c),new pr(Hr(c),0)}a.ok||await Wr(a);if(null==s?void 0:s.noResolveJson)return a;try{return await a.json()}catch(c){await Wr(c)}}(e,t,r+a,{headers:i,noResolveJson:null==s?void 0:s.noResolveJson},{},null==s?void 0:s.body);return(null==s?void 0:s.xform)?null==s?void 0:s.xform(c):{data:Object.assign({},c),error:null}}function Jr(e){var t;let r=null;var s;(function(e){return e.access_token&&e.refresh_token&&e.expires_in})(e)&&(r=Object.assign({},e),e.expires_at||(r.expires_at=(s=e.expires_in,Math.round(Date.now()/1e3)+s)));return{data:{session:r,user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function Gr(e){const t=Jr(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function Yr(e){var t;return{data:{user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function Xr(e){return{data:e,error:null}}function Qr(e){const{action_link:t,email_otp:r,hashed_token:s,redirect_to:n,verification_type:i}=e,o=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.indexOf(s)<0&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(s=Object.getOwnPropertySymbols(e);n<s.length;n++)t.indexOf(s[n])<0&&Object.prototype.propertyIsEnumerable.call(e,s[n])&&(r[s[n]]=e[s[n]])}return r}(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]);return{data:{properties:{action_link:t,email_otp:r,hashed_token:s,redirect_to:n,verification_type:i},user:Object.assign({},o)},error:null}}function Zr(e){return e}const es=["global","local","others"];class ts{constructor({url:e="",headers:t={},fetch:r}){this.url=e,this.headers=t,this.fetch=Br(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t=es[0]){if(es.indexOf(t)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${es.join(", ")}`);try{return await Kr(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(r){if(nr(r))return{data:null,error:r};throw r}}async inviteUserByEmail(e,t={}){try{return await Kr(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:Yr})}catch(r){if(nr(r))return{data:{user:null},error:r};throw r}}async generateLink(e){try{const{options:t}=e,r=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.indexOf(s)<0&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(s=Object.getOwnPropertySymbols(e);n<s.length;n++)t.indexOf(s[n])<0&&Object.prototype.propertyIsEnumerable.call(e,s[n])&&(r[s[n]]=e[s[n]])}return r}(e,["options"]),s=Object.assign(Object.assign({},r),t);return"newEmail"in r&&(s.new_email=null==r?void 0:r.newEmail,delete s.newEmail),await Kr(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:s,headers:this.headers,xform:Qr,redirectTo:null==t?void 0:t.redirectTo})}catch(t){if(nr(t))return{data:{properties:null,user:null},error:t};throw t}}async createUser(e){try{return await Kr(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:Yr})}catch(t){if(nr(t))return{data:{user:null},error:t};throw t}}async listUsers(e){var t,r,s,n,i,o,a;try{const c={nextPage:null,lastPage:0,total:0},l=await Kr(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!==(r=null===(t=null==e?void 0:e.page)||void 0===t?void 0:t.toString())&&void 0!==r?r:"",per_page:null!==(n=null===(s=null==e?void 0:e.perPage)||void 0===s?void 0:s.toString())&&void 0!==n?n:""},xform:Zr});if(l.error)throw l.error;const h=await l.json(),u=null!==(i=l.headers.get("x-total-count"))&&void 0!==i?i:0,d=null!==(a=null===(o=l.headers.get("link"))||void 0===o?void 0:o.split(","))&&void 0!==a?a:[];return d.length>0&&(d.forEach(e=>{const t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),r=JSON.parse(e.split(";")[1].split("=")[1]);c[`${r}Page`]=t}),c.total=parseInt(u)),{data:Object.assign(Object.assign({},h),c),error:null}}catch(c){if(nr(c))return{data:{users:[]},error:c};throw c}}async getUserById(e){Mr(e);try{return await Kr(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:Yr})}catch(t){if(nr(t))return{data:{user:null},error:t};throw t}}async updateUserById(e,t){Mr(e);try{return await Kr(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:Yr})}catch(r){if(nr(r))return{data:{user:null},error:r};throw r}}async deleteUser(e,t=!1){Mr(e);try{return await Kr(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:Yr})}catch(r){if(nr(r))return{data:{user:null},error:r};throw r}}async _listFactors(e){Mr(e.userId);try{const{data:t,error:r}=await Kr(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:r}}catch(t){if(nr(t))return{data:null,error:t};throw t}}async _deleteFactor(e){Mr(e.userId),Mr(e.id);try{return{data:await Kr(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(t){if(nr(t))return{data:null,error:t};throw t}}}function rs(e={}){return{getItem:t=>e[t]||null,setItem:(t,r)=>{e[t]=r},removeItem:t=>{delete e[t]}}}const ss=!!(globalThis&&Pr()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"));class ns extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class is extends ns{}async function os(e,t,r){ss&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const s=new globalThis.AbortController;return t>0&&setTimeout(()=>{s.abort(),ss&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:s.signal},async s=>{if(!s){if(0===t)throw ss&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new is(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(ss)try{const e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(n){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",n)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await r()}ss&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,s.name);try{return await r()}finally{ss&&console.log("@supabase/gotrue-js: navigatorLock: released",e,s.name)}}))}!function(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}}();const as={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:Xt,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function cs(e,t,r){return await r()}const ls={};class hs{constructor(e){var t,r;this.userStorage=null,this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=hs.nextInstanceID,hs.nextInstanceID+=1,this.instanceID>0&&Cr()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const s=Object.assign(Object.assign({},as),e);if(this.logDebugMessages=!!s.debug,"function"==typeof s.debug&&(this.logger=s.debug),this.persistSession=s.persistSession,this.storageKey=s.storageKey,this.autoRefreshToken=s.autoRefreshToken,this.admin=new ts({url:s.url,headers:s.headers,fetch:s.fetch}),this.url=s.url,this.headers=s.headers,this.fetch=Br(s.fetch),this.lock=s.lock||cs,this.detectSessionInUrl=s.detectSessionInUrl,this.flowType=s.flowType,this.hasCustomAuthorizationHeader=s.hasCustomAuthorizationHeader,s.lock?this.lock=s.lock:Cr()&&(null===(t=null===globalThis||void 0===globalThis?void 0:globalThis.navigator)||void 0===t?void 0:t.locks)?this.lock=os:this.lock=cs,this.jwks||(this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER),this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?(s.storage?this.storage=s.storage:Pr()?this.storage=globalThis.localStorage:(this.memoryStorage={},this.storage=rs(this.memoryStorage)),s.userStorage&&(this.userStorage=s.userStorage)):(this.memoryStorage={},this.storage=rs(this.memoryStorage)),Cr()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(n){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",n)}null===(r=this.broadcastChannel)||void 0===r||r.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}get jwks(){var e,t;return null!==(t=null===(e=ls[this.storageKey])||void 0===e?void 0:e.jwks)&&void 0!==t?t:{keys:[]}}set jwks(e){ls[this.storageKey]=Object.assign(Object.assign({},ls[this.storageKey]),{jwks:e})}get jwks_cached_at(){var e,t;return null!==(t=null===(e=ls[this.storageKey])||void 0===e?void 0:e.cachedAt)&&void 0!==t?t:Number.MIN_SAFE_INTEGER}set jwks_cached_at(e){ls[this.storageKey]=Object.assign(Object.assign({},ls[this.storageKey]),{cachedAt:e})}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${Jt}) ${(new Date).toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{const t=function(e){const t={},r=new URL(e);if(r.hash&&"#"===r.hash[0])try{new URLSearchParams(r.hash.substring(1)).forEach((e,r)=>{t[r]=e})}catch(s){}return r.searchParams.forEach((e,r)=>{t[r]=e}),t}(window.location.href);let r="none";if(this._isImplicitGrantCallback(t)?r="implicit":await this._isPKCECallback(t)&&(r="pkce"),Cr()&&this.detectSessionInUrl&&"none"!==r){const{data:s,error:n}=await this._getSessionFromURL(t,r);if(n){if(this._debug("#_initialize()","error detecting session from URL",n),function(e){return nr(e)&&"AuthImplicitGrantRedirectError"===e.name}(n)){const t=null===(e=n.details)||void 0===e?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:n}}return await this._removeSession(),{error:n}}const{session:i,redirectType:o}=s;return this._debug("#_initialize()","detected session in URL",i,"redirect type",o),await this._saveSession(i),setTimeout(async()=>{"recovery"===o?await this._notifyAllSubscribers("PASSWORD_RECOVERY",i):await this._notifyAllSubscribers("SIGNED_IN",i)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(t){return nr(t)?{error:t}:{error:new or("Unexpected error during initialization",t)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,r,s;try{const n=await Kr(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!==(r=null===(t=null==e?void 0:e.options)||void 0===t?void 0:t.data)&&void 0!==r?r:{},gotrue_meta_security:{captcha_token:null===(s=null==e?void 0:e.options)||void 0===s?void 0:s.captchaToken}},xform:Jr}),{data:i,error:o}=n;if(o||!i)return{data:{user:null,session:null},error:o};const a=i.session,c=i.user;return i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:c,session:a},error:null}}catch(n){if(nr(n))return{data:{user:null,session:null},error:n};throw n}}async signUp(e){var t,r,s;try{let n;if("email"in e){const{email:r,password:s,options:i}=e;let o=null,a=null;"pkce"===this.flowType&&([o,a]=await $r(this.storage,this.storageKey)),n=await Kr(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==i?void 0:i.emailRedirectTo,body:{email:r,password:s,data:null!==(t=null==i?void 0:i.data)&&void 0!==t?t:{},gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken},code_challenge:o,code_challenge_method:a},xform:Jr})}else{if(!("phone"in e))throw new hr("You must provide either an email or phone number and a password");{const{phone:t,password:i,options:o}=e;n=await Kr(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:i,data:null!==(r=null==o?void 0:o.data)&&void 0!==r?r:{},channel:null!==(s=null==o?void 0:o.channel)&&void 0!==s?s:"sms",gotrue_meta_security:{captcha_token:null==o?void 0:o.captchaToken}},xform:Jr})}}const{data:i,error:o}=n;if(o||!i)return{data:{user:null,session:null},error:o};const a=i.session,c=i.user;return i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:c,session:a},error:null}}catch(n){if(nr(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithPassword(e){try{let t;if("email"in e){const{email:r,password:s,options:n}=e;t=await Kr(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:r,password:s,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},xform:Gr})}else{if(!("phone"in e))throw new hr("You must provide either an email or phone number and a password");{const{phone:r,password:s,options:n}=e;t=await Kr(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:r,password:s,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},xform:Gr})}}const{data:r,error:s}=t;return s?{data:{user:null,session:null},error:s}:r&&r.session&&r.user?(r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:s}):{data:{user:null,session:null},error:new lr}}catch(t){if(nr(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOAuth(e){var t,r,s,n;return await this._handleProviderSignIn(e.provider,{redirectTo:null===(t=e.options)||void 0===t?void 0:t.redirectTo,scopes:null===(r=e.options)||void 0===r?void 0:r.scopes,queryParams:null===(s=e.options)||void 0===s?void 0:s.queryParams,skipBrowserRedirect:null===(n=e.options)||void 0===n?void 0:n.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async signInWithWeb3(e){const{chain:t}=e;if("solana"===t)return await this.signInWithSolana(e);throw new Error(`@supabase/auth-js: Unsupported chain "${t}"`)}async signInWithSolana(e){var t,r,s,n,i,o,a,c,l,h,u,d;let p,f;if("message"in e)p=e.message,f=e.signature;else{const{chain:u,wallet:d,statement:g,options:m}=e;let v;if(Cr())if("object"==typeof d)v=d;else{const e=window;if(!("solana"in e)||"object"!=typeof e.solana||!("signIn"in e.solana&&"function"==typeof e.solana.signIn||"signMessage"in e.solana&&"function"==typeof e.solana.signMessage))throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.");v=e.solana}else{if("object"!=typeof d||!(null==m?void 0:m.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");v=d}const y=new URL(null!==(t=null==m?void 0:m.url)&&void 0!==t?t:window.location.href);if("signIn"in v&&v.signIn){const e=await v.signIn(Object.assign(Object.assign(Object.assign({issuedAt:(new Date).toISOString()},null==m?void 0:m.signInWithSolana),{version:"1",domain:y.host,uri:y.href}),g?{statement:g}:null));let t;if(Array.isArray(e)&&e[0]&&"object"==typeof e[0])t=e[0];else{if(!(e&&"object"==typeof e&&"signedMessage"in e&&"signature"in e))throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");t=e}if(!("signedMessage"in t&&"signature"in t&&("string"==typeof t.signedMessage||t.signedMessage instanceof Uint8Array)&&t.signature instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields");p="string"==typeof t.signedMessage?t.signedMessage:(new TextDecoder).decode(t.signedMessage),f=t.signature}else{if(!("signMessage"in v&&"function"==typeof v.signMessage&&"publicKey"in v&&"object"==typeof v&&v.publicKey&&"toBase58"in v.publicKey&&"function"==typeof v.publicKey.toBase58))throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");p=[`${y.host} wants you to sign in with your Solana account:`,v.publicKey.toBase58(),...g?["",g,""]:[""],"Version: 1",`URI: ${y.href}`,`Issued At: ${null!==(s=null===(r=null==m?void 0:m.signInWithSolana)||void 0===r?void 0:r.issuedAt)&&void 0!==s?s:(new Date).toISOString()}`,...(null===(n=null==m?void 0:m.signInWithSolana)||void 0===n?void 0:n.notBefore)?[`Not Before: ${m.signInWithSolana.notBefore}`]:[],...(null===(i=null==m?void 0:m.signInWithSolana)||void 0===i?void 0:i.expirationTime)?[`Expiration Time: ${m.signInWithSolana.expirationTime}`]:[],...(null===(o=null==m?void 0:m.signInWithSolana)||void 0===o?void 0:o.chainId)?[`Chain ID: ${m.signInWithSolana.chainId}`]:[],...(null===(a=null==m?void 0:m.signInWithSolana)||void 0===a?void 0:a.nonce)?[`Nonce: ${m.signInWithSolana.nonce}`]:[],...(null===(c=null==m?void 0:m.signInWithSolana)||void 0===c?void 0:c.requestId)?[`Request ID: ${m.signInWithSolana.requestId}`]:[],...(null===(h=null===(l=null==m?void 0:m.signInWithSolana)||void 0===l?void 0:l.resources)||void 0===h?void 0:h.length)?["Resources",...m.signInWithSolana.resources.map(e=>`- ${e}`)]:[]].join("\n");const e=await v.signMessage((new TextEncoder).encode(p),"utf8");if(!(e&&e instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");f=e}}try{const{data:t,error:r}=await Kr(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:p,signature:Tr(f)},(null===(u=e.options)||void 0===u?void 0:u.captchaToken)?{gotrue_meta_security:{captcha_token:null===(d=e.options)||void 0===d?void 0:d.captchaToken}}:null),xform:Jr});if(r)throw r;return t&&t.session&&t.user?(t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign({},t),error:r}):{data:{user:null,session:null},error:new lr}}catch(g){if(nr(g))return{data:{user:null,session:null},error:g};throw g}}async _exchangeCodeForSession(e){const t=await jr(this.storage,`${this.storageKey}-code-verifier`),[r,s]=(null!=t?t:"").split("/");try{const{data:t,error:n}=await Kr(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:r},xform:Jr});if(await Ir(this.storage,`${this.storageKey}-code-verifier`),n)throw n;return t&&t.session&&t.user?(t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=s?s:null}),error:n}):{data:{user:null,session:null,redirectType:null},error:new lr}}catch(n){if(nr(n))return{data:{user:null,session:null,redirectType:null},error:n};throw n}}async signInWithIdToken(e){try{const{options:t,provider:r,token:s,access_token:n,nonce:i}=e,o=await Kr(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:s,access_token:n,nonce:i,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:Jr}),{data:a,error:c}=o;return c?{data:{user:null,session:null},error:c}:a&&a.session&&a.user?(a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",a.session)),{data:a,error:c}):{data:{user:null,session:null},error:new lr}}catch(t){if(nr(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOtp(e){var t,r,s,n,i;try{if("email"in e){const{email:s,options:n}=e;let i=null,o=null;"pkce"===this.flowType&&([i,o]=await $r(this.storage,this.storageKey));const{error:a}=await Kr(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:s,data:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:{},create_user:null===(r=null==n?void 0:n.shouldCreateUser)||void 0===r||r,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken},code_challenge:i,code_challenge_method:o},redirectTo:null==n?void 0:n.emailRedirectTo});return{data:{user:null,session:null},error:a}}if("phone"in e){const{phone:t,options:r}=e,{data:o,error:a}=await Kr(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!==(s=null==r?void 0:r.data)&&void 0!==s?s:{},create_user:null===(n=null==r?void 0:r.shouldCreateUser)||void 0===n||n,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken},channel:null!==(i=null==r?void 0:r.channel)&&void 0!==i?i:"sms"}});return{data:{user:null,session:null,messageId:null==o?void 0:o.message_id},error:a}}throw new hr("You must provide either an email or phone number.")}catch(o){if(nr(o))return{data:{user:null,session:null},error:o};throw o}}async verifyOtp(e){var t,r;try{let s,n;"options"in e&&(s=null===(t=e.options)||void 0===t?void 0:t.redirectTo,n=null===(r=e.options)||void 0===r?void 0:r.captchaToken);const{data:i,error:o}=await Kr(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:n}}),redirectTo:s,xform:Jr});if(o)throw o;if(!i)throw new Error("An error occurred on token verification.");const a=i.session,c=i.user;return(null==a?void 0:a.access_token)&&(await this._saveSession(a),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",a)),{data:{user:c,session:a},error:null}}catch(s){if(nr(s))return{data:{user:null,session:null},error:s};throw s}}async signInWithSSO(e){var t,r,s;try{let n=null,i=null;return"pkce"===this.flowType&&([n,i]=await $r(this.storage,this.storageKey)),await Kr(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!==(r=null===(t=e.options)||void 0===t?void 0:t.redirectTo)&&void 0!==r?r:void 0}),(null===(s=null==e?void 0:e.options)||void 0===s?void 0:s.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:n,code_challenge_method:i}),headers:this.headers,xform:Xr})}catch(n){if(nr(n))return{data:null,error:n};throw n}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{const{data:{session:t},error:r}=e;if(r)throw r;if(!t)throw new cr;const{error:s}=await Kr(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:s}})}catch(e){if(nr(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{const t=`${this.url}/resend`;if("email"in e){const{email:r,type:s,options:n}=e,{error:i}=await Kr(this.fetch,"POST",t,{headers:this.headers,body:{email:r,type:s,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},redirectTo:null==n?void 0:n.emailRedirectTo});return{data:{user:null,session:null},error:i}}if("phone"in e){const{phone:r,type:s,options:n}=e,{data:i,error:o}=await Kr(this.fetch,"POST",t,{headers:this.headers,body:{phone:r,type:s,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}}});return{data:{user:null,session:null,messageId:null==i?void 0:i.message_id},error:o}}throw new hr("You must provide either an email or phone number and a type")}catch(t){if(nr(t))return{data:{user:null,session:null},error:t};throw t}}async getSession(){await this.initializePromise;return await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){const e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),r=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await r}catch(e){}})()),r}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(t){}})()),await e;this.pendingInLock.length;){const e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{const t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",(new Error).stack);try{let e=null;const t=await jr(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};const r=!!e.expires_at&&1e3*e.expires_at-Date.now()<Yt;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",e.expires_at),!r){if(this.userStorage){const t=await jr(this.userStorage,this.storageKey+"-user");(null==t?void 0:t.user)?e.user=t.user:e.user=zr()}if(this.storage.isServer&&e.user){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,r,s)=>(t||"user"!==r||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,r,s))})}return{data:{session:e},error:null}}const{session:s,error:n}=await this._callRefreshToken(e.refresh_token);return n?{data:{session:null},error:n}:{data:{session:s},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){if(e)return await this._getUser(e);await this.initializePromise;return await this._acquireLock(-1,async()=>await this._getUser())}async _getUser(e){try{return e?await Kr(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:Yr}):await this._useSession(async e=>{var t,r,s;const{data:n,error:i}=e;if(i)throw i;return(null===(t=n.session)||void 0===t?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await Kr(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!==(s=null===(r=n.session)||void 0===r?void 0:r.access_token)&&void 0!==s?s:void 0,xform:Yr}):{data:{user:null},error:new cr}})}catch(t){if(nr(t))return function(e){return nr(e)&&"AuthSessionMissingError"===e.name}(t)&&(await this._removeSession(),await Ir(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:t};throw t}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async r=>{const{data:s,error:n}=r;if(n)throw n;if(!s.session)throw new cr;const i=s.session;let o=null,a=null;"pkce"===this.flowType&&null!=e.email&&([o,a]=await $r(this.storage,this.storageKey));const{data:c,error:l}=await Kr(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:o,code_challenge_method:a}),jwt:i.access_token,xform:Yr});if(l)throw l;return i.user=c.user,await this._saveSession(i),await this._notifyAllSubscribers("USER_UPDATED",i),{data:{user:i.user},error:null}})}catch(r){if(nr(r))return{data:{user:null},error:r};throw r}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new cr;const t=Date.now()/1e3;let r=t,s=!0,n=null;const{payload:i}=Or(e.access_token);if(i.exp&&(r=i.exp,s=r<=t),s){const{session:t,error:r}=await this._callRefreshToken(e.refresh_token);if(r)return{data:{user:null,session:null},error:r};if(!t)return{data:{user:null,session:null},error:null};n=t}else{const{data:s,error:i}=await this._getUser(e.access_token);if(i)throw i;n={access_token:e.access_token,refresh_token:e.refresh_token,user:s.user,token_type:"bearer",expires_in:r-t,expires_at:r},await this._saveSession(n),await this._notifyAllSubscribers("SIGNED_IN",n)}return{data:{user:n.user,session:n},error:null}}catch(t){if(nr(t))return{data:{session:null,user:null},error:t};throw t}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var r;if(!e){const{data:s,error:n}=t;if(n)throw n;e=null!==(r=s.session)&&void 0!==r?r:void 0}if(!(null==e?void 0:e.refresh_token))throw new cr;const{session:s,error:n}=await this._callRefreshToken(e.refresh_token);return n?{data:{user:null,session:null},error:n}:s?{data:{user:s.user,session:s},error:null}:{data:{user:null,session:null},error:null}})}catch(t){if(nr(t))return{data:{user:null,session:null},error:t};throw t}}async _getSessionFromURL(e,t){try{if(!Cr())throw new ur("No browser detected.");if(e.error||e.error_description||e.error_code)throw new ur(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new dr("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new ur("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new dr("No code detected.");const{data:t,error:r}=await this._exchangeCodeForSession(e.code);if(r)throw r;const s=new URL(window.location.href);return s.searchParams.delete("code"),window.history.replaceState(window.history.state,"",s.toString()),{data:{session:t.session,redirectType:null},error:null}}const{provider_token:r,provider_refresh_token:s,access_token:n,refresh_token:i,expires_in:o,expires_at:a,token_type:c}=e;if(!(n&&o&&i&&c))throw new ur("No session defined in URL");const l=Math.round(Date.now()/1e3),h=parseInt(o);let u=l+h;a&&(u=parseInt(a));const d=u-l;1e3*d<=Gt&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${d}s, should have been closer to ${h}s`);const p=u-h;l-p>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",p,u,l):l-p<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",p,u,l);const{data:f,error:g}=await this._getUser(n);if(g)throw g;const m={provider_token:r,provider_refresh_token:s,access_token:n,expires_in:h,expires_at:u,refresh_token:i,token_type:c,user:f.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:m,redirectType:e.type},error:null}}catch(r){if(nr(r))return{data:{session:null,redirectType:null},error:r};throw r}}_isImplicitGrantCallback(e){return Boolean(e.access_token||e.error_description)}async _isPKCECallback(e){const t=await jr(this.storage,`${this.storageKey}-code-verifier`);return!(!e.code||!t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var r;const{data:s,error:n}=t;if(n)return{error:n};const i=null===(r=s.session)||void 0===r?void 0:r.access_token;if(i){const{error:t}=await this.admin.signOut(i,e);if(t&&(!function(e){return nr(e)&&"AuthApiError"===e.name}(t)||404!==t.status&&401!==t.status&&403!==t.status))return{error:t}}return"others"!==e&&(await this._removeSession(),await Ir(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){const t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),r={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,r),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:r}}}async _emitInitialSession(e){return await this._useSession(async t=>{var r,s;try{const{data:{session:s},error:n}=t;if(n)throw n;await(null===(r=this.stateChangeEmitters.get(e))||void 0===r?void 0:r.callback("INITIAL_SESSION",s)),this._debug("INITIAL_SESSION","callback id",e,"session",s)}catch(n){await(null===(s=this.stateChangeEmitters.get(e))||void 0===s?void 0:s.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",n),console.error(n)}})}async resetPasswordForEmail(e,t={}){let r=null,s=null;"pkce"===this.flowType&&([r,s]=await $r(this.storage,this.storageKey,!0));try{return await Kr(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:r,code_challenge_method:s,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(n){if(nr(n))return{data:null,error:n};throw n}}async getUserIdentities(){var e;try{const{data:t,error:r}=await this.getUser();if(r)throw r;return{data:{identities:null!==(e=t.user.identities)&&void 0!==e?e:[]},error:null}}catch(t){if(nr(t))return{data:null,error:t};throw t}}async linkIdentity(e){var t;try{const{data:r,error:s}=await this._useSession(async t=>{var r,s,n,i,o;const{data:a,error:c}=t;if(c)throw c;const l=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null===(r=e.options)||void 0===r?void 0:r.redirectTo,scopes:null===(s=e.options)||void 0===s?void 0:s.scopes,queryParams:null===(n=e.options)||void 0===n?void 0:n.queryParams,skipBrowserRedirect:!0});return await Kr(this.fetch,"GET",l,{headers:this.headers,jwt:null!==(o=null===(i=a.session)||void 0===i?void 0:i.access_token)&&void 0!==o?o:void 0})});if(s)throw s;return Cr()&&!(null===(t=e.options)||void 0===t?void 0:t.skipBrowserRedirect)&&window.location.assign(null==r?void 0:r.url),{data:{provider:e.provider,url:null==r?void 0:r.url},error:null}}catch(r){if(nr(r))return{data:{provider:e.provider,url:null},error:r};throw r}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var r,s;const{data:n,error:i}=t;if(i)throw i;return await Kr(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!==(s=null===(r=n.session)||void 0===r?void 0:r.access_token)&&void 0!==s?s:void 0})})}catch(t){if(nr(t))return{data:null,error:t};throw t}}async _refreshAccessToken(e){const t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{const n=Date.now();return await(r=async r=>(r>0&&await async function(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}(200*Math.pow(2,r-1)),this._debug(t,"refreshing attempt",r),await Kr(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:Jr})),s=(e,t)=>{const r=200*Math.pow(2,e);return t&&fr(t)&&Date.now()+r-n<Gt},new Promise((e,t)=>{(async()=>{for(let i=0;i<1/0;i++)try{const t=await r(i);if(!s(i,null,t))return void e(t)}catch(n){if(!s(i,n))return void t(n)}})()}))}catch(n){if(this._debug(t,"error",n),nr(n))return{data:{session:null,user:null},error:n};throw n}finally{this._debug(t,"end")}var r,s}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){const r=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",r),Cr()&&!t.skipBrowserRedirect&&window.location.assign(r),{data:{provider:e,url:r},error:null}}async _recoverAndRefresh(){var e,t;const r="#_recoverAndRefresh()";this._debug(r,"begin");try{const n=await jr(this.storage,this.storageKey);if(n&&this.userStorage){let t=await jr(this.userStorage,this.storageKey+"-user");this.storage.isServer||!Object.is(this.storage,this.userStorage)||t||(t={user:n.user},await Nr(this.userStorage,this.storageKey+"-user",t)),n.user=null!==(e=null==t?void 0:t.user)&&void 0!==e?e:zr()}else if(n&&!n.user&&!n.user){const e=await jr(this.storage,this.storageKey+"-user");e&&(null==e?void 0:e.user)?(n.user=e.user,await Ir(this.storage,this.storageKey+"-user"),await Nr(this.storage,this.storageKey,n)):n.user=zr()}if(this._debug(r,"session from storage",n),!this._isValidSession(n))return this._debug(r,"session is not valid"),void(null!==n&&await this._removeSession());const i=1e3*(null!==(t=n.expires_at)&&void 0!==t?t:1/0)-Date.now()<Yt;if(this._debug(r,`session has${i?"":" not"} expired with margin of 90000s`),i){if(this.autoRefreshToken&&n.refresh_token){const{error:e}=await this._callRefreshToken(n.refresh_token);e&&(console.error(e),fr(e)||(this._debug(r,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else if(n.user&&!0===n.user.__isUserNotAvailableProxy)try{const{data:e,error:t}=await this._getUser(n.access_token);!t&&(null==e?void 0:e.user)?(n.user=e.user,await this._saveSession(n),await this._notifyAllSubscribers("SIGNED_IN",n)):this._debug(r,"could not get user data, skipping SIGNED_IN notification")}catch(s){console.error("Error getting user data:",s),this._debug(r,"error getting user data, skipping SIGNED_IN notification",s)}else await this._notifyAllSubscribers("SIGNED_IN",n)}catch(n){return this._debug(r,"error",n),void console.error(n)}finally{this._debug(r,"end")}}async _callRefreshToken(e){var t,r;if(!e)throw new cr;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const s=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(s,"begin");try{this.refreshingDeferred=new Dr;const{data:t,error:r}=await this._refreshAccessToken(e);if(r)throw r;if(!t.session)throw new cr;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);const s={session:t.session,error:null};return this.refreshingDeferred.resolve(s),s}catch(n){if(this._debug(s,"error",n),nr(n)){const e={session:null,error:n};return fr(n)||await this._removeSession(),null===(t=this.refreshingDeferred)||void 0===t||t.resolve(e),e}throw null===(r=this.refreshingDeferred)||void 0===r||r.reject(n),n}finally{this.refreshingDeferred=null,this._debug(s,"end")}}async _notifyAllSubscribers(e,t,r=!0){const s=`#_notifyAllSubscribers(${e})`;this._debug(s,"begin",t,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:e,session:t});const s=[],n=Array.from(this.stateChangeEmitters.values()).map(async r=>{try{await r.callback(e,t)}catch(n){s.push(n)}});if(await Promise.all(n),s.length>0){for(let e=0;e<s.length;e+=1)console.error(s[e]);throw s[0]}}finally{this._debug(s,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0;const t=Object.assign({},e),r=t.user&&!0===t.user.__isUserNotAvailableProxy;if(this.userStorage){!r&&t.user&&await Nr(this.userStorage,this.storageKey+"-user",{user:t.user});const e=Object.assign({},t);delete e.user;const s=Fr(e);await Nr(this.storage,this.storageKey,s)}else{const e=Fr(t);await Nr(this.storage,this.storageKey,e)}}async _removeSession(){this._debug("#_removeSession()"),await Ir(this.storage,this.storageKey),await Ir(this.storage,this.storageKey+"-code-verifier"),await Ir(this.storage,this.storageKey+"-user"),this.userStorage&&await Ir(this.userStorage,this.storageKey+"-user"),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&Cr()&&(null===window||void 0===window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(t){console.error("removing visibilitychange callback failed",t)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const e=setInterval(()=>this._autoRefreshTokenTick(),Gt);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const t=Date.now();try{return await this._useSession(async e=>{const{data:{session:r}}=e;if(!r||!r.refresh_token||!r.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");const s=Math.floor((1e3*r.expires_at-t)/Gt);this._debug("#_autoRefreshTokenTick()",`access token expires in ${s} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),s<=3&&await this._callRefreshToken(r.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(!(e.isAcquireTimeout||e instanceof ns))throw e;this._debug("auto refresh token tick lock not available")}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!Cr()||!(null===window||void 0===window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null===window||void 0===window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){const t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{"visible"===document.visibilityState?await this._recoverAndRefresh():this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting")}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,r){const s=[`provider=${encodeURIComponent(t)}`];if((null==r?void 0:r.redirectTo)&&s.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),(null==r?void 0:r.scopes)&&s.push(`scopes=${encodeURIComponent(r.scopes)}`),"pkce"===this.flowType){const[e,t]=await $r(this.storage,this.storageKey),r=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});s.push(r.toString())}if(null==r?void 0:r.queryParams){const e=new URLSearchParams(r.queryParams);s.push(e.toString())}return(null==r?void 0:r.skipBrowserRedirect)&&s.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${e}?${s.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var r;const{data:s,error:n}=t;return n?{data:null,error:n}:await Kr(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null===(r=null==s?void 0:s.session)||void 0===r?void 0:r.access_token})})}catch(t){if(nr(t))return{data:null,error:t};throw t}}async _enroll(e){try{return await this._useSession(async t=>{var r,s;const{data:n,error:i}=t;if(i)return{data:null,error:i};const o=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:a,error:c}=await Kr(this.fetch,"POST",`${this.url}/factors`,{body:o,headers:this.headers,jwt:null===(r=null==n?void 0:n.session)||void 0===r?void 0:r.access_token});return c?{data:null,error:c}:("totp"===e.factorType&&(null===(s=null==a?void 0:a.totp)||void 0===s?void 0:s.qr_code)&&(a.totp.qr_code=`data:image/svg+xml;utf-8,${a.totp.qr_code}`),{data:a,error:null})})}catch(t){if(nr(t))return{data:null,error:t};throw t}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;const{data:s,error:n}=t;if(n)return{data:null,error:n};const{data:i,error:o}=await Kr(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null===(r=null==s?void 0:s.session)||void 0===r?void 0:r.access_token});return o?{data:null,error:o}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+i.expires_in},i)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",i),{data:i,error:o})})}catch(t){if(nr(t))return{data:null,error:t};throw t}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;const{data:s,error:n}=t;return n?{data:null,error:n}:await Kr(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null===(r=null==s?void 0:s.session)||void 0===r?void 0:r.access_token})})}catch(t){if(nr(t))return{data:null,error:t};throw t}})}async _challengeAndVerify(e){const{data:t,error:r}=await this._challenge({factorId:e.factorId});return r?{data:null,error:r}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){const{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};const r=(null==e?void 0:e.factors)||[],s=r.filter(e=>"totp"===e.factor_type&&"verified"===e.status),n=r.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:r,totp:s,phone:n},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,r;const{data:{session:s},error:n}=e;if(n)return{data:null,error:n};if(!s)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:i}=Or(s.access_token);let o=null;i.aal&&(o=i.aal);let a=o;(null!==(r=null===(t=s.user.factors)||void 0===t?void 0:t.filter(e=>"verified"===e.status))&&void 0!==r?r:[]).length>0&&(a="aal2");return{data:{currentLevel:o,nextLevel:a,currentAuthenticationMethods:i.amr||[]},error:null}}))}async fetchJwk(e,t={keys:[]}){let r=t.keys.find(t=>t.kid===e);if(r)return r;const s=Date.now();if(r=this.jwks.keys.find(t=>t.kid===e),r&&this.jwks_cached_at+6e5>s)return r;const{data:n,error:i}=await Kr(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(i)throw i;return n.keys&&0!==n.keys.length?(this.jwks=n,this.jwks_cached_at=s,r=n.keys.find(t=>t.kid===e),r||null):null}async getClaims(e,t={}){try{let r=e;if(!r){const{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};r=e.session.access_token}const{header:s,payload:n,signature:i,raw:{header:o,payload:a}}=Or(r);(null==t?void 0:t.allowExpired)||function(e){if(!e)throw new Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw new Error("JWT has expired")}(n.exp);const c=s.alg&&!s.alg.startsWith("HS")&&s.kid&&"crypto"in globalThis&&"subtle"in globalThis.crypto?await this.fetchJwk(s.kid,(null==t?void 0:t.keys)?{keys:t.keys}:null==t?void 0:t.jwks):null;if(!c){const{error:e}=await this.getUser(r);if(e)throw e;return{data:{claims:n,header:s,signature:i},error:null}}const l=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}(s.alg),h=await rr.subtle.importKey("jwk",c,l,!0,["verify"]);if(!(await rr.subtle.verify(l,h,i,xr(`${o}.${a}`))))throw new mr("Invalid JWT signature");return{data:{claims:n,header:s,signature:i},error:null}}catch(r){if(nr(r))return{data:null,error:r};throw r}}}hs.nextInstanceID=0;const us=hs;class ds extends us{constructor(e){super(e)}}var ps=function(e,t,r,s){return new(r||(r=Promise))(function(n,i){function o(e){try{c(s.next(e))}catch(t){i(t)}}function a(e){try{c(s.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}c((s=s.apply(e,t||[])).next())})};class fs{constructor(e,t,r){var s,n,i;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const o=(a=e).endsWith("/")?a:a+"/";var a;const c=new URL(o);this.realtimeUrl=new URL("realtime/v1",c),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",c),this.storageUrl=new URL("storage/v1",c),this.functionsUrl=new URL("functions/v1",c);const l=`sb-${c.hostname.split(".")[0]}-auth-token`,h=function(e,t){var r,s;const{db:n,auth:i,realtime:o,global:a}=e,{db:c,auth:l,realtime:h,global:u}=t,d={db:Object.assign(Object.assign({},c),n),auth:Object.assign(Object.assign({},l),i),realtime:Object.assign(Object.assign({},h),o),storage:{},global:Object.assign(Object.assign(Object.assign({},u),a),{headers:Object.assign(Object.assign({},null!==(r=null==u?void 0:u.headers)&&void 0!==r?r:{}),null!==(s=null==a?void 0:a.headers)&&void 0!==s?s:{})}),accessToken:()=>Kt(this,void 0,void 0,function*(){return""})};return e.accessToken?d.accessToken=e.accessToken:delete d.accessToken,d}(null!=r?r:{},{db:zt,realtime:Ht,auth:Object.assign(Object.assign({},Ft),{storageKey:l}),global:Mt});this.storageKey=null!==(s=h.auth.storageKey)&&void 0!==s?s:"",this.headers=null!==(n=h.global.headers)&&void 0!==n?n:{},h.accessToken?(this.accessToken=h.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!==(i=h.auth)&&void 0!==i?i:{},this.headers,h.global.fetch),this.fetch=Wt(t,this._getAccessToken.bind(this),h.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},h.realtime)),this.rest=new ke(new URL("rest/v1",c).href,{headers:this.headers,schema:h.db.schema,fetch:this.fetch}),this.storage=new Lt(this.storageUrl.href,this.headers,this.fetch,null==r?void 0:r.storage),h.accessToken||this._listenForAuthEvents()}get functions(){return new B(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},r={}){return this.rest.rpc(e,t,r)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return ps(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:r}=yield this.auth.getSession();return null!==(t=null===(e=r.session)||void 0===e?void 0:e.access_token)&&void 0!==t?t:this.supabaseKey})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:s,storageKey:n,flowType:i,lock:o,debug:a},c,l){const h={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new ds({url:this.authUrl.href,headers:Object.assign(Object.assign({},h),c),storageKey:n,autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:s,flowType:i,lock:o,debug:a,fetch:l,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new gt(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,r){"TOKEN_REFRESHED"!==e&&"SIGNED_IN"!==e||this.changedAccessToken===r?"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0):this.changedAccessToken=r}}const gs=(e,t,r)=>new fs(e,t,r);(function(){if("undefined"!=typeof window)return!1;if("undefined"==typeof process)return!1;const e=process.version;if(null==e)return!1;const t=e.match(/^v(\d+)\./);return!!t&&parseInt(t[1],10)<=18})()&&console.warn("⚠️  Node.js 18 and below are deprecated and will no longer be supported in future versions of @supabase/supabase-js. Please upgrade to Node.js 20 or later. For more information, visit: https://github.com/orgs/supabase/discussions/37217");const ms="https://gcrsoruzrxompokkckii.supabase.co",vs="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdjcnNvcnV6cnhvbXBva2tja2lpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDE5MzgzMDcsImV4cCI6MjA1NzUxNDMwN30.YZNPNofM03rbi7Gay-iE95CRzb9llrehuLt0O4JU9MI";let ys;function ws(){return ys||(ys=gs(ms,vs,{global:{headers:{Authorization:`Bearer ${vs}`}},auth:{persistSession:!1,autoRefreshToken:!1,detectSessionInUrl:!1}})),ys}function _s(e){return e?gs(ms,vs,{global:{headers:{"x-auth-token":e,Authorization:`Bearer ${vs}`}},auth:{persistSession:!1,autoRefreshToken:!1,detectSessionInUrl:!1}}):ws()}var bs={exports:{}};var ks={exports:{}};const Ss=I(Object.freeze(Object.defineProperty({__proto__:null,default:{}},Symbol.toStringTag,{value:"Module"})));var Es;function xs(){return Es||(Es=1,ks.exports=(e=e||function(e,t){var r;if("undefined"!=typeof window&&rr&&(r=rr),"undefined"!=typeof self&&self.crypto&&(r=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(r=globalThis.crypto),!r&&"undefined"!=typeof window&&window.msCrypto&&(r=window.msCrypto),!r&&void 0!==N&&N.crypto&&(r=N.crypto),!r)try{r=Ss}catch(g){}var s=function(){if(r){if("function"==typeof r.getRandomValues)try{return r.getRandomValues(new Uint32Array(1))[0]}catch(g){}if("function"==typeof r.randomBytes)try{return r.randomBytes(4).readInt32LE()}catch(g){}}throw new Error("Native crypto module could not be used to get secure random number.")},n=Object.create||function(){function e(){}return function(t){var r;return e.prototype=t,r=new e,e.prototype=null,r}}(),i={},o=i.lib={},a=o.Base=function(){return{extend:function(e){var t=n(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),c=o.WordArray=a.extend({init:function(e,r){e=this.words=e||[],this.sigBytes=r!=t?r:4*e.length},toString:function(e){return(e||h).stringify(this)},concat:function(e){var t=this.words,r=e.words,s=this.sigBytes,n=e.sigBytes;if(this.clamp(),s%4)for(var i=0;i<n;i++){var o=r[i>>>2]>>>24-i%4*8&255;t[s+i>>>2]|=o<<24-(s+i)%4*8}else for(var a=0;a<n;a+=4)t[s+a>>>2]=r[a>>>2];return this.sigBytes+=n,this},clamp:function(){var t=this.words,r=this.sigBytes;t[r>>>2]&=4294967295<<32-r%4*8,t.length=e.ceil(r/4)},clone:function(){var e=a.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],r=0;r<e;r+=4)t.push(s());return new c.init(t,e)}}),l=i.enc={},h=l.Hex={stringify:function(e){for(var t=e.words,r=e.sigBytes,s=[],n=0;n<r;n++){var i=t[n>>>2]>>>24-n%4*8&255;s.push((i>>>4).toString(16)),s.push((15&i).toString(16))}return s.join("")},parse:function(e){for(var t=e.length,r=[],s=0;s<t;s+=2)r[s>>>3]|=parseInt(e.substr(s,2),16)<<24-s%8*4;return new c.init(r,t/2)}},u=l.Latin1={stringify:function(e){for(var t=e.words,r=e.sigBytes,s=[],n=0;n<r;n++){var i=t[n>>>2]>>>24-n%4*8&255;s.push(String.fromCharCode(i))}return s.join("")},parse:function(e){for(var t=e.length,r=[],s=0;s<t;s++)r[s>>>2]|=(255&e.charCodeAt(s))<<24-s%4*8;return new c.init(r,t)}},d=l.Utf8={stringify:function(e){try{return decodeURIComponent(escape(u.stringify(e)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(e){return u.parse(unescape(encodeURIComponent(e)))}},p=o.BufferedBlockAlgorithm=a.extend({reset:function(){this._data=new c.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=d.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var r,s=this._data,n=s.words,i=s.sigBytes,o=this.blockSize,a=i/(4*o),l=(a=t?e.ceil(a):e.max((0|a)-this._minBufferSize,0))*o,h=e.min(4*l,i);if(l){for(var u=0;u<l;u+=o)this._doProcessBlock(n,u);r=n.splice(0,l),s.sigBytes-=h}return new c.init(r,h)},clone:function(){var e=a.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});o.Hasher=p.extend({cfg:a.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,r){return new e.init(r).finalize(t)}},_createHmacHelper:function(e){return function(t,r){return new f.HMAC.init(e,r).finalize(t)}}});var f=i.algo={};return i}(Math),e)),ks.exports;var e}var Ts,Cs={exports:{}};function As(){return Ts||(Ts=1,Cs.exports=(o=xs(),r=(t=o).lib,s=r.Base,n=r.WordArray,(i=t.x64={}).Word=s.extend({init:function(e,t){this.high=e,this.low=t}}),i.WordArray=s.extend({init:function(t,r){t=this.words=t||[],this.sigBytes=r!=e?r:8*t.length},toX32:function(){for(var e=this.words,t=e.length,r=[],s=0;s<t;s++){var i=e[s];r.push(i.high),r.push(i.low)}return n.create(r,this.sigBytes)},clone:function(){for(var e=s.clone.call(this),t=e.words=this.words.slice(0),r=t.length,n=0;n<r;n++)t[n]=t[n].clone();return e}}),o)),Cs.exports;var e,t,r,s,n,i,o}var Ps,Bs={exports:{}};function Ns(){return Ps||(Ps=1,Bs.exports=(e=xs(),function(){if("function"==typeof ArrayBuffer){var t=e.lib.WordArray,r=t.init,s=t.init=function(e){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){for(var t=e.byteLength,s=[],n=0;n<t;n++)s[n>>>2]|=e[n]<<24-n%4*8;r.call(this,s,t)}else r.apply(this,arguments)};s.prototype=t}}(),e.lib.WordArray)),Bs.exports;var e}var js,Is={exports:{}};function Ds(){return js||(js=1,Is.exports=(e=xs(),function(){var t=e,r=t.lib.WordArray,s=t.enc;function n(e){return e<<8&4278255360|e>>>8&16711935}s.Utf16=s.Utf16BE={stringify:function(e){for(var t=e.words,r=e.sigBytes,s=[],n=0;n<r;n+=2){var i=t[n>>>2]>>>16-n%4*8&65535;s.push(String.fromCharCode(i))}return s.join("")},parse:function(e){for(var t=e.length,s=[],n=0;n<t;n++)s[n>>>1]|=e.charCodeAt(n)<<16-n%2*16;return r.create(s,2*t)}},s.Utf16LE={stringify:function(e){for(var t=e.words,r=e.sigBytes,s=[],i=0;i<r;i+=2){var o=n(t[i>>>2]>>>16-i%4*8&65535);s.push(String.fromCharCode(o))}return s.join("")},parse:function(e){for(var t=e.length,s=[],i=0;i<t;i++)s[i>>>1]|=n(e.charCodeAt(i)<<16-i%2*16);return r.create(s,2*t)}}}(),e.enc.Utf16)),Is.exports;var e}var Os,Rs={exports:{}};function Vs(){return Os||(Os=1,Rs.exports=(e=xs(),function(){var t=e,r=t.lib.WordArray;function s(e,t,s){for(var n=[],i=0,o=0;o<t;o++)if(o%4){var a=s[e.charCodeAt(o-1)]<<o%4*2|s[e.charCodeAt(o)]>>>6-o%4*2;n[i>>>2]|=a<<24-i%4*8,i++}return r.create(n,i)}t.enc.Base64={stringify:function(e){var t=e.words,r=e.sigBytes,s=this._map;e.clamp();for(var n=[],i=0;i<r;i+=3)for(var o=(t[i>>>2]>>>24-i%4*8&255)<<16|(t[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|t[i+2>>>2]>>>24-(i+2)%4*8&255,a=0;a<4&&i+.75*a<r;a++)n.push(s.charAt(o>>>6*(3-a)&63));var c=s.charAt(64);if(c)for(;n.length%4;)n.push(c);return n.join("")},parse:function(e){var t=e.length,r=this._map,n=this._reverseMap;if(!n){n=this._reverseMap=[];for(var i=0;i<r.length;i++)n[r.charCodeAt(i)]=i}var o=r.charAt(64);if(o){var a=e.indexOf(o);-1!==a&&(t=a)}return s(e,t,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),e.enc.Base64)),Rs.exports;var e}var $s,Ls={exports:{}};function Us(){return $s||($s=1,Ls.exports=(e=xs(),function(){var t=e,r=t.lib.WordArray;function s(e,t,s){for(var n=[],i=0,o=0;o<t;o++)if(o%4){var a=s[e.charCodeAt(o-1)]<<o%4*2|s[e.charCodeAt(o)]>>>6-o%4*2;n[i>>>2]|=a<<24-i%4*8,i++}return r.create(n,i)}t.enc.Base64url={stringify:function(e,t){void 0===t&&(t=!0);var r=e.words,s=e.sigBytes,n=t?this._safe_map:this._map;e.clamp();for(var i=[],o=0;o<s;o+=3)for(var a=(r[o>>>2]>>>24-o%4*8&255)<<16|(r[o+1>>>2]>>>24-(o+1)%4*8&255)<<8|r[o+2>>>2]>>>24-(o+2)%4*8&255,c=0;c<4&&o+.75*c<s;c++)i.push(n.charAt(a>>>6*(3-c)&63));var l=n.charAt(64);if(l)for(;i.length%4;)i.push(l);return i.join("")},parse:function(e,t){void 0===t&&(t=!0);var r=e.length,n=t?this._safe_map:this._map,i=this._reverseMap;if(!i){i=this._reverseMap=[];for(var o=0;o<n.length;o++)i[n.charCodeAt(o)]=o}var a=n.charAt(64);if(a){var c=e.indexOf(a);-1!==c&&(r=c)}return s(e,r,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"}}(),e.enc.Base64url)),Ls.exports;var e}var Ms,zs={exports:{}};function Fs(){return Ms||(Ms=1,zs.exports=(e=xs(),function(t){var r=e,s=r.lib,n=s.WordArray,i=s.Hasher,o=r.algo,a=[];!function(){for(var e=0;e<64;e++)a[e]=4294967296*t.abs(t.sin(e+1))|0}();var c=o.MD5=i.extend({_doReset:function(){this._hash=new n.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var s=t+r,n=e[s];e[s]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8)}var i=this._hash.words,o=e[t+0],c=e[t+1],p=e[t+2],f=e[t+3],g=e[t+4],m=e[t+5],v=e[t+6],y=e[t+7],w=e[t+8],_=e[t+9],b=e[t+10],k=e[t+11],S=e[t+12],E=e[t+13],x=e[t+14],T=e[t+15],C=i[0],A=i[1],P=i[2],B=i[3];C=l(C,A,P,B,o,7,a[0]),B=l(B,C,A,P,c,12,a[1]),P=l(P,B,C,A,p,17,a[2]),A=l(A,P,B,C,f,22,a[3]),C=l(C,A,P,B,g,7,a[4]),B=l(B,C,A,P,m,12,a[5]),P=l(P,B,C,A,v,17,a[6]),A=l(A,P,B,C,y,22,a[7]),C=l(C,A,P,B,w,7,a[8]),B=l(B,C,A,P,_,12,a[9]),P=l(P,B,C,A,b,17,a[10]),A=l(A,P,B,C,k,22,a[11]),C=l(C,A,P,B,S,7,a[12]),B=l(B,C,A,P,E,12,a[13]),P=l(P,B,C,A,x,17,a[14]),C=h(C,A=l(A,P,B,C,T,22,a[15]),P,B,c,5,a[16]),B=h(B,C,A,P,v,9,a[17]),P=h(P,B,C,A,k,14,a[18]),A=h(A,P,B,C,o,20,a[19]),C=h(C,A,P,B,m,5,a[20]),B=h(B,C,A,P,b,9,a[21]),P=h(P,B,C,A,T,14,a[22]),A=h(A,P,B,C,g,20,a[23]),C=h(C,A,P,B,_,5,a[24]),B=h(B,C,A,P,x,9,a[25]),P=h(P,B,C,A,f,14,a[26]),A=h(A,P,B,C,w,20,a[27]),C=h(C,A,P,B,E,5,a[28]),B=h(B,C,A,P,p,9,a[29]),P=h(P,B,C,A,y,14,a[30]),C=u(C,A=h(A,P,B,C,S,20,a[31]),P,B,m,4,a[32]),B=u(B,C,A,P,w,11,a[33]),P=u(P,B,C,A,k,16,a[34]),A=u(A,P,B,C,x,23,a[35]),C=u(C,A,P,B,c,4,a[36]),B=u(B,C,A,P,g,11,a[37]),P=u(P,B,C,A,y,16,a[38]),A=u(A,P,B,C,b,23,a[39]),C=u(C,A,P,B,E,4,a[40]),B=u(B,C,A,P,o,11,a[41]),P=u(P,B,C,A,f,16,a[42]),A=u(A,P,B,C,v,23,a[43]),C=u(C,A,P,B,_,4,a[44]),B=u(B,C,A,P,S,11,a[45]),P=u(P,B,C,A,T,16,a[46]),C=d(C,A=u(A,P,B,C,p,23,a[47]),P,B,o,6,a[48]),B=d(B,C,A,P,y,10,a[49]),P=d(P,B,C,A,x,15,a[50]),A=d(A,P,B,C,m,21,a[51]),C=d(C,A,P,B,S,6,a[52]),B=d(B,C,A,P,f,10,a[53]),P=d(P,B,C,A,b,15,a[54]),A=d(A,P,B,C,c,21,a[55]),C=d(C,A,P,B,w,6,a[56]),B=d(B,C,A,P,T,10,a[57]),P=d(P,B,C,A,v,15,a[58]),A=d(A,P,B,C,E,21,a[59]),C=d(C,A,P,B,g,6,a[60]),B=d(B,C,A,P,k,10,a[61]),P=d(P,B,C,A,p,15,a[62]),A=d(A,P,B,C,_,21,a[63]),i[0]=i[0]+C|0,i[1]=i[1]+A|0,i[2]=i[2]+P|0,i[3]=i[3]+B|0},_doFinalize:function(){var e=this._data,r=e.words,s=8*this._nDataBytes,n=8*e.sigBytes;r[n>>>5]|=128<<24-n%32;var i=t.floor(s/4294967296),o=s;r[15+(n+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),r[14+(n+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),e.sigBytes=4*(r.length+1),this._process();for(var a=this._hash,c=a.words,l=0;l<4;l++){var h=c[l];c[l]=16711935&(h<<8|h>>>24)|4278255360&(h<<24|h>>>8)}return a},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});function l(e,t,r,s,n,i,o){var a=e+(t&r|~t&s)+n+o;return(a<<i|a>>>32-i)+t}function h(e,t,r,s,n,i,o){var a=e+(t&s|r&~s)+n+o;return(a<<i|a>>>32-i)+t}function u(e,t,r,s,n,i,o){var a=e+(t^r^s)+n+o;return(a<<i|a>>>32-i)+t}function d(e,t,r,s,n,i,o){var a=e+(r^(t|~s))+n+o;return(a<<i|a>>>32-i)+t}r.MD5=i._createHelper(c),r.HmacMD5=i._createHmacHelper(c)}(Math),e.MD5)),zs.exports;var e}var Hs,qs={exports:{}};function Ws(){return Hs||(Hs=1,qs.exports=(a=xs(),t=(e=a).lib,r=t.WordArray,s=t.Hasher,n=e.algo,i=[],o=n.SHA1=s.extend({_doReset:function(){this._hash=new r.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=this._hash.words,s=r[0],n=r[1],o=r[2],a=r[3],c=r[4],l=0;l<80;l++){if(l<16)i[l]=0|e[t+l];else{var h=i[l-3]^i[l-8]^i[l-14]^i[l-16];i[l]=h<<1|h>>>31}var u=(s<<5|s>>>27)+c+i[l];u+=l<20?1518500249+(n&o|~n&a):l<40?1859775393+(n^o^a):l<60?(n&o|n&a|o&a)-1894007588:(n^o^a)-899497514,c=a,a=o,o=n<<30|n>>>2,n=s,s=u}r[0]=r[0]+s|0,r[1]=r[1]+n|0,r[2]=r[2]+o|0,r[3]=r[3]+a|0,r[4]=r[4]+c|0},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,s=8*e.sigBytes;return t[s>>>5]|=128<<24-s%32,t[14+(s+64>>>9<<4)]=Math.floor(r/4294967296),t[15+(s+64>>>9<<4)]=r,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=s.clone.call(this);return e._hash=this._hash.clone(),e}}),e.SHA1=s._createHelper(o),e.HmacSHA1=s._createHmacHelper(o),a.SHA1)),qs.exports;var e,t,r,s,n,i,o,a}var Ks,Js={exports:{}};function Gs(){return Ks||(Ks=1,Js.exports=(e=xs(),function(t){var r=e,s=r.lib,n=s.WordArray,i=s.Hasher,o=r.algo,a=[],c=[];!function(){function e(e){for(var r=t.sqrt(e),s=2;s<=r;s++)if(!(e%s))return!1;return!0}function r(e){return 4294967296*(e-(0|e))|0}for(var s=2,n=0;n<64;)e(s)&&(n<8&&(a[n]=r(t.pow(s,.5))),c[n]=r(t.pow(s,1/3)),n++),s++}();var l=[],h=o.SHA256=i.extend({_doReset:function(){this._hash=new n.init(a.slice(0))},_doProcessBlock:function(e,t){for(var r=this._hash.words,s=r[0],n=r[1],i=r[2],o=r[3],a=r[4],h=r[5],u=r[6],d=r[7],p=0;p<64;p++){if(p<16)l[p]=0|e[t+p];else{var f=l[p-15],g=(f<<25|f>>>7)^(f<<14|f>>>18)^f>>>3,m=l[p-2],v=(m<<15|m>>>17)^(m<<13|m>>>19)^m>>>10;l[p]=g+l[p-7]+v+l[p-16]}var y=s&n^s&i^n&i,w=(s<<30|s>>>2)^(s<<19|s>>>13)^(s<<10|s>>>22),_=d+((a<<26|a>>>6)^(a<<21|a>>>11)^(a<<7|a>>>25))+(a&h^~a&u)+c[p]+l[p];d=u,u=h,h=a,a=o+_|0,o=i,i=n,n=s,s=_+(w+y)|0}r[0]=r[0]+s|0,r[1]=r[1]+n|0,r[2]=r[2]+i|0,r[3]=r[3]+o|0,r[4]=r[4]+a|0,r[5]=r[5]+h|0,r[6]=r[6]+u|0,r[7]=r[7]+d|0},_doFinalize:function(){var e=this._data,r=e.words,s=8*this._nDataBytes,n=8*e.sigBytes;return r[n>>>5]|=128<<24-n%32,r[14+(n+64>>>9<<4)]=t.floor(s/4294967296),r[15+(n+64>>>9<<4)]=s,e.sigBytes=4*r.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});r.SHA256=i._createHelper(h),r.HmacSHA256=i._createHmacHelper(h)}(Math),e.SHA256)),Js.exports;var e}var Ys,Xs={exports:{}};var Qs,Zs={exports:{}};function en(){return Qs||(Qs=1,Zs.exports=(e=xs(),As(),function(){var t=e,r=t.lib.Hasher,s=t.x64,n=s.Word,i=s.WordArray,o=t.algo;function a(){return n.create.apply(n,arguments)}var c=[a(1116352408,3609767458),a(1899447441,602891725),a(3049323471,3964484399),a(3921009573,2173295548),a(961987163,4081628472),a(1508970993,3053834265),a(2453635748,2937671579),a(2870763221,3664609560),a(3624381080,2734883394),a(310598401,1164996542),a(607225278,1323610764),a(1426881987,3590304994),a(1925078388,4068182383),a(2162078206,991336113),a(2614888103,633803317),a(3248222580,3479774868),a(3835390401,2666613458),a(4022224774,944711139),a(264347078,2341262773),a(604807628,2007800933),a(770255983,1495990901),a(1249150122,1856431235),a(1555081692,3175218132),a(1996064986,2198950837),a(2554220882,3999719339),a(2821834349,766784016),a(2952996808,2566594879),a(3210313671,3203337956),a(3336571891,1034457026),a(3584528711,2466948901),a(113926993,3758326383),a(338241895,168717936),a(666307205,1188179964),a(773529912,1546045734),a(1294757372,1522805485),a(1396182291,2643833823),a(1695183700,2343527390),a(1986661051,1014477480),a(2177026350,1206759142),a(2456956037,344077627),a(2730485921,1290863460),a(2820302411,3158454273),a(3259730800,3505952657),a(3345764771,106217008),a(3516065817,3606008344),a(3600352804,1432725776),a(4094571909,1467031594),a(275423344,851169720),a(430227734,3100823752),a(506948616,1363258195),a(659060556,3750685593),a(883997877,3785050280),a(958139571,3318307427),a(1322822218,3812723403),a(1537002063,2003034995),a(1747873779,3602036899),a(1955562222,1575990012),a(2024104815,1125592928),a(2227730452,2716904306),a(2361852424,442776044),a(2428436474,593698344),a(2756734187,3733110249),a(3204031479,2999351573),a(3329325298,3815920427),a(3391569614,3928383900),a(3515267271,566280711),a(3940187606,3454069534),a(4118630271,4000239992),a(116418474,1914138554),a(174292421,2731055270),a(289380356,3203993006),a(460393269,320620315),a(685471733,587496836),a(852142971,1086792851),a(1017036298,365543100),a(1126000580,2618297676),a(1288033470,3409855158),a(1501505948,4234509866),a(1607167915,987167468),a(1816402316,1246189591)],l=[];!function(){for(var e=0;e<80;e++)l[e]=a()}();var h=o.SHA512=r.extend({_doReset:function(){this._hash=new i.init([new n.init(1779033703,4089235720),new n.init(3144134277,2227873595),new n.init(1013904242,4271175723),new n.init(2773480762,1595750129),new n.init(1359893119,2917565137),new n.init(2600822924,725511199),new n.init(528734635,4215389547),new n.init(1541459225,327033209)])},_doProcessBlock:function(e,t){for(var r=this._hash.words,s=r[0],n=r[1],i=r[2],o=r[3],a=r[4],h=r[5],u=r[6],d=r[7],p=s.high,f=s.low,g=n.high,m=n.low,v=i.high,y=i.low,w=o.high,_=o.low,b=a.high,k=a.low,S=h.high,E=h.low,x=u.high,T=u.low,C=d.high,A=d.low,P=p,B=f,N=g,j=m,I=v,D=y,O=w,R=_,V=b,$=k,L=S,U=E,M=x,z=T,F=C,H=A,q=0;q<80;q++){var W,K,J=l[q];if(q<16)K=J.high=0|e[t+2*q],W=J.low=0|e[t+2*q+1];else{var G=l[q-15],Y=G.high,X=G.low,Q=(Y>>>1|X<<31)^(Y>>>8|X<<24)^Y>>>7,Z=(X>>>1|Y<<31)^(X>>>8|Y<<24)^(X>>>7|Y<<25),ee=l[q-2],te=ee.high,re=ee.low,se=(te>>>19|re<<13)^(te<<3|re>>>29)^te>>>6,ne=(re>>>19|te<<13)^(re<<3|te>>>29)^(re>>>6|te<<26),ie=l[q-7],oe=ie.high,ae=ie.low,ce=l[q-16],le=ce.high,he=ce.low;K=(K=(K=Q+oe+((W=Z+ae)>>>0<Z>>>0?1:0))+se+((W+=ne)>>>0<ne>>>0?1:0))+le+((W+=he)>>>0<he>>>0?1:0),J.high=K,J.low=W}var ue,de=V&L^~V&M,pe=$&U^~$&z,fe=P&N^P&I^N&I,ge=B&j^B&D^j&D,me=(P>>>28|B<<4)^(P<<30|B>>>2)^(P<<25|B>>>7),ve=(B>>>28|P<<4)^(B<<30|P>>>2)^(B<<25|P>>>7),ye=(V>>>14|$<<18)^(V>>>18|$<<14)^(V<<23|$>>>9),we=($>>>14|V<<18)^($>>>18|V<<14)^($<<23|V>>>9),_e=c[q],be=_e.high,ke=_e.low,Se=F+ye+((ue=H+we)>>>0<H>>>0?1:0),Ee=ve+ge;F=M,H=z,M=L,z=U,L=V,U=$,V=O+(Se=(Se=(Se=Se+de+((ue+=pe)>>>0<pe>>>0?1:0))+be+((ue+=ke)>>>0<ke>>>0?1:0))+K+((ue+=W)>>>0<W>>>0?1:0))+(($=R+ue|0)>>>0<R>>>0?1:0)|0,O=I,R=D,I=N,D=j,N=P,j=B,P=Se+(me+fe+(Ee>>>0<ve>>>0?1:0))+((B=ue+Ee|0)>>>0<ue>>>0?1:0)|0}f=s.low=f+B,s.high=p+P+(f>>>0<B>>>0?1:0),m=n.low=m+j,n.high=g+N+(m>>>0<j>>>0?1:0),y=i.low=y+D,i.high=v+I+(y>>>0<D>>>0?1:0),_=o.low=_+R,o.high=w+O+(_>>>0<R>>>0?1:0),k=a.low=k+$,a.high=b+V+(k>>>0<$>>>0?1:0),E=h.low=E+U,h.high=S+L+(E>>>0<U>>>0?1:0),T=u.low=T+z,u.high=x+M+(T>>>0<z>>>0?1:0),A=d.low=A+H,d.high=C+F+(A>>>0<H>>>0?1:0)},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,s=8*e.sigBytes;return t[s>>>5]|=128<<24-s%32,t[30+(s+128>>>10<<5)]=Math.floor(r/4294967296),t[31+(s+128>>>10<<5)]=r,e.sigBytes=4*t.length,this._process(),this._hash.toX32()},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e},blockSize:32});t.SHA512=r._createHelper(h),t.HmacSHA512=r._createHmacHelper(h)}(),e.SHA512)),Zs.exports;var e}var tn,rn={exports:{}};var sn,nn={exports:{}};function on(){return sn||(sn=1,nn.exports=(e=xs(),As(),function(t){var r=e,s=r.lib,n=s.WordArray,i=s.Hasher,o=r.x64.Word,a=r.algo,c=[],l=[],h=[];!function(){for(var e=1,t=0,r=0;r<24;r++){c[e+5*t]=(r+1)*(r+2)/2%64;var s=(2*e+3*t)%5;e=t%5,t=s}for(e=0;e<5;e++)for(t=0;t<5;t++)l[e+5*t]=t+(2*e+3*t)%5*5;for(var n=1,i=0;i<24;i++){for(var a=0,u=0,d=0;d<7;d++){if(1&n){var p=(1<<d)-1;p<32?u^=1<<p:a^=1<<p-32}128&n?n=n<<1^113:n<<=1}h[i]=o.create(a,u)}}();var u=[];!function(){for(var e=0;e<25;e++)u[e]=o.create()}();var d=a.SHA3=i.extend({cfg:i.cfg.extend({outputLength:512}),_doReset:function(){for(var e=this._state=[],t=0;t<25;t++)e[t]=new o.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(e,t){for(var r=this._state,s=this.blockSize/2,n=0;n<s;n++){var i=e[t+2*n],o=e[t+2*n+1];i=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),o=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),(A=r[n]).high^=o,A.low^=i}for(var a=0;a<24;a++){for(var d=0;d<5;d++){for(var p=0,f=0,g=0;g<5;g++)p^=(A=r[d+5*g]).high,f^=A.low;var m=u[d];m.high=p,m.low=f}for(d=0;d<5;d++){var v=u[(d+4)%5],y=u[(d+1)%5],w=y.high,_=y.low;for(p=v.high^(w<<1|_>>>31),f=v.low^(_<<1|w>>>31),g=0;g<5;g++)(A=r[d+5*g]).high^=p,A.low^=f}for(var b=1;b<25;b++){var k=(A=r[b]).high,S=A.low,E=c[b];E<32?(p=k<<E|S>>>32-E,f=S<<E|k>>>32-E):(p=S<<E-32|k>>>64-E,f=k<<E-32|S>>>64-E);var x=u[l[b]];x.high=p,x.low=f}var T=u[0],C=r[0];for(T.high=C.high,T.low=C.low,d=0;d<5;d++)for(g=0;g<5;g++){var A=r[b=d+5*g],P=u[b],B=u[(d+1)%5+5*g],N=u[(d+2)%5+5*g];A.high=P.high^~B.high&N.high,A.low=P.low^~B.low&N.low}A=r[0];var j=h[a];A.high^=j.high,A.low^=j.low}},_doFinalize:function(){var e=this._data,r=e.words;this._nDataBytes;var s=8*e.sigBytes,i=32*this.blockSize;r[s>>>5]|=1<<24-s%32,r[(t.ceil((s+1)/i)*i>>>5)-1]|=128,e.sigBytes=4*r.length,this._process();for(var o=this._state,a=this.cfg.outputLength/8,c=a/8,l=[],h=0;h<c;h++){var u=o[h],d=u.high,p=u.low;d=16711935&(d<<8|d>>>24)|4278255360&(d<<24|d>>>8),p=16711935&(p<<8|p>>>24)|4278255360&(p<<24|p>>>8),l.push(p),l.push(d)}return new n.init(l,a)},clone:function(){for(var e=i.clone.call(this),t=e._state=this._state.slice(0),r=0;r<25;r++)t[r]=t[r].clone();return e}});r.SHA3=i._createHelper(d),r.HmacSHA3=i._createHmacHelper(d)}(Math),e.SHA3)),nn.exports;var e}var an,cn={exports:{}};var ln,hn={exports:{}};function un(){return ln||(ln=1,hn.exports=(e=xs(),r=(t=e).lib.Base,s=t.enc.Utf8,void(t.algo.HMAC=r.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=s.parse(t));var r=e.blockSize,n=4*r;t.sigBytes>n&&(t=e.finalize(t)),t.clamp();for(var i=this._oKey=t.clone(),o=this._iKey=t.clone(),a=i.words,c=o.words,l=0;l<r;l++)a[l]^=1549556828,c[l]^=909522486;i.sigBytes=o.sigBytes=n,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}})))),hn.exports;var e,t,r,s}var dn,pn={exports:{}};var fn,gn={exports:{}};function mn(){return fn||(fn=1,gn.exports=(a=xs(),Ws(),un(),t=(e=a).lib,r=t.Base,s=t.WordArray,n=e.algo,i=n.MD5,o=n.EvpKDF=r.extend({cfg:r.extend({keySize:4,hasher:i,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r,n=this.cfg,i=n.hasher.create(),o=s.create(),a=o.words,c=n.keySize,l=n.iterations;a.length<c;){r&&i.update(r),r=i.update(e).finalize(t),i.reset();for(var h=1;h<l;h++)r=i.finalize(r),i.reset();o.concat(r)}return o.sigBytes=4*c,o}}),e.EvpKDF=function(e,t,r){return o.create(r).compute(e,t)},a.EvpKDF)),gn.exports;var e,t,r,s,n,i,o,a}var vn,yn={exports:{}};function wn(){return vn||(vn=1,yn.exports=(e=xs(),mn(),void(e.lib.Cipher||function(t){var r=e,s=r.lib,n=s.Base,i=s.WordArray,o=s.BufferedBlockAlgorithm,a=r.enc;a.Utf8;var c=a.Base64,l=r.algo.EvpKDF,h=s.Cipher=o.extend({cfg:n.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,r){this.cfg=this.cfg.extend(r),this._xformMode=e,this._key=t,this.reset()},reset:function(){o.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function e(e){return"string"==typeof e?w:v}return function(t){return{encrypt:function(r,s,n){return e(s).encrypt(t,r,s,n)},decrypt:function(r,s,n){return e(s).decrypt(t,r,s,n)}}}}()});s.StreamCipher=h.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var u=r.mode={},d=s.BlockCipherMode=n.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),p=u.CBC=function(){var e=d.extend();function r(e,r,s){var n,i=this._iv;i?(n=i,this._iv=t):n=this._prevBlock;for(var o=0;o<s;o++)e[r+o]^=n[o]}return e.Encryptor=e.extend({processBlock:function(e,t){var s=this._cipher,n=s.blockSize;r.call(this,e,t,n),s.encryptBlock(e,t),this._prevBlock=e.slice(t,t+n)}}),e.Decryptor=e.extend({processBlock:function(e,t){var s=this._cipher,n=s.blockSize,i=e.slice(t,t+n);s.decryptBlock(e,t),r.call(this,e,t,n),this._prevBlock=i}}),e}(),f=(r.pad={}).Pkcs7={pad:function(e,t){for(var r=4*t,s=r-e.sigBytes%r,n=s<<24|s<<16|s<<8|s,o=[],a=0;a<s;a+=4)o.push(n);var c=i.create(o,s);e.concat(c)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}};s.BlockCipher=h.extend({cfg:h.cfg.extend({mode:p,padding:f}),reset:function(){var e;h.reset.call(this);var t=this.cfg,r=t.iv,s=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=s.createEncryptor:(e=s.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,r&&r.words):(this._mode=e.call(s,this,r&&r.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4});var g=s.CipherParams=n.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),m=(r.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext,r=e.salt;return(r?i.create([1398893684,1701076831]).concat(r).concat(t):t).toString(c)},parse:function(e){var t,r=c.parse(e),s=r.words;return 1398893684==s[0]&&1701076831==s[1]&&(t=i.create(s.slice(2,4)),s.splice(0,4),r.sigBytes-=16),g.create({ciphertext:r,salt:t})}},v=s.SerializableCipher=n.extend({cfg:n.extend({format:m}),encrypt:function(e,t,r,s){s=this.cfg.extend(s);var n=e.createEncryptor(r,s),i=n.finalize(t),o=n.cfg;return g.create({ciphertext:i,key:r,iv:o.iv,algorithm:e,mode:o.mode,padding:o.padding,blockSize:e.blockSize,formatter:s.format})},decrypt:function(e,t,r,s){return s=this.cfg.extend(s),t=this._parse(t,s.format),e.createDecryptor(r,s).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),y=(r.kdf={}).OpenSSL={execute:function(e,t,r,s,n){if(s||(s=i.random(8)),n)o=l.create({keySize:t+r,hasher:n}).compute(e,s);else var o=l.create({keySize:t+r}).compute(e,s);var a=i.create(o.words.slice(t),4*r);return o.sigBytes=4*t,g.create({key:o,iv:a,salt:s})}},w=s.PasswordBasedCipher=v.extend({cfg:v.cfg.extend({kdf:y}),encrypt:function(e,t,r,s){var n=(s=this.cfg.extend(s)).kdf.execute(r,e.keySize,e.ivSize,s.salt,s.hasher);s.iv=n.iv;var i=v.encrypt.call(this,e,t,n.key,s);return i.mixIn(n),i},decrypt:function(e,t,r,s){s=this.cfg.extend(s),t=this._parse(t,s.format);var n=s.kdf.execute(r,e.keySize,e.ivSize,t.salt,s.hasher);return s.iv=n.iv,v.decrypt.call(this,e,t,n.key,s)}})}()))),yn.exports;var e}var _n,bn={exports:{}};var kn,Sn={exports:{}};var En,xn={exports:{}};function Tn(){return En||(En=1,xn.exports=(e=xs(),wn(),
/** @preserve
         * Counter block mode compatible with  Dr Brian Gladman fileenc.c
         * derived from CryptoJS.mode.CTR
         * <NAME_EMAIL>
         */
e.mode.CTRGladman=function(){var t=e.lib.BlockCipherMode.extend();function r(e){if(255&~(e>>24))e+=1<<24;else{var t=e>>16&255,r=e>>8&255,s=255&e;255===t?(t=0,255===r?(r=0,255===s?s=0:++s):++r):++t,e=0,e+=t<<16,e+=r<<8,e+=s}return e}function s(e){return 0===(e[0]=r(e[0]))&&(e[1]=r(e[1])),e}var n=t.Encryptor=t.extend({processBlock:function(e,t){var r=this._cipher,n=r.blockSize,i=this._iv,o=this._counter;i&&(o=this._counter=i.slice(0),this._iv=void 0),s(o);var a=o.slice(0);r.encryptBlock(a,0);for(var c=0;c<n;c++)e[t+c]^=a[c]}});return t.Decryptor=n,t}(),e.mode.CTRGladman)),xn.exports;var e}var Cn,An={exports:{}};var Pn,Bn={exports:{}};var Nn,jn={exports:{}};var In,Dn={exports:{}};var On,Rn={exports:{}};var Vn,$n={exports:{}};var Ln,Un={exports:{}};var Mn,zn={exports:{}};var Fn,Hn={exports:{}};var qn,Wn={exports:{}};function Kn(){return qn||(qn=1,Wn.exports=(e=xs(),Vs(),Fs(),mn(),wn(),function(){var t=e,r=t.lib,s=r.WordArray,n=r.BlockCipher,i=t.algo,o=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],a=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],c=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],l=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],h=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],u=i.DES=n.extend({_doReset:function(){for(var e=this._key.words,t=[],r=0;r<56;r++){var s=o[r]-1;t[r]=e[s>>>5]>>>31-s%32&1}for(var n=this._subKeys=[],i=0;i<16;i++){var l=n[i]=[],h=c[i];for(r=0;r<24;r++)l[r/6|0]|=t[(a[r]-1+h)%28]<<31-r%6,l[4+(r/6|0)]|=t[28+(a[r+24]-1+h)%28]<<31-r%6;for(l[0]=l[0]<<1|l[0]>>>31,r=1;r<7;r++)l[r]=l[r]>>>4*(r-1)+3;l[7]=l[7]<<5|l[7]>>>27}var u=this._invSubKeys=[];for(r=0;r<16;r++)u[r]=n[15-r]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._subKeys)},decryptBlock:function(e,t){this._doCryptBlock(e,t,this._invSubKeys)},_doCryptBlock:function(e,t,r){this._lBlock=e[t],this._rBlock=e[t+1],d.call(this,4,252645135),d.call(this,16,65535),p.call(this,2,858993459),p.call(this,8,16711935),d.call(this,1,1431655765);for(var s=0;s<16;s++){for(var n=r[s],i=this._lBlock,o=this._rBlock,a=0,c=0;c<8;c++)a|=l[c][((o^n[c])&h[c])>>>0];this._lBlock=o,this._rBlock=i^a}var u=this._lBlock;this._lBlock=this._rBlock,this._rBlock=u,d.call(this,1,1431655765),p.call(this,8,16711935),p.call(this,2,858993459),d.call(this,16,65535),d.call(this,4,252645135),e[t]=this._lBlock,e[t+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function d(e,t){var r=(this._lBlock>>>e^this._rBlock)&t;this._rBlock^=r,this._lBlock^=r<<e}function p(e,t){var r=(this._rBlock>>>e^this._lBlock)&t;this._lBlock^=r,this._rBlock^=r<<e}t.DES=n._createHelper(u);var f=i.TripleDES=n.extend({_doReset:function(){var e=this._key.words;if(2!==e.length&&4!==e.length&&e.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var t=e.slice(0,2),r=e.length<4?e.slice(0,2):e.slice(2,4),n=e.length<6?e.slice(0,2):e.slice(4,6);this._des1=u.createEncryptor(s.create(t)),this._des2=u.createEncryptor(s.create(r)),this._des3=u.createEncryptor(s.create(n))},encryptBlock:function(e,t){this._des1.encryptBlock(e,t),this._des2.decryptBlock(e,t),this._des3.encryptBlock(e,t)},decryptBlock:function(e,t){this._des3.decryptBlock(e,t),this._des2.encryptBlock(e,t),this._des1.decryptBlock(e,t)},keySize:6,ivSize:2,blockSize:2});t.TripleDES=n._createHelper(f)}(),e.TripleDES)),Wn.exports;var e}var Jn,Gn={exports:{}};var Yn,Xn={exports:{}};var Qn,Zn={exports:{}};var ei,ti,ri,si,ni,ii,oi,ai={exports:{}};function ci(){return ei||(ei=1,ai.exports=(e=xs(),Vs(),Fs(),mn(),wn(),function(){var t=e,r=t.lib.BlockCipher,s=t.algo;const n=16,i=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731],o=[[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946],[1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055],[3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504],[976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462]];var a={pbox:[],sbox:[]};function c(e,t){let r=t>>24&255,s=t>>16&255,n=t>>8&255,i=255&t,o=e.sbox[0][r]+e.sbox[1][s];return o^=e.sbox[2][n],o+=e.sbox[3][i],o}function l(e,t,r){let s,i=t,o=r;for(let a=0;a<n;++a)i^=e.pbox[a],o=c(e,i)^o,s=i,i=o,o=s;return s=i,i=o,o=s,o^=e.pbox[n],i^=e.pbox[n+1],{left:i,right:o}}function h(e,t,r){let s,i=t,o=r;for(let a=n+1;a>1;--a)i^=e.pbox[a],o=c(e,i)^o,s=i,i=o,o=s;return s=i,i=o,o=s,o^=e.pbox[1],i^=e.pbox[0],{left:i,right:o}}function u(e,t,r){for(let n=0;n<4;n++){e.sbox[n]=[];for(let t=0;t<256;t++)e.sbox[n][t]=o[n][t]}let s=0;for(let o=0;o<n+2;o++)e.pbox[o]=i[o]^t[s],s++,s>=r&&(s=0);let a=0,c=0,h=0;for(let i=0;i<n+2;i+=2)h=l(e,a,c),a=h.left,c=h.right,e.pbox[i]=a,e.pbox[i+1]=c;for(let n=0;n<4;n++)for(let t=0;t<256;t+=2)h=l(e,a,c),a=h.left,c=h.right,e.sbox[n][t]=a,e.sbox[n][t+1]=c;return!0}var d=s.Blowfish=r.extend({_doReset:function(){if(this._keyPriorReset!==this._key){var e=this._keyPriorReset=this._key,t=e.words,r=e.sigBytes/4;u(a,t,r)}},encryptBlock:function(e,t){var r=l(a,e[t],e[t+1]);e[t]=r.left,e[t+1]=r.right},decryptBlock:function(e,t){var r=h(a,e[t],e[t+1]);e[t]=r.left,e[t+1]=r.right},blockSize:2,keySize:4,ivSize:2});t.Blowfish=r._createHelper(d)}(),e.Blowfish)),ai.exports;var e}bs.exports=function(e){return e}(xs(),As(),Ns(),Ds(),Vs(),Us(),Fs(),Ws(),Gs(),Ys||(Ys=1,Xs.exports=(oi=xs(),Gs(),ri=(ti=oi).lib.WordArray,si=ti.algo,ni=si.SHA256,ii=si.SHA224=ni.extend({_doReset:function(){this._hash=new ri.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var e=ni._doFinalize.call(this);return e.sigBytes-=4,e}}),ti.SHA224=ni._createHelper(ii),ti.HmacSHA224=ni._createHmacHelper(ii),oi.SHA224)),en(),function(){return tn||(tn=1,rn.exports=(a=xs(),As(),en(),t=(e=a).x64,r=t.Word,s=t.WordArray,n=e.algo,i=n.SHA512,o=n.SHA384=i.extend({_doReset:function(){this._hash=new s.init([new r.init(3418070365,3238371032),new r.init(1654270250,914150663),new r.init(2438529370,812702999),new r.init(355462360,4144912697),new r.init(1731405415,4290775857),new r.init(2394180231,1750603025),new r.init(3675008525,1694076839),new r.init(1203062813,3204075428)])},_doFinalize:function(){var e=i._doFinalize.call(this);return e.sigBytes-=16,e}}),e.SHA384=i._createHelper(o),e.HmacSHA384=i._createHmacHelper(o),a.SHA384)),rn.exports;var e,t,r,s,n,i,o,a}(),on(),function(){return an||(an=1,cn.exports=(e=xs(),
/** @preserve
            			(c) 2012 by Cédric Mesnil. All rights reserved.
        
            			Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:
        
            			    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
            			    - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.
        
            			THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
            			*/
function(){var t=e,r=t.lib,s=r.WordArray,n=r.Hasher,i=t.algo,o=s.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),a=s.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),c=s.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),l=s.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),h=s.create([0,1518500249,1859775393,2400959708,2840853838]),u=s.create([1352829926,1548603684,1836072691,2053994217,0]),d=i.RIPEMD160=n.extend({_doReset:function(){this._hash=s.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var s=t+r,n=e[s];e[s]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8)}var i,d,w,_,b,k,S,E,x,T,C,A=this._hash.words,P=h.words,B=u.words,N=o.words,j=a.words,I=c.words,D=l.words;for(k=i=A[0],S=d=A[1],E=w=A[2],x=_=A[3],T=b=A[4],r=0;r<80;r+=1)C=i+e[t+N[r]]|0,C+=r<16?p(d,w,_)+P[0]:r<32?f(d,w,_)+P[1]:r<48?g(d,w,_)+P[2]:r<64?m(d,w,_)+P[3]:v(d,w,_)+P[4],C=(C=y(C|=0,I[r]))+b|0,i=b,b=_,_=y(w,10),w=d,d=C,C=k+e[t+j[r]]|0,C+=r<16?v(S,E,x)+B[0]:r<32?m(S,E,x)+B[1]:r<48?g(S,E,x)+B[2]:r<64?f(S,E,x)+B[3]:p(S,E,x)+B[4],C=(C=y(C|=0,D[r]))+T|0,k=T,T=x,x=y(E,10),E=S,S=C;C=A[1]+w+x|0,A[1]=A[2]+_+T|0,A[2]=A[3]+b+k|0,A[3]=A[4]+i+S|0,A[4]=A[0]+d+E|0,A[0]=C},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,s=8*e.sigBytes;t[s>>>5]|=128<<24-s%32,t[14+(s+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),e.sigBytes=4*(t.length+1),this._process();for(var n=this._hash,i=n.words,o=0;o<5;o++){var a=i[o];i[o]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8)}return n},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}});function p(e,t,r){return e^t^r}function f(e,t,r){return e&t|~e&r}function g(e,t,r){return(e|~t)^r}function m(e,t,r){return e&r|t&~r}function v(e,t,r){return e^(t|~r)}function y(e,t){return e<<t|e>>>32-t}t.RIPEMD160=n._createHelper(d),t.HmacRIPEMD160=n._createHmacHelper(d)}(),e.RIPEMD160)),cn.exports;var e}(),un(),function(){return dn||(dn=1,pn.exports=(c=xs(),Gs(),un(),t=(e=c).lib,r=t.Base,s=t.WordArray,n=e.algo,i=n.SHA256,o=n.HMAC,a=n.PBKDF2=r.extend({cfg:r.extend({keySize:4,hasher:i,iterations:25e4}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r=this.cfg,n=o.create(r.hasher,e),i=s.create(),a=s.create([1]),c=i.words,l=a.words,h=r.keySize,u=r.iterations;c.length<h;){var d=n.update(t).finalize(a);n.reset();for(var p=d.words,f=p.length,g=d,m=1;m<u;m++){g=n.finalize(g),n.reset();for(var v=g.words,y=0;y<f;y++)p[y]^=v[y]}i.concat(d),l[0]++}return i.sigBytes=4*h,i}}),e.PBKDF2=function(e,t,r){return a.create(r).compute(e,t)},c.PBKDF2)),pn.exports;var e,t,r,s,n,i,o,a,c}(),mn(),wn(),function(){return _n||(_n=1,bn.exports=(e=xs(),wn(),e.mode.CFB=function(){var t=e.lib.BlockCipherMode.extend();function r(e,t,r,s){var n,i=this._iv;i?(n=i.slice(0),this._iv=void 0):n=this._prevBlock,s.encryptBlock(n,0);for(var o=0;o<r;o++)e[t+o]^=n[o]}return t.Encryptor=t.extend({processBlock:function(e,t){var s=this._cipher,n=s.blockSize;r.call(this,e,t,n,s),this._prevBlock=e.slice(t,t+n)}}),t.Decryptor=t.extend({processBlock:function(e,t){var s=this._cipher,n=s.blockSize,i=e.slice(t,t+n);r.call(this,e,t,n,s),this._prevBlock=i}}),t}(),e.mode.CFB)),bn.exports;var e}(),function(){return kn||(kn=1,Sn.exports=(r=xs(),wn(),r.mode.CTR=(e=r.lib.BlockCipherMode.extend(),t=e.Encryptor=e.extend({processBlock:function(e,t){var r=this._cipher,s=r.blockSize,n=this._iv,i=this._counter;n&&(i=this._counter=n.slice(0),this._iv=void 0);var o=i.slice(0);r.encryptBlock(o,0),i[s-1]=i[s-1]+1|0;for(var a=0;a<s;a++)e[t+a]^=o[a]}}),e.Decryptor=t,e),r.mode.CTR)),Sn.exports;var e,t,r}(),Tn(),function(){return Cn||(Cn=1,An.exports=(r=xs(),wn(),r.mode.OFB=(e=r.lib.BlockCipherMode.extend(),t=e.Encryptor=e.extend({processBlock:function(e,t){var r=this._cipher,s=r.blockSize,n=this._iv,i=this._keystream;n&&(i=this._keystream=n.slice(0),this._iv=void 0),r.encryptBlock(i,0);for(var o=0;o<s;o++)e[t+o]^=i[o]}}),e.Decryptor=t,e),r.mode.OFB)),An.exports;var e,t,r}(),function(){return Pn||(Pn=1,Bn.exports=(t=xs(),wn(),t.mode.ECB=((e=t.lib.BlockCipherMode.extend()).Encryptor=e.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),e.Decryptor=e.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),e),t.mode.ECB)),Bn.exports;var e,t}(),function(){return Nn||(Nn=1,jn.exports=(e=xs(),wn(),e.pad.AnsiX923={pad:function(e,t){var r=e.sigBytes,s=4*t,n=s-r%s,i=r+n-1;e.clamp(),e.words[i>>>2]|=n<<24-i%4*8,e.sigBytes+=n},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},e.pad.Ansix923)),jn.exports;var e}(),function(){return In||(In=1,Dn.exports=(e=xs(),wn(),e.pad.Iso10126={pad:function(t,r){var s=4*r,n=s-t.sigBytes%s;t.concat(e.lib.WordArray.random(n-1)).concat(e.lib.WordArray.create([n<<24],1))},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},e.pad.Iso10126)),Dn.exports;var e}(),function(){return On||(On=1,Rn.exports=(e=xs(),wn(),e.pad.Iso97971={pad:function(t,r){t.concat(e.lib.WordArray.create([2147483648],1)),e.pad.ZeroPadding.pad(t,r)},unpad:function(t){e.pad.ZeroPadding.unpad(t),t.sigBytes--}},e.pad.Iso97971)),Rn.exports;var e}(),function(){return Vn||(Vn=1,$n.exports=(e=xs(),wn(),e.pad.ZeroPadding={pad:function(e,t){var r=4*t;e.clamp(),e.sigBytes+=r-(e.sigBytes%r||r)},unpad:function(e){var t=e.words,r=e.sigBytes-1;for(r=e.sigBytes-1;r>=0;r--)if(t[r>>>2]>>>24-r%4*8&255){e.sigBytes=r+1;break}}},e.pad.ZeroPadding)),$n.exports;var e}(),function(){return Ln||(Ln=1,Un.exports=(e=xs(),wn(),e.pad.NoPadding={pad:function(){},unpad:function(){}},e.pad.NoPadding)),Un.exports;var e}(),function(){return Mn||(Mn=1,zn.exports=(s=xs(),wn(),t=(e=s).lib.CipherParams,r=e.enc.Hex,e.format.Hex={stringify:function(e){return e.ciphertext.toString(r)},parse:function(e){var s=r.parse(e);return t.create({ciphertext:s})}},s.format.Hex)),zn.exports;var e,t,r,s}(),function(){return Fn||(Fn=1,Hn.exports=(e=xs(),Vs(),Fs(),mn(),wn(),function(){var t=e,r=t.lib.BlockCipher,s=t.algo,n=[],i=[],o=[],a=[],c=[],l=[],h=[],u=[],d=[],p=[];!function(){for(var e=[],t=0;t<256;t++)e[t]=t<128?t<<1:t<<1^283;var r=0,s=0;for(t=0;t<256;t++){var f=s^s<<1^s<<2^s<<3^s<<4;f=f>>>8^255&f^99,n[r]=f,i[f]=r;var g=e[r],m=e[g],v=e[m],y=257*e[f]^16843008*f;o[r]=y<<24|y>>>8,a[r]=y<<16|y>>>16,c[r]=y<<8|y>>>24,l[r]=y,y=16843009*v^65537*m^257*g^16843008*r,h[f]=y<<24|y>>>8,u[f]=y<<16|y>>>16,d[f]=y<<8|y>>>24,p[f]=y,r?(r=g^e[e[e[v^g]]],s^=e[e[s]]):r=s=1}}();var f=[0,1,2,4,8,16,32,64,128,27,54],g=s.AES=r.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e=this._keyPriorReset=this._key,t=e.words,r=e.sigBytes/4,s=4*((this._nRounds=r+6)+1),i=this._keySchedule=[],o=0;o<s;o++)o<r?i[o]=t[o]:(l=i[o-1],o%r?r>6&&o%r==4&&(l=n[l>>>24]<<24|n[l>>>16&255]<<16|n[l>>>8&255]<<8|n[255&l]):(l=n[(l=l<<8|l>>>24)>>>24]<<24|n[l>>>16&255]<<16|n[l>>>8&255]<<8|n[255&l],l^=f[o/r|0]<<24),i[o]=i[o-r]^l);for(var a=this._invKeySchedule=[],c=0;c<s;c++){if(o=s-c,c%4)var l=i[o];else l=i[o-4];a[c]=c<4||o<=4?l:h[n[l>>>24]]^u[n[l>>>16&255]]^d[n[l>>>8&255]]^p[n[255&l]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,o,a,c,l,n)},decryptBlock:function(e,t){var r=e[t+1];e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,h,u,d,p,i),r=e[t+1],e[t+1]=e[t+3],e[t+3]=r},_doCryptBlock:function(e,t,r,s,n,i,o,a){for(var c=this._nRounds,l=e[t]^r[0],h=e[t+1]^r[1],u=e[t+2]^r[2],d=e[t+3]^r[3],p=4,f=1;f<c;f++){var g=s[l>>>24]^n[h>>>16&255]^i[u>>>8&255]^o[255&d]^r[p++],m=s[h>>>24]^n[u>>>16&255]^i[d>>>8&255]^o[255&l]^r[p++],v=s[u>>>24]^n[d>>>16&255]^i[l>>>8&255]^o[255&h]^r[p++],y=s[d>>>24]^n[l>>>16&255]^i[h>>>8&255]^o[255&u]^r[p++];l=g,h=m,u=v,d=y}g=(a[l>>>24]<<24|a[h>>>16&255]<<16|a[u>>>8&255]<<8|a[255&d])^r[p++],m=(a[h>>>24]<<24|a[u>>>16&255]<<16|a[d>>>8&255]<<8|a[255&l])^r[p++],v=(a[u>>>24]<<24|a[d>>>16&255]<<16|a[l>>>8&255]<<8|a[255&h])^r[p++],y=(a[d>>>24]<<24|a[l>>>16&255]<<16|a[h>>>8&255]<<8|a[255&u])^r[p++],e[t]=g,e[t+1]=m,e[t+2]=v,e[t+3]=y},keySize:8});t.AES=r._createHelper(g)}(),e.AES)),Hn.exports;var e}(),Kn(),function(){return Jn||(Jn=1,Gn.exports=(e=xs(),Vs(),Fs(),mn(),wn(),function(){var t=e,r=t.lib.StreamCipher,s=t.algo,n=s.RC4=r.extend({_doReset:function(){for(var e=this._key,t=e.words,r=e.sigBytes,s=this._S=[],n=0;n<256;n++)s[n]=n;n=0;for(var i=0;n<256;n++){var o=n%r,a=t[o>>>2]>>>24-o%4*8&255;i=(i+s[n]+a)%256;var c=s[n];s[n]=s[i],s[i]=c}this._i=this._j=0},_doProcessBlock:function(e,t){e[t]^=i.call(this)},keySize:8,ivSize:0});function i(){for(var e=this._S,t=this._i,r=this._j,s=0,n=0;n<4;n++){r=(r+e[t=(t+1)%256])%256;var i=e[t];e[t]=e[r],e[r]=i,s|=e[(e[t]+e[r])%256]<<24-8*n}return this._i=t,this._j=r,s}t.RC4=r._createHelper(n);var o=s.RC4Drop=n.extend({cfg:n.cfg.extend({drop:192}),_doReset:function(){n._doReset.call(this);for(var e=this.cfg.drop;e>0;e--)i.call(this)}});t.RC4Drop=r._createHelper(o)}(),e.RC4)),Gn.exports;var e}(),function(){return Yn||(Yn=1,Xn.exports=(e=xs(),Vs(),Fs(),mn(),wn(),function(){var t=e,r=t.lib.StreamCipher,s=t.algo,n=[],i=[],o=[],a=s.Rabbit=r.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,r=0;r<4;r++)e[r]=16711935&(e[r]<<8|e[r]>>>24)|4278255360&(e[r]<<24|e[r]>>>8);var s=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],n=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];for(this._b=0,r=0;r<4;r++)c.call(this);for(r=0;r<8;r++)n[r]^=s[r+4&7];if(t){var i=t.words,o=i[0],a=i[1],l=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),h=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),u=l>>>16|4294901760&h,d=h<<16|65535&l;for(n[0]^=l,n[1]^=u,n[2]^=h,n[3]^=d,n[4]^=l,n[5]^=u,n[6]^=h,n[7]^=d,r=0;r<4;r++)c.call(this)}},_doProcessBlock:function(e,t){var r=this._X;c.call(this),n[0]=r[0]^r[5]>>>16^r[3]<<16,n[1]=r[2]^r[7]>>>16^r[5]<<16,n[2]=r[4]^r[1]>>>16^r[7]<<16,n[3]=r[6]^r[3]>>>16^r[1]<<16;for(var s=0;s<4;s++)n[s]=16711935&(n[s]<<8|n[s]>>>24)|4278255360&(n[s]<<24|n[s]>>>8),e[t+s]^=n[s]},blockSize:4,ivSize:2});function c(){for(var e=this._X,t=this._C,r=0;r<8;r++)i[r]=t[r];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<i[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<i[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<i[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<i[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<i[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<i[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<i[6]>>>0?1:0)|0,this._b=t[7]>>>0<i[7]>>>0?1:0,r=0;r<8;r++){var s=e[r]+t[r],n=65535&s,a=s>>>16,c=((n*n>>>17)+n*a>>>15)+a*a,l=((4294901760&s)*s|0)+((65535&s)*s|0);o[r]=c^l}e[0]=o[0]+(o[7]<<16|o[7]>>>16)+(o[6]<<16|o[6]>>>16)|0,e[1]=o[1]+(o[0]<<8|o[0]>>>24)+o[7]|0,e[2]=o[2]+(o[1]<<16|o[1]>>>16)+(o[0]<<16|o[0]>>>16)|0,e[3]=o[3]+(o[2]<<8|o[2]>>>24)+o[1]|0,e[4]=o[4]+(o[3]<<16|o[3]>>>16)+(o[2]<<16|o[2]>>>16)|0,e[5]=o[5]+(o[4]<<8|o[4]>>>24)+o[3]|0,e[6]=o[6]+(o[5]<<16|o[5]>>>16)+(o[4]<<16|o[4]>>>16)|0,e[7]=o[7]+(o[6]<<8|o[6]>>>24)+o[5]|0}t.Rabbit=r._createHelper(a)}(),e.Rabbit)),Xn.exports;var e}(),function(){return Qn||(Qn=1,Zn.exports=(e=xs(),Vs(),Fs(),mn(),wn(),function(){var t=e,r=t.lib.StreamCipher,s=t.algo,n=[],i=[],o=[],a=s.RabbitLegacy=r.extend({_doReset:function(){var e=this._key.words,t=this.cfg.iv,r=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],s=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];this._b=0;for(var n=0;n<4;n++)c.call(this);for(n=0;n<8;n++)s[n]^=r[n+4&7];if(t){var i=t.words,o=i[0],a=i[1],l=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),h=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),u=l>>>16|4294901760&h,d=h<<16|65535&l;for(s[0]^=l,s[1]^=u,s[2]^=h,s[3]^=d,s[4]^=l,s[5]^=u,s[6]^=h,s[7]^=d,n=0;n<4;n++)c.call(this)}},_doProcessBlock:function(e,t){var r=this._X;c.call(this),n[0]=r[0]^r[5]>>>16^r[3]<<16,n[1]=r[2]^r[7]>>>16^r[5]<<16,n[2]=r[4]^r[1]>>>16^r[7]<<16,n[3]=r[6]^r[3]>>>16^r[1]<<16;for(var s=0;s<4;s++)n[s]=16711935&(n[s]<<8|n[s]>>>24)|4278255360&(n[s]<<24|n[s]>>>8),e[t+s]^=n[s]},blockSize:4,ivSize:2});function c(){for(var e=this._X,t=this._C,r=0;r<8;r++)i[r]=t[r];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<i[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<i[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<i[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<i[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<i[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<i[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<i[6]>>>0?1:0)|0,this._b=t[7]>>>0<i[7]>>>0?1:0,r=0;r<8;r++){var s=e[r]+t[r],n=65535&s,a=s>>>16,c=((n*n>>>17)+n*a>>>15)+a*a,l=((4294901760&s)*s|0)+((65535&s)*s|0);o[r]=c^l}e[0]=o[0]+(o[7]<<16|o[7]>>>16)+(o[6]<<16|o[6]>>>16)|0,e[1]=o[1]+(o[0]<<8|o[0]>>>24)+o[7]|0,e[2]=o[2]+(o[1]<<16|o[1]>>>16)+(o[0]<<16|o[0]>>>16)|0,e[3]=o[3]+(o[2]<<8|o[2]>>>24)+o[1]|0,e[4]=o[4]+(o[3]<<16|o[3]>>>16)+(o[2]<<16|o[2]>>>16)|0,e[5]=o[5]+(o[4]<<8|o[4]>>>24)+o[3]|0,e[6]=o[6]+(o[5]<<16|o[5]>>>16)+(o[4]<<16|o[4]>>>16)|0,e[7]=o[7]+(o[6]<<8|o[6]>>>24)+o[5]|0}t.RabbitLegacy=r._createHelper(a)}(),e.RabbitLegacy)),Zn.exports;var e}(),ci());const li=j(bs.exports),hi=e=>li.SHA256(e).toString(),ui=()=>li.lib.WordArray.random(32).toString(),di=function(t,r,s){let n,i;const o="function"==typeof r;function h(t,s){const h=e.getCurrentInstance();(t=t||h&&e.inject(l,null))&&c(t),(t=a)._s.has(n)||(o?b(n,r,i,t):function(t,r,s){const{state:n,actions:i,getters:o}=r,a=s.state.value[t];let l;l=b(t,function(){a||(s.state.value[t]=n?n():{});const r=e.toRefs(s.state.value[t]);return w(r,i,Object.keys(o||{}).reduce((r,n)=>(r[n]=e.markRaw(e.computed(()=>{c(s);const e=s._s.get(t);return o[n].call(e,e)})),r),{}))},r,s,0,!0)}(n,i,t));return t._s.get(n)}return n=t,i=o?s:r,h.$id=n,h}("auth",()=>{const r=e.ref(null),s=e.ref(""),n=e.ref(!1),i=e.ref(null),o=e.computed(()=>!!s.value&&!!r.value),a=e.computed(()=>o.value),c=e.computed(()=>{var e;return(null==(e=r.value)?void 0:e.nickname)||""}),l=e.computed(()=>{var e;return(null==(e=r.value)?void 0:e.id)||""}),h=e.computed(()=>{var e;return(null==(e=r.value)?void 0:e.avatar_url)||""}),u=e=>{e.user&&(r.value=e.user),e.token&&(s.value=e.token,uni.setStorageSync("auth_token",e.token)),i.value=null},d=()=>{r.value=null,s.value="",uni.removeStorageSync("auth_token"),i.value=null},p=async()=>{const e=uni.getStorageSync("auth_token");if(!e)return!1;n.value=!0,i.value=null;try{const r=await(async e=>{try{if(!e)return{success:!1,error:"令牌不存在"};const{data:r,error:s}=await ws().from("user_sessions").select("user_id, expires_at").eq("token",e).single();if(s)return"PGRST116"===s.code?{success:!1,error:"无效的令牌"}:(t("error","at services/auth.ts:336","验证会话失败:",s),{success:!1,error:"验证会话失败"});if(new Date(r.expires_at)<new Date)return await ws().from("user_sessions").delete().eq("token",e),{success:!1,error:"令牌已过期"};const{data:n,error:i}=await ws().from("users").select("id, phone, nickname, avatar_url, gender, bio").eq("id",r.user_id).single();return i?(t("error","at services/auth.ts:359","获取用户信息失败:",i),{success:!1,error:"获取用户信息失败"}):{success:!0,data:{user:{id:n.id,phone:n.phone,nickname:n.nickname,gender:n.gender,avatar_url:n.avatar_url,bio:n.bio}}}}catch(i){return t("error","at services/auth.ts:380","验证会话异常:",i),{success:!1,error:i.message||"验证会话失败"}}})(e);return r.success&&r.data?(u({user:r.data.user,token:e}),!0):(d(),!1)}catch(r){return t("error","at stores/auth.ts:151","验证会话失败:",r),d(),!1}finally{n.value=!1}};return{user:r,token:s,isLoading:n,error:i,isAuthenticated:o,isLoggedIn:a,username:c,userId:l,avatarUrl:h,registerWithPhone:async e=>{n.value=!0,i.value=null;try{const r=await(async e=>{try{const{phone:s,password:n,nickname:i,gender:o=0,avatarUrl:a="",invitationCode:c=""}=e,l={0:"undisclosed",1:"male",2:"female"}[o]||"undisclosed";if(t("log","at services/auth.ts:132","[注册流程] 开始手机号注册:",{phone:s,nickname:i,gender:o,timestamp:(new Date).toISOString()}),!s||!n||!i)return t("error","at services/auth.ts:136","[注册流程] 表单数据不完整:",{phone:!!s,password:!!n,nickname:!!i}),{success:!1,error:"手机号、密码和昵称不能为空"};if(n.length<6)return t("error","at services/auth.ts:141","[注册流程] 密码长度不足:",n.length),{success:!1,error:"密码长度不能少于6位"};t("log","at services/auth.ts:146","[注册流程] 开始密码加密");const h=hi(n);t("log","at services/auth.ts:148","[注册流程] 密码加密完成"),t("log","at services/auth.ts:151","[注册流程] 开始创建用户记录");const{data:u,error:d}=await ws().from("users").insert({phone:s,password_hash:h,nickname:i,gender:l,avatar_url:a}).select().single();if(t("log","at services/auth.ts:164","[注册流程] 用户创建结果:",{user:u,userError:d}),d){if(t("error","at services/auth.ts:166","[注册流程] 创建用户失败:",d),"23505"===d.code){if(d.message.includes("phone"))return{success:!1,error:"手机号已被注册"};if(d.message.includes("nickname"))return{success:!1,error:"昵称已被使用"}}return{success:!1,error:"注册失败，请重试"}}t("log","at services/auth.ts:182","[注册流程] 开始创建用户会话");const p=ui(),f=new Date(Date.now()+6048e5),{data:g,error:m}=await ws().from("user_sessions").insert({user_id:u.id,token:p,expires_at:f}).select().single();if(t("log","at services/auth.ts:196","[注册流程] 会话创建结果:",{session:g,sessionError:m}),m)return t("error","at services/auth.ts:198","[注册流程] 创建会话失败:",m),{success:!1,error:"注册失败，请重试"};if(c){t("log","at services/auth.ts:204","[注册流程] 开始处理邀请码:",c);try{const{data:e}=await ws().from("invitations").select("current_uses").eq("code",c).single();e&&await ws().from("invitations").update({current_uses:(e.current_uses||0)+1,used_by:u.id}).eq("code",c)}catch(r){t("warn","at services/auth.ts:222","[注册流程] 邀请码处理失败，但不影响注册:",r)}}return t("log","at services/auth.ts:226","[注册流程] 注册成功:",u.id),{success:!0,data:{user:u,token:p},message:"注册成功"}}catch(i){return t("error","at services/auth.ts:236","注册失败:",i),{success:!1,error:i.message||"注册失败，请重试"}}})(e);return r.success&&r.data?(u(r.data),r):(i.value=r.error||"注册失败",r)}catch(r){return i.value=r.message||"注册失败",t("error","at stores/auth.ts:74","注册失败:",r),{success:!1,error:r.message||"注册失败"}}finally{n.value=!1}},login:async e=>{n.value=!0,i.value=null;try{const r=await(async({phone:e,password:r})=>{try{t("log","at services/auth.ts:249","开始登录:",{phone:e});const{data:s,error:n}=await ws().from("users").select("id, phone, password_hash, nickname, avatar_url, gender").eq("phone",e).single();if(n)return"PGRST116"===n.code?{success:!1,error:"用户不存在"}:(t("error","at services/auth.ts:262","查找用户失败:",n),{success:!1,error:"登录失败"});const i=hi(r);if(s.password_hash!==i)return{success:!1,error:"密码错误"};const o=ui(),a=new Date(Date.now()+6048e5),{data:c,error:l}=await ws().from("user_sessions").insert({user_id:s.id,token:o,expires_at:a}).select().single();return l?(t("error","at services/auth.ts:287","创建会话失败:",l),{success:!1,error:"登录失败"}):(delete s.password_hash,{success:!0,data:{user:{id:s.id,phone:s.phone,nickname:s.nickname,gender:s.gender,avatar_url:s.avatar_url},token:o},message:"登录成功"})}catch(i){return t("error","at services/auth.ts:309","登录失败:",i),{success:!1,error:i.message||"登录失败，请重试"}}})(e);return r.success&&r.data?(u(r.data),r):(i.value=r.error||"登录失败",r)}catch(r){return i.value=r.message||"登录失败",t("error","at stores/auth.ts:99","登录失败:",r),{success:!1,error:r.message||"登录失败"}}finally{n.value=!1}},logout:async()=>{n.value=!0,i.value=null;try{await(async e=>{try{return e&&await ws().from("user_sessions").delete().eq("token",e),{success:!0,message:"退出成功"}}catch(i){return t("error","at services/auth.ts:405","退出登录失败:",i),{success:!1,error:i.message||"退出失败"}}})(s.value),d()}catch(e){i.value=e.message||"退出失败",t("error","at stores/auth.ts:118","退出失败:",e)}finally{n.value=!1}},validateSession:p,getCurrentUser:async()=>{try{return o.value||await p(),r.value}catch(e){return t("error","at stores/auth.ts:170","获取当前用户失败:",e),null}}}});var pi=Object.defineProperty,fi=(e,t,r)=>(((e,t,r)=>{t in e?pi(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r})(e,t+"",r),r);const gi=new class{constructor(){fi(this,"authStore",null)}getAuthStore(){return this.authStore||(this.authStore=di()),this.authStore}isLoggedIn(){return this.getAuthStore().isLoggedIn}async getCurrentUser(){const e=this.getAuthStore();return await e.getCurrentUser()}async validateSession(){const e=this.getAuthStore();return await e.validateSession()}async login(e){const t=this.getAuthStore();return await t.login(e)}async register(e){const t=this.getAuthStore();return await t.registerWithPhone(e)}async logout(){const e=this.getAuthStore();await e.logout()}getUserId(){return this.getAuthStore().userId}getUsername(){return this.getAuthStore().username}getAvatarUrl(){return this.getAuthStore().avatarUrl}getToken(){return this.getAuthStore().token}isLoading(){return this.getAuthStore().isLoading}getError(){return this.getAuthStore().error}clearError(){this.getAuthStore().error=null}},mi=(e="default")=>vi(e),vi=(e="default")=>{const t=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFEAA7","#DDA0DD","#98D8C8","#F7DC6F"],r=t[e.split("").reduce((e,t)=>e+t.charCodeAt(0),0)%t.length],s=e.charAt(0).toUpperCase();return`data:image/svg+xml;charset=utf-8,${encodeURIComponent(`<svg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg">\n    <circle cx="40" cy="40" r="40" fill="${r}"/>\n    <text x="40" y="50" font-family="Arial, sans-serif" font-size="32" font-weight="bold" text-anchor="middle" fill="white">${s}</text>\n  </svg>`)}`},yi=(e,r="anonymous")=>{if(e&&"string"==typeof e&&e.trim()){if(e.startsWith("blob:")&&!e.includes("valid"))return t("warn","at utils/avatar.ts:55","检测到无效的blob URL，使用默认头像:",e),mi(r||"anonymous");if(e.startsWith("http")||e.startsWith("data:")||e.startsWith("/"))return e;t("warn","at utils/avatar.ts:65","检测到无效的头像URL格式，使用默认头像:",e)}return mi(r||"anonymous")},wi=(e,r="YYYY-MM-DD HH:mm")=>{try{const t="string"==typeof e?new Date(e):e,s=t.getFullYear(),n=String(t.getMonth()+1).padStart(2,"0"),i=String(t.getDate()).padStart(2,"0"),o=String(t.getHours()).padStart(2,"0"),a=String(t.getMinutes()).padStart(2,"0"),c=String(t.getSeconds()).padStart(2,"0");return r.replace("YYYY",s.toString()).replace("MM",n).replace("DD",i).replace("HH",o).replace("mm",a).replace("ss",c)}catch(s){return t("error","at utils/time-utils.ts:91","格式化日期失败:",s),"日期错误"}},_i=(e,t)=>{const r=e.__vccOpts||e;for(const[s,n]of t)r[s]=n;return r},bi=_i({__name:"LoadingSpinner",props:{text:{type:String,default:"加载中..."},size:{type:String,default:"normal"}},setup:t=>(r,s)=>(e.openBlock(),e.createElementBlock("view",{class:"loading-container"},[e.createElementVNode("view",{class:e.normalizeClass(["spinner",[t.size]])},[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(8,t=>e.createElementVNode("view",{class:"spinner-item",key:t,style:e.normalizeStyle({transform:`rotate(${45*t}deg)`})},[e.createElementVNode("view",{class:"spinner-dot",style:e.normalizeStyle({animationDelay:.1*t+"s"})},null,4)],4)),64))],2),e.createElementVNode("text",{class:"loading-text"},e.toDisplayString(t.text),1)]))},[["__scopeId","data-v-ebc9152e"]]);const ki=_i({name:"VirtualList",props:{items:{type:Array,required:!0},itemHeight:{type:Number,default:200},containerHeight:{type:Number,default:600}},data:()=>({visibleCount:0,offsetY:0}),computed:{totalHeight(){return this.items.length*this.itemHeight},visibleStart(){return Math.floor(this.offsetY/this.itemHeight)},visibleEnd(){return this.visibleStart+this.visibleCount},visibleData(){return this.items.slice(this.visibleStart,this.visibleEnd)}},mounted(){this.visibleCount=Math.ceil(this.containerHeight/this.itemHeight)},methods:{handleScroll(e){const{scrollTop:t}=e.detail;this.offsetY=t,this.$emit("scroll",e)}}},[["render",function(t,r,s,n,i,o){return e.openBlock(),e.createElementBlock("view",{class:"virtual-list",onScroll:r[0]||(r[0]=(...e)=>o.handleScroll&&o.handleScroll(...e)),style:e.normalizeStyle({height:s.containerHeight+"px"})},[e.createElementVNode("view",{class:"virtual-list-phantom",style:e.normalizeStyle({height:o.totalHeight+"px"})},null,4),e.createElementVNode("view",{class:"virtual-list-content",style:e.normalizeStyle({transform:`translate3d(0,${i.offsetY}px,0)`})},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.visibleData,r=>(e.openBlock(),e.createElementBlock("view",{key:r.id,"data-id":r.id,class:"virtual-list-item",style:e.normalizeStyle({height:s.itemHeight+"px"})},[e.renderSlot(t.$slots,"default",{item:r},void 0,!0)],12,["data-id"]))),128))],4)],36)}],["__scopeId","data-v-ddd64502"]]),Si=_i({__name:"LazyImage",props:{src:{type:String,required:!0},placeholder:{type:String,default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},borderRadius:{type:[String,Number],default:0},immediate:{type:Boolean,default:!1},threshold:{type:Number,default:100}},emits:["load","error","click"],setup(t,{emit:r}){const s=t,n=r,i=e.ref(!1),o=e.ref(!1),a=e.ref(!1),c=e.ref(""),l=e.computed(()=>({width:"number"==typeof s.width?`${s.width}rpx`:s.width,height:"number"==typeof s.height?`${s.height}rpx`:s.height,borderRadius:"number"==typeof s.borderRadius?`${s.borderRadius}rpx`:s.borderRadius,overflow:"hidden",position:"relative"})),h=e.computed(()=>({width:"100%",height:"100%",display:"block"})),u=e.computed(()=>({width:"100%",height:"100%",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"#f5f5f5",position:"absolute",top:0,left:0})),d=()=>{i.value=!0,o.value=!1,n("load")},p=()=>{if(s.src&&s.src.startsWith("blob:"))return i.value=!1,void n("error");i.value=!1,o.value=!0,n("error")},f=e=>{n("click",e)},g=()=>{!c.value&&s.src&&(s.src.startsWith("blob:")||s.src.startsWith("http")||s.src.startsWith("/")||s.src.startsWith("data:"),c.value=s.src)};return e.watch(()=>s.src,e=>{e&&(i.value=!1,o.value=!1,c.value="",(a.value||s.immediate)&&g())}),e.onMounted(()=>{(()=>{if(s.immediate)return a.value=!0,void g();a.value=!0,g()})()}),(r,s)=>{const n=e.resolveComponent("uni-icons");return e.openBlock(),e.createElementBlock("view",{class:"lazy-image-container",style:e.normalizeStyle(l.value)},[i.value||o.value?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:0,class:"placeholder",style:e.normalizeStyle(u.value)},[e.createElementVNode("view",{class:"placeholder-content"},[e.createVNode(n,{type:"image",size:"40",color:"#ccc"}),e.createElementVNode("text",{class:"placeholder-text"},"加载中...")])],4)),e.withDirectives(e.createElementVNode("image",{src:c.value,mode:t.mode,"lazy-load":!0,"fade-show":!0,class:"lazy-image",style:e.normalizeStyle(h.value),onLoad:d,onError:p,onClick:f},null,44,["src","mode"]),[[e.vShow,i.value&&!o.value]]),o.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"error-placeholder",style:e.normalizeStyle(u.value)},[e.createElementVNode("view",{class:"error-content"},[e.createVNode(n,{type:"close",size:"40",color:"#999"}),e.createElementVNode("text",{class:"error-text"},"加载失败")])],4)):e.createCommentVNode("",!0)],4)}}},[["__scopeId","data-v-d908c289"]]);const Ei=_i({name:"IndexPage",components:{LoadingSpinner:bi,VirtualList:ki,FeedItem:_i(e.defineComponent({__name:"FeedItem",props:{item:{},formatTime:{type:Function,default:void 0}},emits:["click","more","like","comment","share","imagePreview"],setup(t,{emit:r}){const s=t,n=r,i=()=>{n("click",s.item)},o=()=>{n("more",s.item)},a=()=>{n("like",s.item)},c=()=>{n("comment",s.item)},l=()=>{n("share",s.item)};return(t,r)=>{return e.openBlock(),e.createElementBlock("view",{class:"social-card",onClick:i},[e.createElementVNode("view",{class:"card-inner"},[e.createElementVNode("image",{src:t.item.avatar_url||"/static/default-avatar.png",class:"avatar",mode:"aspectFit"},null,8,["src"]),e.createElementVNode("view",{class:"content-area"},[e.createElementVNode("view",{class:"header-info"},[e.createElementVNode("div",{class:"user-info"},[e.createElementVNode("span",{class:"username"},e.toDisplayString(t.item.username),1),e.createElementVNode("span",{class:"timestamp"},e.toDisplayString((h=t.item.created_at,s.formatTime?s.formatTime(h):"5小时前")),1)]),e.createElementVNode("view",{class:"more-btn",onClick:e.withModifiers(o,["stop"])},[e.createElementVNode("text",null,"⋯")])]),t.item.content?(e.openBlock(),e.createElementBlock("view",{key:0,class:"content-text"},[e.createElementVNode("text",null,e.toDisplayString(t.item.content),1)])):e.createCommentVNode("",!0),t.item.location?(e.openBlock(),e.createElementBlock("view",{key:1,class:"location"},[e.createElementVNode("text",{class:"location-text"},"📍 "+e.toDisplayString(t.item.location),1)])):e.createCommentVNode("",!0)]),t.item.images&&t.item.images.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"media-container"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.item.images,(r,s)=>(e.openBlock(),e.createBlock(Si,{key:s,src:r,class:"media",mode:"aspectFill",onClick:e.withModifiers(e=>{return r={images:t.item.images,index:s},void n("imagePreview",r);var r},["stop"])},null,8,["src","onClick"]))),128))])):e.createCommentVNode("",!0),e.createElementVNode("div",{class:"actions-container"},[e.createElementVNode("button",{class:"action-btn like-btn",onClick:e.withModifiers(a,["stop"])},[e.createElementVNode("text",{class:e.normalizeClass(["action-icon",{liked:t.item.liked}])},"❤️",2),e.createElementVNode("text",{class:"action-text"},e.toDisplayString(t.item.likes||0),1)]),e.createElementVNode("button",{class:"action-btn comment-btn",onClick:e.withModifiers(c,["stop"])},[e.createElementVNode("text",{class:"action-icon"},"💬"),e.createElementVNode("text",{class:"action-text"},e.toDisplayString(t.item.comments||0),1)]),e.createElementVNode("button",{class:"action-btn share-btn",onClick:e.withModifiers(l,["stop"])},[e.createElementVNode("text",{class:"action-icon"},"↗"),e.createElementVNode("text",{class:"action-text"},"分享")])])])]);var h}}}),[["__scopeId","data-v-5db360fa"]])},data:()=>({searchKeyword:"",currentTab:0,scrollLeft:0,loading:!1,hasMore:!0,touchStartX:0,touchStartY:0,minSwipeDistance:50,tabs:[{id:"recommend",name:"推荐"},{id:"following",name:"关注"},{id:"hot",name:"热门"},{id:"local",name:"附近"},{id:"interest",name:"兴趣"}],feedItems:[],page:1,limit:10,lastRefreshTime:null,refreshInterval:3e3}),computed:{isLoggedIn:()=>gi.isLoggedIn(),userAvatar:()=>yi(gi.getAvatarUrl(),gi.getUsername()),filteredFeedItems(){if(!Array.isArray(this.feedItems))return[];const e=this.tabs[this.currentTab].id;return"recommend"===e?this.feedItems:this.feedItems.filter(t=>t.tag===e)}},async onLoad(){t("log","at pages/index/index.vue:164","📱 首页加载中...");try{await gi.validateSession()}catch(e){t("error","at pages/index/index.vue:170","验证会话失败:",e)}await this.checkAuthStatus(),t("log","at pages/index/index.vue:177","🔄 加载首页内容..."),await this.fetchContentList(!0)},async onShow(){t("log","at pages/index/index.vue:181","📱 首页显示，检查是否需要刷新内容...");uni.getStorageSync("hasNewContent")||uni.getStorageSync("refreshFeed")?(t("log","at pages/index/index.vue:188","🔄 检测到新内容或用户请求刷新，刷新列表..."),uni.removeStorageSync("hasNewContent"),uni.removeStorageSync("refreshFeed"),await this.fetchContentList(!0),uni.showToast({title:"内容已更新",icon:"success",duration:1500})):this.feedItems&&0!==this.feedItems.length||await this.fetchContentList(!0)},async onPullDownRefresh(){const e=Date.now();if(this.lastRefreshTime&&e-this.lastRefreshTime<this.refreshInterval)return t("log","at pages/index/index.vue:217","⏱️ 刷新过于频繁，请稍后再试"),uni.showToast({title:"刷新过于频繁，请稍后再试",icon:"none",duration:1500}),void uni.stopPullDownRefresh();this.lastRefreshTime=e,t("log","at pages/index/index.vue:230","🔄 用户触发下拉刷新..."),await this.fetchContentList(!0)},methods:{formatTime:e=>(e=>{try{const t=new Date(e),r=(new Date).getTime()-t.getTime();if(r<0)return"刚刚";const s=Math.floor(r/1e3),n=Math.floor(s/60),i=Math.floor(n/60),o=Math.floor(i/24);return s<60?"刚刚":n<60?`${n}分钟前`:i<24?`${i}小时前`:o<7?`${o}天前`:wi(t)}catch(r){return t("error","at utils/time-utils.ts:53","格式化时间失败:",r),"未知时间"}})(e),handleTouchStart(e){const t=e.touches[0];this.touchStartX=t.clientX,this.touchStartY=t.clientY},handleTouchEnd(e){const t=e.changedTouches[0],r=t.clientX-this.touchStartX,s=t.clientY-this.touchStartY;r>this.minSwipeDistance&&Math.abs(s)<.5*Math.abs(r)&&this.navigateToProfile()},handleScroll(e){t("log","at pages/index/index.vue:272","滚动事件:",e)},async checkAuthStatus(){try{t("log","at pages/index/index.vue:280","🔍 检查用户登录状态...");const e=di();if(e.isLoggedIn&&e.user)return t("log","at pages/index/index.vue:285","✅ 用户已登录 (来自store):",e.username),!0;const r=await gi.getCurrentUser();return r&&gi.isLoggedIn()?(t("log","at pages/index/index.vue:293","✅ 用户已登录 (来自服务器):",gi.getUsername()),e.setUser(r),!0):(t("log","at pages/index/index.vue:298","❌ 用户未登录"),!1)}catch(e){return t("error","at pages/index/index.vue:302","检查登录状态失败:",e),!1}},async fetchContentList(e=!1){if(!this.loading)try{this.loading=!0,e&&(this.page=1,this.hasMore=!0,this.feedItems=[]),t("log","at pages/index/index.vue:323","🔄 开始获取内容列表...");const{posts:r,hasMore:s}=await this.fetchPostsFromServer();e?this.feedItems=r:this.feedItems.push(...r),this.hasMore=s,r.length>0&&this.page++}catch(r){t("error","at pages/index/index.vue:340","获取内容失败:",r),uni.showToast({title:"获取数据失败，请检查网络连接",icon:"none",duration:2e3})}finally{this.loading=!1,uni.stopPullDownRefresh()}},async fetchPostsFromServer(){var e;try{null==(e=this.tabs[this.currentTab])||e.id;const{posts:r,hasMore:s}=await(async(e=1,r=10)=>{try{const s=_s(di().token),n=(e-1)*r;let i=s.from("posts").select("\n        *,\n        users (\n          id,\n          nickname,\n          avatar_url\n        )\n      ").order("created_at",{ascending:!1}).range(n,n+r-1);const{data:o,error:a}=await i;if(a)throw t("error","at services/post.ts:51","获取帖子列表失败:",a),new Error("获取帖子列表失败: "+(a.message||"未知错误"));const c=o.map(e=>{var t,r,s;return{id:e.id,content:e.content,images:e.media||[],location:e.location,created_at:e.created_at,user_id:e.author_id,username:(null==(t=e.users)?void 0:t.nickname)||"未知用户",avatar_url:yi(null==(r=e.users)?void 0:r.avatar_url,null==(s=e.users)?void 0:s.nickname),like_count:e.likes_count||0,comment_count:e.comments_count||0,share_count:e.shares_count||0,liked:!1}});return{posts:c,hasMore:o.length===r}}catch(s){throw t("error","at services/post.ts:76","获取帖子数据异常:",s),s}})(this.page,this.limit);return{posts:r,hasMore:s}}catch(r){throw t("error","at pages/index/index.vue:367","从服务器获取帖子数据失败:",r),r}},switchTab(e){this.currentTab=e,this.scrollLeft=100*e,this.fetchContentList(!0)},handleSearch(){this.searchKeyword.trim()&&uni.navigateTo({url:`/pages/search/search?keyword=${encodeURIComponent(this.searchKeyword)}`})},async likePost(e){if(!gi.isLoggedIn())return void uni.showToast({title:"请先登录",icon:"none"});const r=e.liked,s=e.like_count;try{e.liked=!e.liked,e.like_count=e.liked?s+1:s-1;const t=this.feedItems.findIndex(t=>t.id===e.id);-1!==t&&(this.feedItems[t]={...e}),this.updateLocalPostData(e)}catch(n){t("error","at pages/index/index.vue:424","点赞操作失败:",n),e.liked=r,e.like_count=s;const i=this.feedItems.findIndex(t=>t.id===e.id);-1!==i&&(this.feedItems[i]={...e}),uni.showToast({title:"操作失败，请重试",icon:"none"})}},updateLocalPostData(e){try{const t=JSON.parse(uni.getStorageSync("posts")||"[]"),r=t.findIndex(t=>t.id===e.id);-1!==r&&(t[r]={...e},uni.setStorageSync("posts",JSON.stringify(t)))}catch(r){t("error","at pages/index/index.vue:455","更新本地数据失败:",r)}},commentPost(e){uni.showToast({title:"评论功能开发中",icon:"none"})},sharePost(e){uni.showActionSheet({itemList:["分享到微信","分享到QQ","分享到微博","复制链接"],success:r=>{try{e.share_count=(e.share_count||0)+1;const t=this.feedItems.findIndex(t=>t.id===e.id);-1!==t&&(this.feedItems[t]={...e}),this.updateLocalPostData(e);const s=["微信","QQ","微博","链接"];uni.showToast({title:`已分享到${s[r.tapIndex]}`,icon:"success"})}catch(s){t("error","at pages/index/index.vue:494","分享失败:",s),uni.showToast({title:"分享失败，请重试",icon:"none"})}}})},createPost(){if(!gi.isLoggedIn())return uni.showToast({title:"请先登录",icon:"none"}),void uni.navigateTo({url:"/pages/login/login"});uni.navigateTo({url:"/pages/create-post/create-post"})},navigateToPostDetail(e){uni.navigateTo({url:`/pages/post-detail/post-detail?id=${e.id}`})},showMoreOptions(e){uni.showActionSheet({itemList:["举报","不感兴趣","关注作者"],success:e=>{switch(e.tapIndex){case 0:uni.showToast({title:"举报成功",icon:"success"});break;case 1:uni.showToast({title:"已减少类似内容",icon:"success"});break;case 2:uni.showToast({title:"关注成功",icon:"success"})}}})},previewImage(e){const{images:t,index:r}=e;uni.previewImage({urls:t,current:t[r]})},async navigateToProfile(){try{await gi.isLoggedIn()?uni.switchTab({url:"/pages/profile/profile"}):(uni.showToast({title:"请先登录",icon:"none",duration:1500}),setTimeout(()=>{uni.navigateTo({url:"/pages/login/login"})},1500))}catch(e){t("error","at pages/index/index.vue:593","导航到个人资料页失败:",e),uni.showToast({title:"导航失败，请重试",icon:"none"}),setTimeout(()=>{uni.navigateTo({url:"/pages/login/login"})},1500)}}}},[["render",function(t,r,s,n,i,o){const a=e.resolveComponent("LoadingSpinner"),c=e.resolveComponent("FeedItem"),l=e.resolveComponent("VirtualList");return e.openBlock(),e.createElementBlock("view",{class:"container",onTouchstart:r[4]||(r[4]=(...e)=>o.handleTouchStart&&o.handleTouchStart(...e)),onTouchend:r[5]||(r[5]=(...e)=>o.handleTouchEnd&&o.handleTouchEnd(...e))},[e.createElementVNode("view",{class:"navbar"},[e.createElementVNode("text",{class:"app-name"},"Biu"),e.createElementVNode("view",{class:"search-container"},[e.withDirectives(e.createElementVNode("input",{type:"text",placeholder:"搜索内容或用户",class:"search-input","onUpdate:modelValue":r[0]||(r[0]=e=>i.searchKeyword=e),onConfirm:r[1]||(r[1]=(...e)=>o.handleSearch&&o.handleSearch(...e))},null,544),[[e.vModelText,i.searchKeyword]])]),o.isLoggedIn?(e.openBlock(),e.createElementBlock("view",{key:0,class:"user-avatar",onClick:r[2]||(r[2]=(...e)=>o.navigateToProfile&&o.navigateToProfile(...e))},[e.createElementVNode("image",{src:o.userAvatar||"/static/default-avatar.png",mode:"aspectFit"},null,8,["src"])])):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"tabs-container"},[e.createElementVNode("scroll-view",{"scroll-x":"",class:"tabs-scroll","scroll-left":i.scrollLeft},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.tabs,(t,r)=>(e.openBlock(),e.createElementBlock("view",{key:t.id,class:e.normalizeClass(["tab-item",{active:i.currentTab===r}]),onClick:e=>o.switchTab(r)},e.toDisplayString(t.name),11,["onClick"]))),128))],8,["scroll-left"])]),e.createElementVNode("view",{class:"content"},[!i.loading||i.feedItems&&0!==i.feedItems.length?o.filteredFeedItems&&o.filteredFeedItems.length>0?(e.openBlock(),e.createElementBlock("view",{key:1,class:"feed-list"},[o.filteredFeedItems.length>20?(e.openBlock(),e.createBlock(l,{key:0,items:o.filteredFeedItems,itemHeight:350,containerHeight:600,onScroll:o.handleScroll},{default:e.withCtx(({item:t})=>[e.createVNode(c,{item:t,formatTime:o.formatTime,onClick:o.navigateToPostDetail,onMore:o.showMoreOptions,onLike:o.likePost,onComment:o.commentPost,onShare:o.sharePost,onImagePreview:o.previewImage},null,8,["item","formatTime","onClick","onMore","onLike","onComment","onShare","onImagePreview"])]),_:1},8,["items","onScroll"])):(e.openBlock(!0),e.createElementBlock(e.Fragment,{key:1},e.renderList(o.filteredFeedItems,(t,r)=>(e.openBlock(),e.createBlock(c,{key:t.id,item:t,formatTime:o.formatTime,onClick:o.navigateToPostDetail,onMore:o.showMoreOptions,onLike:o.likePost,onComment:o.commentPost,onShare:o.sharePost,onImagePreview:o.previewImage},null,8,["item","formatTime","onClick","onMore","onLike","onComment","onShare","onImagePreview"]))),128)),i.loading&&i.feedItems&&i.feedItems.length>0?(e.openBlock(),e.createElementBlock("view",{key:2,class:"loading-more"},[e.createVNode(a,{text:"加载更多内容...",size:"small"})])):e.createCommentVNode("",!0)])):(e.openBlock(),e.createElementBlock("view",{key:2,class:"empty-state"},[e.createElementVNode("text",{class:"empty-text"},"暂无内容"),e.createElementVNode("text",{class:"empty-hint"},"快来发布第一条动态吧！")])):(e.openBlock(),e.createElementBlock("view",{key:0,class:"loading-container"},[e.createVNode(a,{text:"正在加载内容..."})]))]),e.createElementVNode("view",{class:"create-btn",onClick:r[3]||(r[3]=(...e)=>o.createPost&&o.createPost(...e))},[e.createElementVNode("text",{class:"create-icon"},"➕")])],32)}],["__scopeId","data-v-3a395b69"]]);const xi=_i({name:"DiscoverPage"},[["render",function(t,r,s,n,i,o){return e.openBlock(),e.createElementBlock("view",{class:"discover-container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("text",{class:"title"},"发现")]),e.createElementVNode("view",{class:"content"},[e.createElementVNode("view",{class:"coming-soon"},[e.createElementVNode("text",{class:"coming-text"},"🔍"),e.createElementVNode("text",{class:"coming-title"},"发现页面"),e.createElementVNode("text",{class:"coming-desc"},"敬请期待更多精彩内容")])])])}],["__scopeId","data-v-7f7040b2"]]);const Ti=_i({name:"ProfilePage",computed:{isLoggedIn:()=>gi.isLoggedIn(),username:()=>gi.getUsername(),userId:()=>gi.getUserId(),userAvatar:()=>yi(gi.getAvatarUrl(),gi.getUsername())},methods:{async handleLogout(){try{await this.authStore.logout(),uni.showToast({title:"已退出登录",icon:"success"}),setTimeout(()=>{uni.reLaunch({url:"/pages/login/login"})},1500)}catch(e){uni.showToast({title:"退出失败",icon:"none"})}},goToLogin(){uni.navigateTo({url:"/pages/login/login"})}}},[["render",function(t,r,s,n,i,o){return e.openBlock(),e.createElementBlock("view",{class:"profile-container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("text",{class:"title"},"我的")]),e.createElementVNode("view",{class:"content"},[o.isLoggedIn?(e.openBlock(),e.createElementBlock("view",{key:0,class:"user-info"},[e.createElementVNode("image",{src:o.userAvatar,class:"avatar",mode:"aspectFit"},null,8,["src"]),e.createElementVNode("text",{class:"username"},e.toDisplayString(o.username),1),e.createElementVNode("text",{class:"user-id"},"ID: "+e.toDisplayString(o.userId),1),e.createElementVNode("button",{class:"logout-btn",onClick:r[0]||(r[0]=(...e)=>o.handleLogout&&o.handleLogout(...e))}," 退出登录 ")])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"login-prompt"},[e.createElementVNode("text",{class:"prompt-text"},"请先登录"),e.createElementVNode("button",{class:"login-btn",onClick:r[1]||(r[1]=(...e)=>o.goToLogin&&o.goToLogin(...e))}," 立即登录 ")]))])])}],["__scopeId","data-v-0a7b78dd"]]);const Ci=_i({name:"SearchPage",data:()=>({searchKeyword:"",searchResults:[]}),methods:{handleSearch(){this.searchKeyword.trim()&&(this.searchResults=[`搜索结果1: ${this.searchKeyword}`,`搜索结果2: ${this.searchKeyword}`,`搜索结果3: ${this.searchKeyword}`])}}},[["render",function(t,r,s,n,i,o){return e.openBlock(),e.createElementBlock("view",{class:"search-container"},[e.createElementVNode("view",{class:"search-header"},[e.withDirectives(e.createElementVNode("input",{type:"text",placeholder:"搜索内容或用户",class:"search-input","onUpdate:modelValue":r[0]||(r[0]=e=>i.searchKeyword=e),onConfirm:r[1]||(r[1]=(...e)=>o.handleSearch&&o.handleSearch(...e))},null,544),[[e.vModelText,i.searchKeyword]]),e.createElementVNode("button",{class:"search-btn",onClick:r[2]||(r[2]=(...e)=>o.handleSearch&&o.handleSearch(...e))},"搜索")]),i.searchResults.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"search-results"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.searchResults,(t,r)=>(e.openBlock(),e.createElementBlock("view",{key:r,class:"result-item"},[e.createElementVNode("text",null,e.toDisplayString(t),1)]))),128))])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"empty-state"},[e.createElementVNode("text",{class:"empty-text"},"请输入关键词搜索")]))])}],["__scopeId","data-v-b5023558"]]);const Ai=_i({name:"LoginPage",data:()=>({formData:{phone:"",password:""},loading:!1,error:""}),computed:{authStore:()=>di(),canSubmit(){return this.formData.phone.trim()&&this.formData.password.trim()&&this.formData.password.length>=6}},methods:{clearError(){this.error=""},async handleLogin(){if(this.canSubmit){this.loading=!0,this.error="";try{const e=this.formData.phone.trim(),t=this.formData.password,r=await this.authStore.login({phone:e,password:t});r&&r.success?(uni.showToast({title:"登录成功",icon:"success",duration:1500}),setTimeout(()=>{this.handleLoginSuccess()},1500)):this.error=(null==r?void 0:r.error)||"手机号或密码错误"}catch(e){this.error="登录失败，请重试"}finally{this.loading=!1}}else this.error="请填写完整的登录信息"},handleLoginSuccess(){uni.switchTab({url:"/pages/index/index"})},goToRegister(){uni.navigateTo({url:"/pages/register/register"})}}},[["render",function(t,r,s,n,i,o){return e.openBlock(),e.createElementBlock("view",{class:"login-container"},[e.createElementVNode("view",{class:"logo-section"},[e.createElementVNode("view",{class:"logo"},[e.createElementVNode("text",{class:"logo-text"},"Biu")]),e.createElementVNode("text",{class:"app-name"},"Biu"),e.createElementVNode("text",{class:"app-slogan"},"连接你我，分享生活")]),e.createElementVNode("view",{class:"form-section"},[e.createElementVNode("view",{class:"form-container"},[e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("view",{class:"input-label"},[e.createElementVNode("text",{class:"label-icon"},"📱"),e.createElementVNode("text",{class:"label-text"},"手机号")]),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":r[0]||(r[0]=e=>i.formData.phone=e),type:"text",placeholder:"请输入手机号",class:"form-input",disabled:i.loading,onInput:r[1]||(r[1]=(...e)=>o.clearError&&o.clearError(...e))},null,40,["disabled"]),[[e.vModelText,i.formData.phone]])]),e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("view",{class:"input-label"},[e.createElementVNode("text",{class:"label-icon"},"🔒"),e.createElementVNode("text",{class:"label-text"},"密码")]),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":r[2]||(r[2]=e=>i.formData.password=e),type:"password",placeholder:"请输入密码",class:"form-input",disabled:i.loading,onInput:r[3]||(r[3]=(...e)=>o.clearError&&o.clearError(...e))},null,40,["disabled"]),[[e.vModelText,i.formData.password]])]),i.error?(e.openBlock(),e.createElementBlock("view",{key:0,class:"error-message"},[e.createElementVNode("text",{class:"error-icon"},"⚠️"),e.createElementVNode("text",{class:"error-text"},e.toDisplayString(i.error),1)])):e.createCommentVNode("",!0),e.createElementVNode("button",{class:e.normalizeClass(["login-btn",{loading:i.loading}]),disabled:i.loading||!o.canSubmit,onClick:r[4]||(r[4]=(...e)=>o.handleLogin&&o.handleLogin(...e))},[i.loading?(e.openBlock(),e.createElementBlock("text",{key:0},"登录中...")):(e.openBlock(),e.createElementBlock("text",{key:1},"登录"))],10,["disabled"]),e.createElementVNode("view",{class:"register-link"},[e.createElementVNode("text",{class:"link-text"},"还没有账号？"),e.createElementVNode("text",{class:"link-btn",onClick:r[5]||(r[5]=(...e)=>o.goToRegister&&o.goToRegister(...e))},"立即注册")])])]),e.createElementVNode("view",{class:"footer-decoration"},[e.createElementVNode("text",{class:"footer-text"},"让生活更有趣")])])}],["__scopeId","data-v-b507259c"]]);const Pi=_i({name:"RegisterPage",data:()=>({formData:{phone:"",password:"",nickname:"",gender:0},loading:!1,error:""}),computed:{authStore:()=>di(),canSubmit(){return this.formData.phone.trim()&&this.formData.password.trim()&&this.formData.nickname.trim()&&this.formData.gender>0&&this.formData.password.length>=6}},methods:{async handleRegister(){if(this.canSubmit){this.loading=!0,this.error="";try{const e=await this.authStore.registerWithPhone(this.formData);e&&e.success?(uni.showToast({title:"注册成功",icon:"success",duration:1500}),setTimeout(()=>{uni.switchTab({url:"/pages/index/index"})},1500)):this.error=(null==e?void 0:e.error)||"注册失败，请重试"}catch(e){this.error="注册失败，请重试"}finally{this.loading=!1}}else this.error="请填写完整的注册信息"},goToLogin(){uni.navigateBack()}}},[["render",function(t,r,s,n,i,o){return e.openBlock(),e.createElementBlock("view",{class:"register-container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("text",{class:"title"},"注册账号"),e.createElementVNode("text",{class:"subtitle"},"加入Biu，开始分享生活")]),e.createElementVNode("view",{class:"form-container"},[e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"label"},"手机号"),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":r[0]||(r[0]=e=>i.formData.phone=e),type:"text",placeholder:"请输入手机号",class:"form-input"},null,512),[[e.vModelText,i.formData.phone]])]),e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"label"},"密码"),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":r[1]||(r[1]=e=>i.formData.password=e),type:"password",placeholder:"请输入密码（至少6位）",class:"form-input"},null,512),[[e.vModelText,i.formData.password]])]),e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"label"},"昵称"),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":r[2]||(r[2]=e=>i.formData.nickname=e),type:"text",placeholder:"请输入昵称",class:"form-input"},null,512),[[e.vModelText,i.formData.nickname]])]),e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"label"},"性别"),e.createElementVNode("view",{class:"gender-options"},[e.createElementVNode("view",{class:e.normalizeClass(["gender-option",{active:1===i.formData.gender}]),onClick:r[3]||(r[3]=e=>i.formData.gender=1)},[e.createElementVNode("text",null,"男")],2),e.createElementVNode("view",{class:e.normalizeClass(["gender-option",{active:2===i.formData.gender}]),onClick:r[4]||(r[4]=e=>i.formData.gender=2)},[e.createElementVNode("text",null,"女")],2)])]),i.error?(e.openBlock(),e.createElementBlock("view",{key:0,class:"error-message"},[e.createElementVNode("text",null,e.toDisplayString(i.error),1)])):e.createCommentVNode("",!0),e.createElementVNode("button",{class:"register-btn",disabled:i.loading||!o.canSubmit,onClick:r[5]||(r[5]=(...e)=>o.handleRegister&&o.handleRegister(...e))},[i.loading?(e.openBlock(),e.createElementBlock("text",{key:0},"注册中...")):(e.openBlock(),e.createElementBlock("text",{key:1},"注册"))],8,["disabled"]),e.createElementVNode("view",{class:"login-link"},[e.createElementVNode("text",{class:"link-text"},"已有账号？"),e.createElementVNode("text",{class:"link-btn",onClick:r[6]||(r[6]=(...e)=>o.goToLogin&&o.goToLogin(...e))},"立即登录")])])])}],["__scopeId","data-v-04eb3c65"]]);const Bi=_i({name:"RegisterTestPage",data:()=>({formData:{phone:"",password:"",nickname:"",gender:0},loading:!1,error:""}),computed:{authStore:()=>di(),canSubmit(){return this.formData.phone.trim()&&this.formData.password.trim()&&this.formData.nickname.trim()&&this.formData.password.length>=6}},methods:{async handleRegister(){if(this.canSubmit){this.loading=!0,this.error="";try{const e=await this.authStore.registerWithPhone(this.formData);e&&e.success?(uni.showToast({title:"注册成功",icon:"success",duration:1500}),setTimeout(()=>{uni.switchTab({url:"/pages/index/index"})},1500)):this.error=(null==e?void 0:e.error)||"注册失败，请重试"}catch(e){this.error="注册失败，请重试"}finally{this.loading=!1}}else this.error="请填写完整的注册信息"},goToLogin(){uni.navigateTo({url:"/pages/login/login"})}}},[["render",function(t,r,s,n,i,o){return e.openBlock(),e.createElementBlock("view",{class:"register-test-container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("text",{class:"title"},"注册流程测试")]),e.createElementVNode("view",{class:"form-container"},[e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"label"},"手机号"),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":r[0]||(r[0]=e=>i.formData.phone=e),type:"text",placeholder:"请输入手机号",class:"form-input"},null,512),[[e.vModelText,i.formData.phone]])]),e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"label"},"密码"),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":r[1]||(r[1]=e=>i.formData.password=e),type:"password",placeholder:"请输入密码（至少6位）",class:"form-input"},null,512),[[e.vModelText,i.formData.password]])]),e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"label"},"昵称"),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":r[2]||(r[2]=e=>i.formData.nickname=e),type:"text",placeholder:"请输入昵称",class:"form-input"},null,512),[[e.vModelText,i.formData.nickname]])]),e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"label"},"性别"),e.createElementVNode("view",{class:"gender-options"},[e.createElementVNode("view",{class:e.normalizeClass(["gender-option",{active:1===i.formData.gender}]),onClick:r[3]||(r[3]=e=>i.formData.gender=1)},[e.createElementVNode("text",null,"男")],2),e.createElementVNode("view",{class:e.normalizeClass(["gender-option",{active:2===i.formData.gender}]),onClick:r[4]||(r[4]=e=>i.formData.gender=2)},[e.createElementVNode("text",null,"女")],2),e.createElementVNode("view",{class:e.normalizeClass(["gender-option",{active:0===i.formData.gender}]),onClick:r[5]||(r[5]=e=>i.formData.gender=0)},[e.createElementVNode("text",null,"保密")],2)])]),i.error?(e.openBlock(),e.createElementBlock("view",{key:0,class:"error-message"},[e.createElementVNode("text",null,e.toDisplayString(i.error),1)])):e.createCommentVNode("",!0),e.createElementVNode("button",{class:"register-btn",disabled:i.loading||!o.canSubmit,onClick:r[6]||(r[6]=(...e)=>o.handleRegister&&o.handleRegister(...e))},[i.loading?(e.openBlock(),e.createElementBlock("text",{key:0},"注册中...")):(e.openBlock(),e.createElementBlock("text",{key:1},"注册"))],8,["disabled"]),e.createElementVNode("view",{class:"login-link"},[e.createElementVNode("text",{class:"link-text"},"已有账号？"),e.createElementVNode("text",{class:"link-btn",onClick:r[7]||(r[7]=(...e)=>o.goToLogin&&o.goToLogin(...e))},"立即登录")])])])}],["__scopeId","data-v-6ee07bae"]]);const Ni=_i({name:"TestRegistrationPage",data:()=>({currentStep:0,steps:["手机号","密码","昵称","性别"],registrationData:{phone:"",password:"",nickname:"",gender:null}}),computed:{authStore:()=>di(),passwordLengthMet(){return this.registrationData.password.length>=6}},methods:{nextStep(){this.currentStep<this.steps.length-1&&this.currentStep++},prevStep(){this.currentStep>0&&this.currentStep--},async submitRegistration(){try{const e=await this.authStore.registerWithPhone(this.registrationData);e&&e.success?(uni.showToast({title:"注册成功",icon:"success",duration:1500}),setTimeout(()=>{uni.switchTab({url:"/pages/index/index"})},1500)):uni.showToast({title:(null==e?void 0:e.error)||"注册失败",icon:"none"})}catch(e){uni.showToast({title:"注册失败，请重试",icon:"none"})}}}},[["render",function(t,r,s,n,i,o){return e.openBlock(),e.createElementBlock("view",{class:"test-registration-container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("text",{class:"title"},"测试注册流程")]),e.createElementVNode("view",{class:"content"},[e.createElementVNode("view",{class:"step-indicator"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.steps,(t,r)=>(e.openBlock(),e.createElementBlock("view",{key:r,class:e.normalizeClass(["step",{active:i.currentStep===r}])},[e.createElementVNode("text",{class:"step-number"},e.toDisplayString(r+1),1),e.createElementVNode("text",{class:"step-name"},e.toDisplayString(t),1)],2))),128))]),e.createElementVNode("view",{class:"form-section"},[0===i.currentStep?(e.openBlock(),e.createElementBlock("view",{key:0,class:"step-content"},[e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"label"},"手机号"),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":r[0]||(r[0]=e=>i.registrationData.phone=e),type:"text",placeholder:"请输入手机号",class:"form-input"},null,512),[[e.vModelText,i.registrationData.phone]])]),e.createElementVNode("button",{class:"next-btn",disabled:!i.registrationData.phone.trim(),onClick:r[1]||(r[1]=(...e)=>o.nextStep&&o.nextStep(...e))}," 下一步 ",8,["disabled"])])):e.createCommentVNode("",!0),1===i.currentStep?(e.openBlock(),e.createElementBlock("view",{key:1,class:"step-content"},[e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"label"},"密码"),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":r[2]||(r[2]=e=>i.registrationData.password=e),type:"password",placeholder:"请输入密码（至少6位）",class:"form-input"},null,512),[[e.vModelText,i.registrationData.password]])]),e.createElementVNode("view",{class:"password-requirements"},[e.createElementVNode("text",{class:e.normalizeClass(["requirement",{met:o.passwordLengthMet}])}," • 至少6位字符 ",2)]),e.createElementVNode("view",{class:"button-group"},[e.createElementVNode("button",{class:"prev-btn",onClick:r[3]||(r[3]=(...e)=>o.prevStep&&o.prevStep(...e))},"上一步"),e.createElementVNode("button",{class:"next-btn",disabled:!o.passwordLengthMet,onClick:r[4]||(r[4]=(...e)=>o.nextStep&&o.nextStep(...e))}," 下一步 ",8,["disabled"])])])):e.createCommentVNode("",!0),2===i.currentStep?(e.openBlock(),e.createElementBlock("view",{key:2,class:"step-content"},[e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"label"},"昵称"),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":r[5]||(r[5]=e=>i.registrationData.nickname=e),type:"text",placeholder:"请输入昵称",class:"form-input"},null,512),[[e.vModelText,i.registrationData.nickname]])]),e.createElementVNode("view",{class:"button-group"},[e.createElementVNode("button",{class:"prev-btn",onClick:r[6]||(r[6]=(...e)=>o.prevStep&&o.prevStep(...e))},"上一步"),e.createElementVNode("button",{class:"next-btn",disabled:!i.registrationData.nickname.trim(),onClick:r[7]||(r[7]=(...e)=>o.nextStep&&o.nextStep(...e))}," 下一步 ",8,["disabled"])])])):e.createCommentVNode("",!0),3===i.currentStep?(e.openBlock(),e.createElementBlock("view",{key:3,class:"step-content"},[e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"label"},"性别"),e.createElementVNode("view",{class:"gender-options"},[e.createElementVNode("view",{class:e.normalizeClass(["gender-option",{active:1===i.registrationData.gender}]),onClick:r[8]||(r[8]=e=>i.registrationData.gender=1)},[e.createElementVNode("text",null,"男")],2),e.createElementVNode("view",{class:e.normalizeClass(["gender-option",{active:2===i.registrationData.gender}]),onClick:r[9]||(r[9]=e=>i.registrationData.gender=2)},[e.createElementVNode("text",null,"女")],2),e.createElementVNode("view",{class:e.normalizeClass(["gender-option",{active:0===i.registrationData.gender}]),onClick:r[10]||(r[10]=e=>i.registrationData.gender=0)},[e.createElementVNode("text",null,"保密")],2)])]),e.createElementVNode("view",{class:"button-group"},[e.createElementVNode("button",{class:"prev-btn",onClick:r[11]||(r[11]=(...e)=>o.prevStep&&o.prevStep(...e))},"上一步"),e.createElementVNode("button",{class:"submit-btn",disabled:null===i.registrationData.gender,onClick:r[12]||(r[12]=(...e)=>o.submitRegistration&&o.submitRegistration(...e))}," 完成注册 ",8,["disabled"])])])):e.createCommentVNode("",!0)])])])}],["__scopeId","data-v-a8845362"]]);const ji=_i({name:"CreatePostPage",data:()=>({postContent:"",imageList:[],publishing:!1}),computed:{authStore:()=>di(),canPublish(){return this.postContent.trim().length>0}},mounted(){this.checkAuth()},methods:{checkAuth(){this.authStore.isLoggedIn||(uni.showToast({title:"请先登录",icon:"none"}),setTimeout(()=>{uni.redirectTo({url:"/pages/login/login"})},1500))},goBack(){uni.navigateBack()},chooseImage(){uni.chooseImage({count:9-this.imageList.length,success:e=>{this.imageList=[...this.imageList,...e.tempFilePaths]}})},removeImage(e){this.imageList.splice(e,1)},async publishPost(){if(this.canPublish&&!this.publishing){if(!this.authStore.isLoggedIn)return uni.showToast({title:"请先登录",icon:"none"}),void setTimeout(()=>{uni.redirectTo({url:"/pages/login/login"})},1500);this.publishing=!0;try{const e={content:this.postContent,images:[...this.imageList],location:null,user_id:this.authStore.userId},r=await(async e=>{var r,s,n;try{const i=_s(di().token),o={content:e.content,media:e.images||[],location:e.location,created_at:(new Date).toISOString(),author_id:e.user_id},{data:a,error:c}=await i.from("posts").insert(o).select("\n        *,\n        users (\n          id,\n          nickname,\n          avatar_url\n        )\n      ").single();return c?(t("error","at services/post.ts:114","发布帖子失败:",c),{success:!1,error:"发布帖子失败: "+c.message}):{success:!0,data:{id:a.id,content:a.content,images:a.media||[],location:a.location,created_at:a.created_at,user_id:a.author_id,username:(null==(r=a.users)?void 0:r.nickname)||"未知用户",avatar_url:yi(null==(s=a.users)?void 0:s.avatar_url,null==(n=a.users)?void 0:n.nickname),like_count:a.likes_count||0,comment_count:a.comments_count||0,share_count:a.shares_count||0,liked:!1}}}catch(i){return t("error","at services/post.ts:136","发布帖子异常:",i),{success:!1,error:"发布帖子异常: "+(i.message||"未知错误")}}})(e);if(!r.success)throw new Error(r.error||"发布失败");uni.showToast({title:"发布成功",icon:"success"}),uni.setStorageSync("refreshFeed",!0),setTimeout(()=>{this.publishing=!1,uni.navigateBack()},1500)}catch(e){t("error","at pages/create-post/create-post.vue:169","发布失败:",e),this.publishing=!1,uni.showToast({title:"发布失败，请重试",icon:"none"})}}}}},[["render",function(t,r,s,n,i,o){return e.openBlock(),e.createElementBlock("view",{class:"create-post-container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("button",{class:"cancel-btn",onClick:r[0]||(r[0]=(...e)=>o.goBack&&o.goBack(...e))},"取消"),e.createElementVNode("text",{class:"title"},"发布动态"),e.createElementVNode("button",{class:"publish-btn",disabled:!o.canPublish||i.publishing,onClick:r[1]||(r[1]=(...e)=>o.publishPost&&o.publishPost(...e))},e.toDisplayString(i.publishing?"发布中...":"发布"),9,["disabled"])]),e.createElementVNode("view",{class:"content-area"},[e.withDirectives(e.createElementVNode("textarea",{"onUpdate:modelValue":r[2]||(r[2]=e=>i.postContent=e),placeholder:"分享你的生活...",class:"content-input",maxlength:"500"},null,512),[[e.vModelText,i.postContent]]),e.createElementVNode("view",{class:"content-info"},[e.createElementVNode("text",{class:"char-count"},e.toDisplayString(i.postContent.length)+"/500",1)]),e.createElementVNode("view",{class:"media-section"},[e.createElementVNode("view",{class:"image-upload"},[e.createElementVNode("text",{class:"section-title"},"添加图片"),i.imageList.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"image-preview"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.imageList,(t,r)=>(e.openBlock(),e.createElementBlock("view",{key:r,class:"image-item"},[e.createElementVNode("image",{src:t,mode:"aspectFill"},null,8,["src"]),e.createElementVNode("text",{class:"remove-image",onClick:e=>o.removeImage(r)},"×",8,["onClick"])]))),128))])):e.createCommentVNode("",!0),e.createElementVNode("button",{class:"upload-btn",onClick:r[3]||(r[3]=(...e)=>o.chooseImage&&o.chooseImage(...e)),disabled:i.imageList.length>=9}," + 添加图片 ",8,["disabled"])])])])])}],["__scopeId","data-v-984b29eb"]]);const Ii=_i({name:"MessagePage"},[["render",function(t,r,s,n,i,o){return e.openBlock(),e.createElementBlock("view",{class:"message-container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("text",{class:"title"},"消息")]),e.createElementVNode("view",{class:"content"},[e.createElementVNode("view",{class:"coming-soon"},[e.createElementVNode("text",{class:"coming-text"},"💬"),e.createElementVNode("text",{class:"coming-title"},"消息功能"),e.createElementVNode("text",{class:"coming-desc"},"敬请期待")])])])}],["__scopeId","data-v-dbf1f087"]]);const Di=_i({name:"PostDetailPage",data:()=>({postId:"",postData:{id:"",content:"",images:[],created_at:"",username:"",avatar_url:"",like_count:0,comment_count:0,share_count:0,liked:!1},commentContent:"",comments:[],touchStartX:0,touchStartY:0,minSwipeDistance:50}),onLoad(e){e.id&&(this.postId=e.id,this.loadPostData(this.postId))},methods:{handleTouchStart(e){const t=e.touches[0];this.touchStartX=t.clientX,this.touchStartY=t.clientY},handleTouchEnd(e){const t=e.changedTouches[0],r=t.clientX-this.touchStartX,s=t.clientY-this.touchStartY;r>this.minSwipeDistance&&Math.abs(s)<.5*Math.abs(r)&&this.navigateToProfile()},navigateToProfile(){uni.switchTab({url:"/pages/profile/profile"})},loadPostData(e){try{const r=uni.getStorageSync("posts");if(r){const s=JSON.parse(r).find(t=>t.id===e);s?(this.postData={...s},t("log","at pages/post-detail/post-detail.vue:167","加载帖子成功:",this.postData)):(t("error","at pages/post-detail/post-detail.vue:169","未找到ID为"+e+"的帖子"),this.showPostNotFoundError())}else t("error","at pages/post-detail/post-detail.vue:173","没有找到存储的帖子数据"),this.showPostNotFoundError()}catch(r){t("error","at pages/post-detail/post-detail.vue:177","加载帖子数据失败:",r),this.showPostNotFoundError()}},showPostNotFoundError(){uni.showToast({title:"帖子不存在",icon:"none"}),setTimeout(()=>{uni.navigateBack()},1500)},formatTime(e){try{const t=new Date(e),r=new Date-t;return r<6e4?"刚刚":r<36e5?`${Math.floor(r/6e4)}分钟前`:r<864e5?`${Math.floor(r/36e5)}小时前`:`${Math.floor(r/864e5)}天前`}catch(t){return"未知时间"}},likePost(){this.postData.liked;const e=this.postData.like_count;this.postData.liked=!this.postData.liked,this.postData.like_count=this.postData.liked?e+1:e-1,this.updatePostData()},updatePostData(){try{const e=uni.getStorageSync("posts");if(e){const t=JSON.parse(e),r=t.findIndex(e=>e.id===this.postId);-1!==r&&(t[r]={...this.postData},uni.setStorageSync("posts",JSON.stringify(t)))}}catch(e){t("error","at pages/post-detail/post-detail.vue:247","更新帖子数据失败:",e)}},commentPost(){},sharePost(){uni.showActionSheet({itemList:["分享到微信","分享到QQ","分享到微博","复制链接"],success:e=>{uni.showToast({title:`已分享到${["微信","QQ","微博","链接"][e.tapIndex]}`,icon:"success"}),this.postData.share_count=(this.postData.share_count||0)+1,this.updatePostData()}})},sendComment(){if(!this.commentContent.trim())return;const e={id:Date.now().toString(),content:this.commentContent,username:"当前用户",avatar_url:"",created_at:(new Date).toISOString()};this.comments.unshift(e),this.commentContent="",this.postData.comment_count=(this.postData.comment_count||0)+1,this.updatePostData(),uni.showToast({title:"评论成功",icon:"success"})},previewImage(e){const{images:t,index:r}=e;uni.previewImage({urls:t,current:t[r]})}}},[["render",function(t,r,s,n,i,o){return e.openBlock(),e.createElementBlock("view",{class:"post-detail-container",onTouchstart:r[5]||(r[5]=(...e)=>o.handleTouchStart&&o.handleTouchStart(...e)),onTouchend:r[6]||(r[6]=(...e)=>o.handleTouchEnd&&o.handleTouchEnd(...e))},[e.createElementVNode("view",{class:"post-content"},[e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("image",{src:i.postData.avatar_url||"/static/default-avatar.png",class:"user-avatar",mode:"aspectFit"},null,8,["src"]),e.createElementVNode("view",{class:"user-details"},[e.createElementVNode("text",{class:"username"},e.toDisplayString(i.postData.username),1),e.createElementVNode("text",{class:"time"},e.toDisplayString(o.formatTime(i.postData.created_at)),1)])]),e.createElementVNode("view",{class:"content-text"},[e.createElementVNode("text",null,e.toDisplayString(i.postData.content),1)]),i.postData.images&&i.postData.images.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"images-container"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.postData.images,(t,r)=>(e.openBlock(),e.createElementBlock("image",{key:r,src:t,class:"content-image",mode:"aspectFill",onClick:e=>o.previewImage({images:i.postData.images,index:r})},null,8,["src","onClick"]))),128))])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"post-actions"},[e.createElementVNode("view",{class:"action-btn",onClick:r[0]||(r[0]=(...e)=>o.likePost&&o.likePost(...e))},[e.createElementVNode("text",{class:e.normalizeClass(["action-icon",{liked:i.postData.liked}])},"❤️",2),e.createElementVNode("text",{class:"action-count"},e.toDisplayString(i.postData.like_count||0),1)]),e.createElementVNode("view",{class:"action-btn",onClick:r[1]||(r[1]=(...e)=>o.commentPost&&o.commentPost(...e))},[e.createElementVNode("text",{class:"action-icon"},"💬"),e.createElementVNode("text",{class:"action-count"},e.toDisplayString(i.postData.comment_count||0),1)]),e.createElementVNode("view",{class:"action-btn",onClick:r[2]||(r[2]=(...e)=>o.sharePost&&o.sharePost(...e))},[e.createElementVNode("text",{class:"action-icon"},"🔗"),e.createElementVNode("text",{class:"action-count"},e.toDisplayString(i.postData.share_count||0),1)])])]),e.createElementVNode("view",{class:"comments-section"},[e.createElementVNode("view",{class:"section-title"},"评论"),e.createElementVNode("view",{class:"comment-input-area"},[e.withDirectives(e.createElementVNode("input",{type:"text",placeholder:"添加评论...",class:"comment-input","onUpdate:modelValue":r[3]||(r[3]=e=>i.commentContent=e)},null,512),[[e.vModelText,i.commentContent]]),e.createElementVNode("button",{class:"send-btn",disabled:!i.commentContent.trim(),onClick:r[4]||(r[4]=(...e)=>o.sendComment&&o.sendComment(...e))}," 发送 ",8,["disabled"])]),e.createElementVNode("view",{class:"comments-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.comments,(t,r)=>(e.openBlock(),e.createElementBlock("view",{key:r,class:"comment-item"},[e.createElementVNode("image",{src:t.avatar_url||"/static/default-avatar.png",class:"comment-avatar",mode:"aspectFit"},null,8,["src"]),e.createElementVNode("view",{class:"comment-content"},[e.createElementVNode("view",{class:"comment-user"},e.toDisplayString(t.username),1),e.createElementVNode("view",{class:"comment-text"},e.toDisplayString(t.content),1),e.createElementVNode("view",{class:"comment-time"},e.toDisplayString(o.formatTime(t.created_at)),1)])]))),128))])])],32)}],["__scopeId","data-v-67f7f087"]]);const Oi=_i({name:"AvatarTestPage",data:()=>({defaultAvatar:"",customAvatar:"",selectedAvatar:""}),mounted(){this.loadAvatars()},methods:{loadAvatars(){this.defaultAvatar=yi("","默认用户"),this.customAvatar=yi("https://via.placeholder.com/150","自定义用户")},chooseAvatar(){uni.chooseImage({count:1,success:e=>{this.selectedAvatar=e.tempFilePaths[0]}})}}},[["render",function(t,r,s,n,i,o){return e.openBlock(),e.createElementBlock("view",{class:"avatar-test-container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("text",{class:"title"},"头像测试")]),e.createElementVNode("view",{class:"content"},[e.createElementVNode("view",{class:"avatar-section"},[e.createElementVNode("text",{class:"section-title"},"默认头像"),e.createElementVNode("view",{class:"avatar-preview"},[e.createElementVNode("image",{src:i.defaultAvatar,class:"avatar-image",mode:"aspectFit"},null,8,["src"]),e.createElementVNode("text",{class:"avatar-label"},"默认头像")])]),e.createElementVNode("view",{class:"avatar-section"},[e.createElementVNode("text",{class:"section-title"},"自定义头像"),e.createElementVNode("view",{class:"avatar-preview"},[e.createElementVNode("image",{src:i.customAvatar,class:"avatar-image",mode:"aspectFit"},null,8,["src"]),e.createElementVNode("text",{class:"avatar-label"},"自定义头像")])]),e.createElementVNode("view",{class:"avatar-section"},[e.createElementVNode("text",{class:"section-title"},"头像上传"),e.createElementVNode("button",{class:"upload-btn",onClick:r[0]||(r[0]=(...e)=>o.chooseAvatar&&o.chooseAvatar(...e))},"选择头像"),i.selectedAvatar?(e.openBlock(),e.createElementBlock("image",{key:0,src:i.selectedAvatar,class:"selected-avatar",mode:"aspectFit"},null,8,["src"])):e.createCommentVNode("",!0)])])])}],["__scopeId","data-v-c95ddcff"]]);__definePage("pages/index/index",Ei),__definePage("pages/discover/discover",xi),__definePage("pages/profile/profile",Ti),__definePage("pages/search/search",Ci),__definePage("pages/login/login",Ai),__definePage("pages/register/register",Pi),__definePage("pages/register/register-test",Bi),__definePage("pages/register/test-registration",Ni),__definePage("pages/create-post/create-post",ji),__definePage("pages/message/message",Ii),__definePage("pages/post-detail/post-detail",Di),__definePage("pages/avatar-test/avatar-test",Oi);const Ri=e.defineComponent({__name:"App",setup:r=>(i(async()=>{t("log","at App.vue:17","App Launch");const e=di();try{await e.validateSession()}catch(r){t("error","at App.vue:24","应用启动时验证会话失败:",r)}}),s(()=>{t("log","at App.vue:30","App Show")}),n(()=>{t("log","at App.vue:35","App Hide")}),o(()=>{di()}),(t,r)=>{const s=e.resolveComponent("router-view");return e.openBlock(),e.createElementBlock("view",{class:"app"},[e.createVNode(s)])})}),Vi=function(){const t=e.effectScope(!0),r=t.run(()=>e.ref({}));let s=[],n=[];const i=e.markRaw({install(e){c(i),i._a=e,e.provide(l,i),e.config.globalProperties.$pinia=i,n.forEach(e=>s.push(e)),n=[]},use(e){return this._a?s.push(e):n.push(e),this},_p:s,_a:null,_e:t,_s:new Map,state:r});return i}();const{app:$i,Vuex:Li,Pinia:Ui}=function(){const t=e.createVueApp(Ri);return t.use(Vi),{app:t}}();uni.Vuex=Li,uni.Pinia=Ui,$i.provide("__globalStyles",__uniConfig.styles),$i._component.mpType="app",$i._component.render=()=>{},$i.mount("#app")}(Vue);
